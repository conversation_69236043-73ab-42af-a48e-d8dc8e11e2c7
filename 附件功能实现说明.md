# 附件功能实现说明

## 功能概述

实现了帖子附件管理功能，包括：
1. 附件在富文本框下方单独展示
2. 图片在富文本框内显示，点击可放大预览
3. 保存时将附件列表传给后端
4. 编辑时加载并显示附件列表
5. 查看详情时返回附件列表

## 前端修改

### 1. TiptapEditor组件 (`blog-ui/src/components/TiptapEditor/index.vue`)

#### 新增功能
- **附件列表展示**：在富文本框下方显示附件列表
- **图片预览**：点击图片可放大预览
- **附件管理**：支持删除附件
- **文件大小格式化**：显示友好的文件大小

#### 主要修改
```javascript
// 数据结构
data() {
  return {
    attachments: [], // 附件列表
    imagePreview: {
      visible: false,
      url: ''
    }
  }
}

// 图片上传 - 点击放大
img.onclick = function(e) {
  e.preventDefault()
  this.showImagePreview(url)
}.bind(this)

// 文件上传 - 添加到附件列表
if (url) {
  const attachment = {
    fileName: file.name,
    fileUrl: url,
    fileSize: file.size,
    uploadTime: new Date().toLocaleString()
  }
  this.attachments.push(attachment)
  this.$emit('attachment-change', this.attachments)
}
```

#### 新增方法
- `showImagePreview(url)` - 显示图片预览
- `closeImagePreview()` - 关闭图片预览
- `removeAttachment(index)` - 删除附件
- `getAttachments()` - 获取附件列表
- `setAttachments(attachments)` - 设置附件列表
- `formatFileSize(bytes)` - 格式化文件大小

### 2. 帖子管理页面 (`blog-ui/src/views/forum/postList/index.vue`)

#### 主要修改
```javascript
// 表单数据添加附件字段
form: {
  // ... 其他字段
  attachments: [], // 附件列表
}

// 监听附件变化
<tiptap-editor 
  ref="tiptapEditor"
  v-model="form.content" 
  :height="300" 
  :upload-url="ossUploadUrl"
  @attachment-change="handleAttachmentChange"/>

// 处理附件变化
handleAttachmentChange(attachments) {
  this.form.attachments = attachments.map(attachment => ({
    fileName: attachment.fileName,
    fileUrl: attachment.fileUrl,
    fileSize: attachment.fileSize,
    fileType: this.getFileExtension(attachment.fileName)
  }));
}
```

## 后端修改

### 1. ForumPost实体类 (`blog-forum/src/main/java/com/blog/forum/domain/ForumPost.java`)

```java
/** 附件列表（非数据库字段） */
private List<ForumPostAttachment> attachments;

public List<ForumPostAttachment> getAttachments() {
    return attachments;
}

public void setAttachments(List<ForumPostAttachment> attachments) {
    this.attachments = attachments;
}
```

### 2. ForumPostService实现类 (`blog-forum/src/main/java/com/blog/forum/service/impl/ForumPostServiceImpl.java`)

#### 新增依赖
```java
@Autowired
private ForumPostAttachmentMapper attachmentMapper;
```

#### 修改insertForumPost方法
```java
@Override
@Transactional(rollbackFor = Exception.class)
public int insertForumPost(ForumPost forumPost) {
    forumPost.setCreateTime(DateUtils.getNowDate());
    int rows = forumPostMapper.insertForumPost(forumPost);
    
    // 处理附件
    if (rows > 0 && forumPost.getAttachments() != null && !forumPost.getAttachments().isEmpty()) {
        for (ForumPostAttachment attachment : forumPost.getAttachments()) {
            attachment.setPostId(forumPost.getPostId());
            attachment.setContentType("1"); // 1表示附件
            attachment.setCreateTime(DateUtils.getNowDate());
        }
        attachmentMapper.batchInsert(forumPost.getAttachments());
    }
    
    return rows;
}
```

#### 修改updateForumPost方法
```java
// 处理附件
if (rows > 0 && forumPost.getAttachments() != null) {
    // 先删除原有附件
    ForumPostAttachment deleteCondition = new ForumPostAttachment();
    deleteCondition.setPostId(forumPost.getPostId());
    deleteCondition.setContentType("1");
    List<ForumPostAttachment> existingAttachments = attachmentMapper.selectForumPostAttachmentList(deleteCondition);
    if (!existingAttachments.isEmpty()) {
        Long[] attachmentIds = existingAttachments.stream()
            .map(ForumPostAttachment::getAttachmentId)
            .toArray(Long[]::new);
        attachmentMapper.deleteForumPostAttachmentByAttachmentIds(attachmentIds);
    }

    // 添加新的附件
    if (!forumPost.getAttachments().isEmpty()) {
        for (ForumPostAttachment attachment : forumPost.getAttachments()) {
            attachment.setPostId(forumPost.getPostId());
            attachment.setContentType("1"); // 1表示附件
            attachment.setCreateTime(DateUtils.getNowDate());
        }
        attachmentMapper.batchInsert(forumPost.getAttachments());
    }
}
```

#### 修改selectForumPostByPostId方法
```java
@Override
public ForumPost selectForumPostByPostId(Long postId) {
    ForumPost post = forumPostMapper.selectForumPostByPostId(postId);
    if (post != null) {
        // 查询帖子关联的标签
        List<Long> tagIds = postTagMapper.selectTagIdsByPostId(postId);
        post.setTagIds(tagIds);
        
        // 查询帖子关联的附件
        ForumPostAttachment attachmentCondition = new ForumPostAttachment();
        attachmentCondition.setPostId(postId);
        attachmentCondition.setContentType("1");
        List<ForumPostAttachment> attachments = attachmentMapper.selectForumPostAttachmentList(attachmentCondition);
        post.setAttachments(attachments);
    }
    return post;
}
```

### 3. ForumPostAttachment映射文件 (`blog-forum/src/main/resources/mapper/forum/ForumPostAttachmentMapper.xml`)

#### 主要修改
- 添加contentType字段映射
- 更新查询语句包含content_type字段
- 修改batchInsert使用传入对象的contentType和createTime
- 添加contentType查询条件支持

```xml
<resultMap type="ForumPostAttachment" id="ForumPostAttachmentResult">
    <!-- ... 其他字段 -->
    <result property="contentType"    column="content_type"    />
    <!-- ... -->
</resultMap>

<sql id="selectForumPostAttachmentVo">
    select attachment_id, post_id, file_name, file_url, file_size, file_type, content_type, create_time from forum_post_attachment
</sql>
```

## 数据库字段说明

### forum_post_attachment表
- `attachment_id` - 附件ID（主键）
- `post_id` - 帖子ID
- `file_name` - 文件名称
- `file_url` - 文件URL
- `file_size` - 文件大小（字节）
- `file_type` - 文件类型（扩展名）
- `content_type` - 内容类型（1=附件，可扩展其他类型）
- `create_time` - 创建时间

## 功能特性

### 1. 图片功能
- ✅ 图片在富文本框内显示
- ✅ 点击图片可放大预览
- ✅ 图片有悬停效果
- ✅ 图片不可缩放（移除了双击缩放功能）

### 2. 附件功能
- ✅ 附件在富文本框下方单独展示
- ✅ 附件显示文件图标、名称、大小、上传时间
- ✅ 附件支持下载和删除
- ✅ 附件有悬停效果

### 3. 数据持久化
- ✅ 新增帖子时保存附件列表
- ✅ 编辑帖子时更新附件列表
- ✅ 查看帖子时返回附件列表
- ✅ 支持事务处理，确保数据一致性

### 4. 用户体验
- ✅ 文件大小友好显示（B、KB、MB、GB）
- ✅ 上传进度显示
- ✅ 错误处理和提示
- ✅ 响应式设计

## 测试建议

### 1. 基础功能测试
- 上传图片，验证在富文本框内显示
- 上传附件，验证在附件列表中显示
- 点击图片，验证预览功能
- 删除附件，验证从列表中移除

### 2. 数据持久化测试
- 新增帖子，验证附件保存到数据库
- 编辑帖子，验证附件更新
- 查看帖子详情，验证附件正确显示

### 3. 边界情况测试
- 上传大文件，验证分片上传
- 上传多个附件，验证批量处理
- 网络异常时的错误处理

## 注意事项

1. **文件类型检测**：前端根据文件扩展名设置fileType
2. **内容类型**：使用contentType="1"标识附件类型
3. **事务处理**：使用@Transactional确保数据一致性
4. **内存管理**：大文件使用分片上传，避免内存溢出
5. **安全考虑**：文件上传需要验证文件类型和大小限制
