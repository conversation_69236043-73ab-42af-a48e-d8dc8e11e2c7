<template>
  <el-config-provider>
    <Navbar />
    <div class="main-content">
      <router-view />
    </div>
  </el-config-provider>
</template>

<script setup lang="ts">
  import Navbar from '@/components/Navbar.vue'
  import axios from '@/utils/request'
  import { useAuthStore } from '@/stores/auth'
  const authStore = useAuthStore()

  const fetchUserInfo = async () => {
    try {
      const userInfo = await axios.get("/api/auth/info", {});
      if (userInfo.code == 200) {
        await authStore.setUserInfo(userInfo)
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error)
    }
  }
  const token = localStorage.getItem('token') || '';
  if (token) {
    fetchUserInfo()

  }
</script>

<style>
  #app {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
      'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .main-content {
    margin-top: 60px;
    min-height: calc(100vh - 60px);
    background: #fff;
  }

  .editor-toolbar {
    width: 768px;
  }

  @media screen and (max-width: 750px) {
    .editor-toolbar {
      width: 100%;
    }
  }
</style>