// src/i18n.ts
import { createI18n } from 'vue-i18n'

// 定义语言资源
const messages = {
  en: {
    message: {
      hello: 'Hello world',
      welcome: 'Welcome {name}'
    }
  },
  zh: {
    message: {
      hello: '你好世界',
      welcome: '欢迎 {name}'
    }
  }
}

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: 'en', // 默认语言
  fallbackLocale: 'en', // 回退语言
  messages
})

export default i18n