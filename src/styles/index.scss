@use './variables.scss' as *;
// @import "@/styles/variables.scss";

// Reset styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: $font-size-base;
  line-height: 1.5;
  color: $text-regular;
  background-color: $background-color-base;
}

#app {
  height: 100%;
}

// Common utility classes
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Responsive container
.container {
  max-width: $breakpoint-xl;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

// Element Plus overrides
.el-card {
  border-radius: $border-radius-base;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  &--primary {
    background-color: $primary-color;
  }
}

// 重置样式
a {
  color: $primary-color;
  text-decoration: none;

  &:hover {
    // color: darken($primary-color, 10%);
  }
}

// 响应式布局
@media (max-width: $breakpoint-md) {
  .container {
    padding: 0 $spacing-sm;
  }
}

// 工具类
.text-primary { color: $text-primary; }
.text-regular { color: $text-regular; }
.text-secondary { color: $text-secondary; }
.text-placeholder { color: $text-placeholder; }

.bg-white { background-color: $background-color-white; }
.bg-light { background-color: $background-color-light; }

.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
.mt-4 { margin-top: $spacing-lg; }
.mt-5 { margin-top: $spacing-xl; }

.mb-1 { margin-bottom: $spacing-xs; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-3 { margin-bottom: $spacing-md; }
.mb-4 { margin-bottom: $spacing-lg; }
.mb-5 { margin-bottom: $spacing-xl; }

.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; } 