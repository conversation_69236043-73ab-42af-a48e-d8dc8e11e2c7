// Colors
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// Text colors
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// Border colors
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// Background colors
$background-color-base: #f5f7fa;
$background-color: #F5F7FA;
$background-color-light: #FAFAFA;
$background-color-white: #FFFFFF;

// Font sizes
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 12px;
$font-size-extra-small: 12px;

// Font weights
$font-weight-primary: 500;
$font-weight-secondary: 400;

// Line heights
$line-height-primary: 24px;
$line-height-secondary: 16px;

// Border radius
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// Breakpoints
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1600px;

// Spacing
$spacing-base: 8px;
$spacing-xs: $spacing-base * 0.5;  // 4px
$spacing-sm: $spacing-base;        // 8px
$spacing-md: $spacing-base * 2;    // 16px
$spacing-lg: $spacing-base * 3;    // 24px
$spacing-xl: $spacing-base * 4;    // 32px
$spacing-xxl: $spacing-base * 6;   // 48px

// Z-index
$z-index-normal: 1;
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070; 