<template>
  <div class="comments-container">
    <h3 class="comments-title">{{$t('comment')}} ({{ comments.length }})</h3>

    <div class="comment-input">
      <div :label="$t('content')" prop="content">
        <div class="editor-container">
          <div class="editor-toolbar" style="width: 100%;">
            <el-button-group>
              <el-button :class="{ 'is-active': editor?.isActive('bold') }"
                @click="editor?.chain().focus().toggleBold().run()">
                B
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('italic') }"
                @click="editor?.chain().focus().toggleItalic().run()">
                <i>I</i>
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('strike') }"
                @click="editor?.chain().focus().toggleStrike().run()">
                <el-icon>
                  <DeleteFilled />
                </el-icon>
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('code') }"
                @click="editor?.chain().focus().toggleCode().run()">
                <el-icon>
                  <CollectionTag />
                </el-icon>
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }"
                @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()">
                H1
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }"
                @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()">
                H2
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }"
                @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()">
                H3
              </el-button>
              <el-button>
                <el-popover class="box-item" title="" content="Bottom Center prompts info" placement="bottom">
                  <template #reference>
                    <img style="width: 20px;" src="../static/smil.png" />
                  </template>
                  <div style="max-height: 300px;overflow: auto;">
                    <span @click="onSelectEmoji(item)" style="padding: 5px;cursor: pointer;" v-for="item,i in emoji"
                      :key="i">{{item}}</span>
                  </div>
                </el-popover>
              </el-button>


            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button :class="{ 'is-active': editor?.isActive('bulletList') }"
                @click="editor?.chain().focus().toggleBulletList().run()">
                <el-icon>
                  <List />
                </el-icon>
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('orderedList') }"
                @click="editor?.chain().focus().toggleOrderedList().run()">
                <el-icon>
                  <Sort />
                </el-icon>
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('blockquote') }"
                @click="editor?.chain().focus().toggleBlockquote().run()">
                <el-icon>
                  <ChatSquare />
                </el-icon>
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button :class="{ 'is-active': editor?.isActive('link') }" @click="addLink">
                <el-icon>
                  <LinkIcon />
                </el-icon>
              </el-button>
              <el-button @click="uploadImage">
                <el-icon>
                  <Picture />
                </el-icon>
              </el-button>
              <el-button @click="uploadFile">
                <el-icon>
                  <Files />
                </el-icon>
              </el-button>

            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button :class="{ 'is-active': editor?.isActive('taskList') }"
                @click="editor?.chain().focus().toggleTaskList().run()">
                <el-icon>
                  <Check />
                </el-icon>
              </el-button>
              <el-button :class="{ 'is-active': editor?.isActive('horizontalRule') }"
                @click="editor?.chain().focus().setHorizontalRule().run()">
                <el-icon>
                  <Minus />
                </el-icon>
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button @click="editor?.chain().focus().undo().run()">
                <el-icon>
                  <Back />
                </el-icon>
              </el-button>
              <el-button @click="editor?.chain().focus().redo().run()">
                <el-icon>
                  <Right />
                </el-icon>
              </el-button>
            </el-button-group>
            <el-select v-model="fontFamilyValue" @change="selectFont" placeholder="fontfamily" style="width: 120px;"
              size="mini" clearable>
              <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>

          <div class="editor-content">
            <div style="max-height: 500px;overflow: auto;">
              <editor-content :editor="editor" />
            </div>
          </div>
        </div>
      </div>
      <el-button type="primary" @click="submitComment(0)" :loading="submitting" class="submit-btn">
        {{$t('comment')}}
      </el-button>
    </div>

    <!-- 评论列表 -->
    <div class="comments-list" v-loading="loading">
      <template v-if="totalComment">
        <div v-for="comment in comments" :key="comment.commentId" class="comment-item">
          <!-- 主评论 -->
          <div class="comment-main">
            <el-avatar v-if="comment.avatar" :src="comment.avatar" :size="40" />
            <img style="width: 40px;height: 40px;border-radius: 50%;" v-else src="../static/thumb.png" />

            <div class="comment-content">
              <div class="comment-header">
                <span class="username">{{ comment.nickname||'***' }}</span>
                <span>
                  <span class="time">{{ formatTime(comment.createTime) }}</span>
                  <span style="margin-left: 10px;cursor: pointer;" size="mini"
                    @click="dialogVisible=true;parentId=comment.commentId" type="primary">
                    <span style="margin-bottom: -2px;position: relative;">
                      <el-icon>
                        <ChatSquare />
                      </el-icon>
                    </span>
                    {{$t('comment')}}</span>
                </span>

              </div>
              <div v-html="comment.content"></div>
              <div class="actions">
                <div style="display: flex;justify-content: space-between;width: 100%;">
                  <!-- <el-button type="primary" link @click="likeComment(comment)">
                    <el-icon>
                      <Star />
                    </el-icon>
                    {{ comment.likeCount }}
                  </el-button> -->
                </div>
              </div>
            </div>
          </div>

          <!-- 回复列表 -->
          <div v-if="comment.replies?.length" class="replies">
            <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
              <el-avatar :src="reply.avatar" :size="32" />
              <div class="reply-content">
                <div class="comment-header">
                  <span class="username">{{ reply.nickname }}</span>
                  <span class="time">{{ formatTime(reply.createTime) }}</span>
                </div>
                <div class="text" v-html="reply.content"></div>
                <!-- <div class="actions">
                  <el-button type="primary" link @click="likeComment(reply)">
                    {{ reply.likeCount }}
                  </el-button> -->
                <!-- </div> -->
              </div>
            </div>
          </div>
        </div>
      </template>
      <el-empty v-else :description="$t('noData')" />
    </div>


    <el-dialog v-model="dialogVisible" :title="$t('comment')" width="800px">

      <div class="comment-input">
        <div :label="$t('content')" prop="content">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('bold') }"
                  @click="editor?.chain().focus().toggleBold().run()">
                  B
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('italic') }"
                  @click="editor?.chain().focus().toggleItalic().run()">
                  <i>I</i>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('strike') }"
                  @click="editor?.chain().focus().toggleStrike().run()">
                  <el-icon>
                    <DeleteFilled />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('code') }"
                  @click="editor?.chain().focus().toggleCode().run()">
                  <el-icon>
                    <CollectionTag />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()">
                  H1
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()">
                  H2
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()">
                  H3
                </el-button>
                <el-button>
                  <el-popover class="box-item" title="" content="Bottom Center prompts info" placement="bottom">
                    <template #reference>
                      <img style="width: 20px;" src="../static/smil.png" />
                    </template>
                    <div style="max-height: 300px;overflow: auto;">
                      <span @click="onSelectEmoji(item)" style="padding: 5px;cursor: pointer;" v-for="item,i in emoji"
                        :key="i">{{item}}</span>
                    </div>
                  </el-popover>
                </el-button>


              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('bulletList') }"
                  @click="editor?.chain().focus().toggleBulletList().run()">
                  <el-icon>
                    <List />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('orderedList') }"
                  @click="editor?.chain().focus().toggleOrderedList().run()">
                  <el-icon>
                    <Sort />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('blockquote') }"
                  @click="editor?.chain().focus().toggleBlockquote().run()">
                  <el-icon>
                    <ChatSquare />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('link') }" @click="addLink">
                  <el-icon>
                    <LinkIcon />
                  </el-icon>
                </el-button>
                <el-button @click="uploadImage">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </el-button>
                <el-button @click="uploadFile">
                  <el-icon>
                    <Files />
                  </el-icon>
                </el-button>

              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('taskList') }"
                  @click="editor?.chain().focus().toggleTaskList().run()">
                  <el-icon>
                    <Check />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('horizontalRule') }"
                  @click="editor?.chain().focus().setHorizontalRule().run()">
                  <el-icon>
                    <Minus />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button @click="editor?.chain().focus().undo().run()">
                  <el-icon>
                    <Back />
                  </el-icon>
                </el-button>
                <el-button @click="editor?.chain().focus().redo().run()">
                  <el-icon>
                    <Right />
                  </el-icon>
                </el-button>
              </el-button-group>
              <el-select v-model="fontFamilyValue" @change="selectFont" placeholder="fontfamily" style="width: 120px;"
                size="mini" clearable>
                <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <div class="editor-content">
              <div style="max-height: 500px;overflow: auto;">
                <editor-content :editor="editor" />
              </div>
            </div>
          </div>
        </div>
        <el-button type="primary" @click="submitComment(parentId)" :loading="submitting" class="submit-btn">
          {{$t('confirm')}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { formatDistanceToNow } from 'date-fns'
  import { zhCN } from 'date-fns/locale'

  import { computed, onBeforeUnmount } from 'vue'
  import { useRouter } from 'vue-router'
  import { Editor, EditorContent } from '@tiptap/vue-3'
  import StarterKit from '@tiptap/starter-kit'
  import Image from '@tiptap/extension-image'
  import Link from '@tiptap/extension-link'
  import TaskList from '@tiptap/extension-task-list'
  import TaskItem from '@tiptap/extension-task-item'
  import * as ElementPlusIconsVue from '@element-plus/icons-vue'
  import type { FormInstance } from 'element-plus'
  const formRef = ref < FormInstance > 
  const editor = ref < Editor | null > (null)
  import axios from '@/utils/request'
  import emoji from '@/utils/emoji'
  import fontFamily from '@/utils/font-family'
  const totalComment = ref(0)
  const dialogVisible = ref(false)
  const fontFamilyValue = ref('')

  // import { Upload as Thumb } from '@element-plus/icons-vue'
  const {

    Italic,
    DeleteFilled,
    CollectionTag,
    List,
    Sort,
    ChatSquare,
    Back,
    Right,
    Picture,
    Link: LinkIcon,
    Check,
    Minus
  } = ElementPlusIconsVue
  interface Comment {
    contentId: number
    contentType: string
    // avatar: string
    content: string
    // createTime: string
    // likes: number
    // replies?: Comment[]
    parentId: number //父评论ID（0表示一级评论）
  }
  const params = {
    contentId: 0,
    contentType: 0,//内容类型（0帖子 1用户发帖）
    parentId: 0, //父评论ID（0表示一级评论）
    content: '',
  }
  const props = defineProps < {
    postId: number
  } > ()

  const comments = ref < Comment > ([])
  const newComment = ref('')
  const loading = ref(false)
  const submitting = ref(false)
  const commentParams = {
    contentId: props.postId,
    contentType: 0,
    isAsc: 'asc',
    orderByColumn: "",
    pageNum: 1,
    pageSize: 10,
    reasonable: true,
  }
  // 获取评论列表
  const fetchComments = async () => {
    loading.value = true
    try {

      const result = await axios.get('/api/comment/list', commentParams)

      if (result.code === 200) {
        comments.value = result.rows;
        totalComment.value = result.total
      }
    } catch (error) {

    } finally {
      loading.value = false
    }
  }

  // 提交评论
  const submitComment = async (parentId) => {
    if (!editor.value.getHTML().trim()) {
      ElMessage.warning('Please enter your comment')
      return
    }

    submitting.value = true
    try {
      params.content = editor.value.getHTML().trim()
      params.contentId = Number(props.postId)
      params.parentId = parentId || 0
      console.log(params)
      const result = await axios.postBody("/api/comment", params)

      if (result.code == 200) {
        if (params.parentId) {
          dialogVisible.value = false;
        }
        newComment.value = ''
        commentParams.pageNum = 1
        fetchComments();
        ElMessage.success('success')
      }
    } catch (error) {
      console.error('Failed to submit comment:', error)
      // ElMessage.error('failed')
    } finally {
      submitting.value = false
    }
  }

  // 点赞评论
  const likeComment = async (comment: Comment) => {
    try {
      const response = await axios.post("/forum/interaction/like",)
      if (result.code === 200) {
        comment.likes++
      }
    } catch (error) {
      console.error('Failed to like comment:', error)
      ElMessage.error('点赞失败')
    }
  }

  // 格式化时间
  const formatTime = (time: string) => {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  }
  // 在组件卸载前销毁编辑器
  onBeforeUnmount(() => {
    if (editor.value) {
      editor.value.destroy()
    }
  })

  // 选择表情
  const onSelectEmoji = (emoji) => {
    editor.value?.chain().focus().insertContent(`${emoji}`).run()
  }
  // 设置字体
  const selectFont = () => {
    editor.value?.chain().focus().setFontFamily(fontFamilyValue.value).run()

  }
  // 上传文件
  const uploadFile = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'zip/zip'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      console.log("file===", file)
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)
        const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
        if (data.code == 200 && data.url) {
          editor.value?.chain().focus().insertContent(`<a download='${file.name}' target='_blank' href="${data.url}">${file.name}</a>`).run()
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('图片上传失败')
      }
    }
    input.click()
  }
  // 添加链接
  const addLink = () => {
    const url = window.prompt('请输入链接地址')
    if (url) {
      editor.value?.chain().focus().setLink({ href: url }).run()
    }
  }

  // 上传图片
  const uploadImage = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)
        const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
        if (data.code == 200 && data.url) {
          editor.value?.chain().focus().setImage({ src: data.url }).run()
        }
      } catch (error) {
        ElMessage.error('图片上传失败')
      }
    }
    input.click()
  }
  onMounted(() => {
    fetchComments();
    editor.value = new Editor({
      extensions: [
        StarterKit,
        Image.configure({
          inline: true,
          HTMLAttributes: {
            class: 'editor-image'
          }
        }),
        Link.configure({
          HTMLAttributes: {
            class: 'editor-link'
          }
        }),
        TaskList,
        TaskItem
      ],
      content: '',
      onUpdate: ({ editor }) => {
        // form.value.content = editor.getHTML()
      }
    })

  })
</script>

<style scoped lang="scss">
  .comments-container {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .comments-title {
      margin: 0 0 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .comment-input {
      margin-bottom: 60px;

      .submit-btn {
        margin-top: 10px;
        float: right;
      }
    }

    .comments-list {
      min-height: 100px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #eee;

      .comment-item {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }
      }

      .comment-main {
        display: flex;
        gap: 12px;
      }

      .comment-content {
        flex: 1;
      }

      .comment-header {
        margin-bottom: 4px;
        display: flex;
        justify-content: space-between;

        .username {
          font-weight: 500;
          margin-right: 8px;
          color: #333;
        }

        .time {
          color: #999;
          font-size: 12px;
        }
      }

      .text {
        margin: 8px 0;
        line-height: 1.5;
        white-space: pre-wrap;
        color: #333;
      }

      .actions {
        margin-top: 8px;
      }

      .replies {
        margin-top: 12px;
        margin-left: 52px;
        padding: 12px;
        background: #f7f8fa;
        border-radius: 4px;

        .reply-item {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .reply-content {
          flex: 1;
        }
      }
    }
  }

  .editor-container {
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 8px;
    border-bottom: 1px solid $border-color-base;
    background-color: $background-color-light;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      padding: 8px;

      &.is-active {
        color: $primary-color;
        background-color: rgba($primary-color, 0.1);
      }
    }
  }

  .editor-content {
    padding: 16px;
    min-height: 300px;
    background-color: $background-color-white;

    :deep(.ProseMirror) {
      min-height: 300px;
      outline: none;

      >*+* {
        margin-top: 0.75em;
      }

      ul,
      ol {
        padding: 0 1rem;
      }

      h1 {
        font-size: 2em;
      }

      h2 {
        font-size: 1.5em;
      }

      h3 {
        font-size: 1.25em;
      }

      blockquote {
        padding-left: 1rem;
        border-left: 2px solid $border-color-base;
        color: $text-secondary;
      }

      code {
        background-color: $background-color-light;
        color: $text-regular;
        padding: 0.2em 0.4em;
        border-radius: $border-radius-small;
      }

      pre {
        background: $background-color-base;
        color: $text-regular;
        padding: 0.75em 1em;
        border-radius: $border-radius-base;

        code {
          background: none;
          color: inherit;
          padding: 0;
        }
      }

      img {
        max-width: 100%;
        height: auto;
      }

      a {
        color: $primary-color;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      ul[data-type="taskList"] {
        list-style: none;
        padding: 0;

        li {
          display: flex;
          align-items: center;

          >label {
            flex: 0 0 auto;
            margin-right: 0.5rem;
            user-select: none;
          }

          >div {
            flex: 1 1 auto;
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .create-post {
      padding: 16px;
    }

    .editor-toolbar {
      .el-button {
        padding: 6px;
      }
    }
  }

  .editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem 0;
  }

  .editor-link {
    color: $primary-color;
    text-decoration: underline;
  }
</style>