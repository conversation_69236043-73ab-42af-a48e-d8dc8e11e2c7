<template>
    <!-- TODO: Implement favorites list -->
    <div v-if="list.length" class="post-list" v-loading="loading">
        <div @click="handlePostClick(post.contentId)" v-for="post in list" :key="post.id" class="post-item">
            <h3>{{ post.title }}</h3>
            <!-- <p>{{ post.content }}</p> -->
            <div class="post-meta">
                <span>{{ post.createTime }}</span>
                <!-- <div class="post-stats">
                    <el-icon>
                        <Star />
                    </el-icon>
                    <span>{{ post.collectCount }}</span>
                    <el-icon>
                        <ChatRound />
                    </el-icon>
                    <span>{{ post.commentCount }}</span>
                </div> -->
            </div>
        </div>
    </div>
    <el-empty v-else description="暂无收藏" />
    <!-- Pagination -->
    <div class="pagination flex-end">
        <el-pagination v-model:current-page="params.pageNum" v-model:page-size="params.pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted } from 'vue'
    import { ElMessage } from 'element-plus'
    import axios from '../utils/request.ts'
    const loading = ref(false)
    import { useRouter } from 'vue-router'
    const router = useRouter()

    const params = ref({
        pageNum: 1,
        pageSize: 10,
    })
  const total = ref(0)

    interface Comment {
        id: number
        username: string
        avatar: string
        content: string
        createTime: string
        likes: number
        replies?: Comment[]
    }
    const list = ref < Comment > ([]);
    const handleSizeChange = (val: number) => {
        params.pageSize = val
        getList()
    }

    const handleCurrentChange = (val: number) => {
        params.pageNum = val
        getList()
    }
    // Methods
    const handlePostClick = (id: number) => {
        router.push(`/post/${id}`)
    }
    const getList = async () => {
        try {
            const result = await axios.get('/api/interaction/user/collect', params.value)
            if (result.code == 200) {
                list.value = result.rows
            }
        } catch (error) {
            console.error('Failed to fetch comments:', error)
            ElMessage.error('获取失败')
        } finally {
            loading.value = false
        }
    }
    // Lifecycle
    onMounted(() => {
        getList();
    })
</script>
<style scoped>
    .flex-end{
        display: flex;
        justify-content: flex-end;
    }
</style>
<style lang="scss" scoped>
    .profile {
      padding: 24px 0;
    }
  
    .user-card {
      .user-header {
        text-align: center;
        padding: 20px 0;
  
        h2 {
          margin: 16px 0 8px;
          color: $text-primary;
        }
  
        .bio {
          color: $text-secondary;
          font-size: $font-size-small;
        }
      }
  
      .user-stats {
        display: flex;
        justify-content: space-around;
        padding: 16px 0;
  
        .stat-item {
          text-align: center;
  
          h3 {
            color: $text-primary;
            margin: 0 0 4px;
          }
  
          span {
            color: $text-secondary;
            font-size: $font-size-small;
          }
        }
      }
  
      .user-actions {
        text-align: center;
        padding: 16px 0;
      }
    }
  
    .post-list {
      .post-item {
        cursor: pointer;
        padding: 16px 0;
        border-bottom: 1px solid $border-color-lighter;
  
        &:last-child {
          border-bottom: none;
        }
  
        h3 {
          color: $text-primary;
          margin: 0 0 8px;
        }
  
        p {
          color: $text-regular;
          margin: 0 0 8px;
        }
  
        .post-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: $text-secondary;
          font-size: $font-size-small;
  
          .post-stats {
            display: flex;
            align-items: center;
            gap: 16px;
  
            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }
    }
  
    .avatar-uploader {
      :deep(.el-upload) {
        border: 1px dashed var(--el-border-color);
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: var(--el-transition-duration-fast);
  
        &:hover {
          border-color: var(--el-color-primary);
        }
      }
    }
  
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
      line-height: 178px;
    }
  
    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  
    .profile-form {
      max-width: 600px;
      margin: 0 auto;
    }
  
    .form-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
    }
  
    @media (max-width: $breakpoint-md) {
      .profile {
        padding: 16px;
  
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
  </style>