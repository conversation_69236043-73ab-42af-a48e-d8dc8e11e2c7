<template>
  <div class="comments-container">
    <h3 class="comments-title">{{$t('comment')}} ({{ comments.length }})</h3>

    <div class="comment-input">
      <div :label="$t('content')" prop="content" v-loading="loadingEditor">

        <div class="editor-container edits">
          <div class="editor-toolbar" style="width: 100%;">
            <div>
              <div class="bars br2  nbl">
                <div>
                  <el-select v-model="fontFamilyValue" @change="selectFont" :placeholder="$t('font')"
                    style="width: 80px;" class="custom-select" size="mini" clearable>
                    <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-select v-model="fontSizeValue" @change="selectFontSize" placeholder="size"
                    style="width: 50px;font-size: 12px;" size="mini" clearable>
                    <el-option v-for="item in fontSize" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <div>
                  <a title="加粗" :class="{ 'is-active': editor?.isActive('bold') }"
                    @click="editor?.chain().focus().toggleBold().run()" class="bar1"></a>
                  <a title="斜" :class="{ 'is-active': editor?.isActive('italic') }"
                    @click="editor?.chain().focus().toggleItalic().run()" class="bar2"></a>
                  <a title="下划线" :class="{ 'is-active': editor?.isActive('strike') }"
                    @click="editor?.chain().focus().toggleUnderline().run()" class="bar3"></a>

                  <el-popover width="auto" class="box-item" title="" content="Bottom Center prompts info"
                    placement="bottom">
                    <template #reference>
                      <a :class="{ 'is-active': editor?.isActive('bulletList') }" class="bar4"></a>
                    </template>
                    <div style="max-height: 200px;overflow: auto;display: flex;flex-wrap: wrap;">

                      <CorlorList :onChildClick="handleChildClick"></CorlorList>

                    </div>
                  </el-popover>


                  <a :class="{ 'is-active': editor?.isActive('link') }" @click="addLink" class="bar5"></a>
                </div>
              </div>
              <div class="bars br2  ">
                <div>
                  <a @click="setAlign('auto')" title="自动排版" class="bar6"></a>
                  <a @click="setAlign('left')" title="居左" class="bar7"></a>
                  <a @click="setAlign('center')" title="居中" class="bar8"></a>
                  <a @click="setAlign('right')" title="居右" class="bar9"></a>
                </div>
                <div>
                  <a @click="setFloating('left')" title="左浮动" class="bar10"></a>
                  <a @click="setFloating('right')" title="右浮动" class="bar11"></a>
                  <a :class="{ 'is-active': editor?.isActive('orderedList') }"
                    @click="editor?.chain().focus().toggleOrderedList().run()" title="排序的列表" class="bar12"></a>
                  <a :class="{ 'is-active': editor?.isActive('bulletList') }"
                    @click="editor?.chain().focus().toggleBulletList().run()" title="未排序的列表" class="bar13"></a>
                </div>
              </div>
              <div class="bars br2  ">
                <div style="display: flex;">
                  <el-popover width="300px" class="box-item" title="" content="Bottom Center prompts info"
                    placement="bottom">
                    <template #reference>
                      <div style="cursor: pointer;">
                        <a title="图片" class="bar14"></a>
                        <div class="msgs">{{$t('expression')}}</div>
                      </div>
                    </template>
                    <div style="max-height: 300px;overflow: auto;display: flex;flex-wrap: wrap;">
                      <span @click="onSelectEmoji(item)" class="emoji-icon" v-for="item,i in emoji"
                        :key="i">{{item}}</span>
                    </div>
                  </el-popover>

                  <el-popover width="250px" class="box-item" title="" content="Bottom Center prompts info"
                    placement="bottom">
                    <template #reference>
                      <div @click="uploadImage" style="margin: 0 5px;cursor: pointer;">
                        <a title="图片" class="bar15"></a>
                        <div class="msgs">{{$t('image')}}</div>
                      </div>
                    </template>
                    <div>
                      {{$t('upImg')}}<br />
                      {{$t('fileSize')}}:{{$t('lessThan')}} 100MB,<br />
                      {{$t('fileavailable')}}:jpg,jpeg,gif,png,bmp
                    </div>
                  </el-popover>
                  <el-popover width="200px" class="box-item" title="" content="Bottom Center prompts info"
                    placement="bottom">
                    <template #reference>
                      <div @click="uploadFile" style="cursor: pointer;">
                        <a title="附件" class="bar16"></a>
                        <div class="msgs">{{$t('annex')}}</div>
                      </div>
                    </template>
                    <div>
                      {{$t('upFile')}}<br />
                      {{$t('fileSize')}}:{{$t('lessThan')}} 100MB,<br />
                      {{$t('fileavailable')}}:jpg,jpeg,gif,png,bmp

                    </div>
                  </el-popover>
                </div>

              </div>
              <div class="bars br2 nbr">
                <a @click="editor?.chain().focus().undo().run()" class="bar17"></a>
                <br />
                <a @click="editor?.chain().focus().redo().run()" class="bar18"></a>
              </div>
            </div>
          </div>
          <div class="editor-content">
            <div style="max-height: 300px;overflow: auto;">
              <editor-content :editor="editor" />
            </div>
          </div>

        </div>
        <div>
          <div v-for="item,i in params.attachments" :key="i">
            <a :download="item.fileName" target="_blank" :href="item.fileUrl">{{item.fileName}}1</a>
          </div>
        </div>
      </div>
      <el-button type="primary" @click="submitComment(0)" :loading="submitting" class="submit-btn">
        {{$t('comment')}}
      </el-button>
    </div>
    <div id="commentList">
      <CommentsList :postId="postId"></CommentsList>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { formatDistanceToNow } from 'date-fns'
  import { zhCN } from 'date-fns/locale'
  import { emitter } from '@/utils/eventBus'
    import CorlorList from '@/components/corlor-list.vue'

  import { computed, onBeforeUnmount } from 'vue'
  import { useRouter } from 'vue-router'
  import { Editor, EditorContent } from '@tiptap/vue-3'
  import StarterKit from '@tiptap/starter-kit'
  import Image from '@tiptap/extension-image'
  import Link from '@tiptap/extension-link'
  import TaskList from '@tiptap/extension-task-list'
  import TaskItem from '@tiptap/extension-task-item'
  import * as ElementPlusIconsVue from '@element-plus/icons-vue'
  import type { FormInstance } from 'element-plus'
  const formRef = ref < FormInstance > 
  const editor = ref < Editor | null > (null)
  import axios from '@/utils/request'
  import { Underline } from '@tiptap/extension-underline'

  import emoji from '@/utils/emoji'
  import fontFamily from '@/utils/font-family'
  const totalComment = ref(0)
  const dialogVisible = ref(false)
  const fontFamilyValue = ref('')
  import CommentsList from './Comment-list.vue'
  const loadingEditor = ref(false)

  import { Extension } from '@tiptap/core'
  import TextStyle from '@tiptap/extension-text-style'
  import FontFamily from '@tiptap/extension-font-family'
  import Color from '@tiptap/extension-color'
  import TextAlign from '@tiptap/extension-text-align'
  import fontSize from '@/utils/font-size'
  import { FontSize } from '@/utils/FontSize'
  import { FloatComponent } from '@/utils/Floating'

  const {

    Italic,
    DeleteFilled,
    CollectionTag,
    List,
    Sort,
    ChatSquare,
    Back,
    Right,
    Picture,
    Link: LinkIcon,
    Check,
    Minus
  } = ElementPlusIconsVue
  interface Comment {
    contentId: number
    contentType: string
    // avatar: string
    content: string
    // createTime: string
    // likes: number
    // replies?: Comment[]
    parentId: number //父评论ID（0表示一级评论）
  }
  const params = ref({
    contentId: 0,
    contentType: 0,//内容类型（0帖子 1用户发帖）
    parentId: 0, //父评论ID（0表示一级评论）
    content: '',
    attachments: [],

  })
  const props = defineProps < {
    postId: number
  } > ()
  const fontSizeValue = ref('')


  const comments = ref < Comment > ([])
  const newComment = ref('')
  const loading = ref(false)
  const submitting = ref(false)
  const commentParams = {
    contentId: props.postId,
    contentType: 0,
    isAsc: 'asc',
    orderByColumn: "",
    pageNum: 1,
    pageSize: 10,
    reasonable: true,
  }
  // 获取评论列表
  const fetchComments = async () => {
    loading.value = true
    try {

      const result = await axios.get('/api/comment/list', commentParams)

      if (result.code === 200) {
        comments.value = result.rows;
        totalComment.value = result.total
      }
    } catch (error) {

    } finally {
      loading.value = false
    }
  }
  const scrollToElementWithOffset = (elementId: string, offset: number = 0) => {
    const element = document.getElementById(elementId)
    if (element) {
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }
  const handleChildClick = (color) => {
    editor.value?.chain().focus().setColor(color.color).run()

  }

  // 提交评论
  const submitComment = async (parentId) => {
    if (!editor.value.getHTML().trim()) {
      ElMessage.warning('Please enter your comment')
      return
    }

    submitting.value = true
    try {
      params.value.content = editor.value.getHTML().trim()
      params.value.contentId = Number(props.postId)
      params.value.parentId = parentId || 0
      console.log(params)
      const result = await axios.postBody("/api/comment", params.value)

      if (result.code == 200) {
        newComment.value = ''
        commentParams.pageNum = 1
        // fetchComments();
        editor.value.commands.setContent("")
        scrollToElementWithOffset('commentList', 0) // 80px 偏移量用于固定头部
        params.value.attachments=[];
        ElMessage.success('success')
        emitter.emit('getCommentList', 'comments')

      }
    } catch (error) {
      console.error('Failed to submit comment:', error)
      // ElMessage.error('failed')
    } finally {
      submitting.value = false
    }
  }

  // 点赞评论
  const likeComment = async (comment: Comment) => {
    try {
      const response = await axios.post("/forum/interaction/like",)
      if (result.code === 200) {
        comment.likes++
      }
    } catch (error) {
      console.error('Failed to like comment:', error)
      ElMessage.error('failed')
    }
  }

  // 格式化时间
  const formatTime = (time: string) => {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  }
  // 在组件卸载前销毁编辑器
  onBeforeUnmount(() => {
    if (editor.value) {
      editor.value.destroy()
    }
  })
  // 浮动
  const setFloating = (type) => {
    if (!editor.value) return
    // 使用TipTap的正确方式设置浮动
    editor.value.chain().focus().setFloat(type).run()
  }
  // 对齐
  const setAlign = (alignment) => {
    editor.value?.chain().focus().setTextAlign(alignment).run()
  }
  // 选择表情
  const onSelectEmoji = (emoji) => {
    editor.value?.chain().focus().insertContent(`${emoji}`).run()
  }
  // 设置字体
  const selectFont = () => {
    editor.value?.chain().focus().setFontFamily(fontFamilyValue.value).run()
  }
  // 设置字体大小
  const selectFontSize = (size: string) => {
    console.log(size)
    editor.value?.chain()
      .focus()
      .setFontSize(size) // 使用自定义命令
      .run()
  }
  // 上传文件
  const uploadFile = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.zip,.doc,.docx,.xls,.xlsx'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      console.log(file)
      if (!file) return
      if (file.size > 100 * 1024 * 1024) {
        ElMessage.warning('The file cannot exceed 100MB')
        return
      }
      try {

        loadingEditor.value = true

        // 如果文件大于10MB，使用分片上传
        if (file.size > 10 * 1024 * 1024) {
          // 动态导入分片上传工具
          const { uploadFileInChunks } = await import('@/utils/upload-helpers')

          // 创建进度条元素
          const progressContainer = document.createElement('div')
          progressContainer.style.position = 'fixed'
          progressContainer.style.top = '50%'
          progressContainer.style.left = '50%'
          progressContainer.style.transform = 'translate(-50%, -50%)'
          progressContainer.style.padding = '20px'
          progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          progressContainer.style.borderRadius = '5px'
          progressContainer.style.color = 'white'
          progressContainer.style.zIndex = '9999'

          const progressText = document.createElement('div')
          progressText.textContent = '正在上传文件: 0%'
          progressText.style.textAlign = 'center'
          progressText.style.marginBottom = '10px'

          const progressBar = document.createElement('div')
          progressBar.style.width = '200px'
          progressBar.style.height = '10px'
          progressBar.style.backgroundColor = '#444'
          progressBar.style.borderRadius = '5px'
          progressBar.style.overflow = 'hidden'

          const progressInner = document.createElement('div')
          progressInner.style.width = '0%'
          progressInner.style.height = '100%'
          progressInner.style.backgroundColor = '#409EFF'
          progressInner.style.transition = 'width 0.3s'

          progressBar.appendChild(progressInner)
          progressContainer.appendChild(progressText)
          progressContainer.appendChild(progressBar)
          document.body.appendChild(progressContainer)

          try {
            // 分片上传文件，并更新进度
            const url = await uploadFileInChunks(file, 'forum', (progress) => {
              progressInner.style.width = `${progress}%`
              progressText.textContent = `正在上传文件: ${progress}%`
            })

            // 上传成功，插入文件链接
            if (url) {
              // editor.value?.chain().focus().insertContent(`<a download='${file.name}' target='_blank' href="${url}">${file.name}</a>`).run()
              let attachmentss = {
                "fileName": file.name,
                "fileSize": file.size,
                "fileUrl": url,
                "fileType": "1",
              }
              params.value.attachments.push(attachmentss)
            }
          } finally {
            // 移除进度条
            document.body.removeChild(progressContainer)
          }
        } else {
          // 小文件使用普通上传
          const formData = new FormData()
          formData.append('file', file)
          const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
          if (data.code == 200 && data.url) {
            let attachmentss = {
              "fileName": file.name,
              "fileSize": file.size,
              "fileUrl": data.url,
              "fileType": "1",
            }
            params.value.attachments.push(attachmentss)
          }
        }
      } catch (error) {
        console.error('error1:', error)
        ElMessage.error('failed')
      } finally {
        loadingEditor.value = false
      }
    }
    input.click()
  }


  // 添加链接
  const addLink = () => {
    const url = window.prompt('enter url')
    if (url) {
      editor.value?.chain().focus().setLink({ href: url }).run()
    }
  }

  // 上传图片
  const uploadImage = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.jpg,.jpeg,.gif,.png,.bmp'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return
      if (file.size > 100 * 1024 * 1024) {
        ElMessage.warning('The file cannot exceed 100MB')
        return
      }
      try {
        loadingEditor.value = true
        // 如果文件大于10MB，使用分片上传
        if (file.size > 10 * 1024 * 1024) {
          // 动态导入分片上传工具
          const { uploadFileInChunks } = await import('@/utils/upload-helpers')

          // 创建进度条元素
          const progressContainer = document.createElement('div')
          progressContainer.style.position = 'fixed'
          progressContainer.style.top = '50%'
          progressContainer.style.left = '50%'
          progressContainer.style.transform = 'translate(-50%, -50%)'
          progressContainer.style.padding = '20px'
          progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          progressContainer.style.borderRadius = '5px'
          progressContainer.style.color = 'white'
          progressContainer.style.zIndex = '9999'

          const progressText = document.createElement('div')
          progressText.textContent = '正在上传图片: 0%'
          progressText.style.textAlign = 'center'
          progressText.style.marginBottom = '10px'

          const progressBar = document.createElement('div')
          progressBar.style.width = '200px'
          progressBar.style.height = '10px'
          progressBar.style.backgroundColor = '#444'
          progressBar.style.borderRadius = '5px'
          progressBar.style.overflow = 'hidden'

          const progressInner = document.createElement('div')
          progressInner.style.width = '0%'
          progressInner.style.height = '100%'
          progressInner.style.backgroundColor = '#409EFF'
          progressInner.style.transition = 'width 0.3s'

          progressBar.appendChild(progressInner)
          progressContainer.appendChild(progressText)
          progressContainer.appendChild(progressBar)
          document.body.appendChild(progressContainer)

          try {
            // 分片上传文件，并更新进度
            const url = await uploadFileInChunks(file, 'forum', (progress) => {
              progressInner.style.width = `${progress}%`
              progressText.textContent = `正在上传图片: ${progress}%`
            })

            // 上传成功，插入图片
            if (url) {
              editor.value?.chain().focus().setImage({ src: url }).run()
            }
          } finally {
            // 移除进度条
            document.body.removeChild(progressContainer)
          }
        } else {
          // 小文件使用普通上传
          const formData = new FormData()
          formData.append('file', file)
          const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
          if (data.code == 200 && data.url) {
            editor.value?.chain().focus().setImage({ src: data.url }).run()
          }
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('图片上传失败')
      } finally {
        loadingEditor.value = false
      }
    }
    input.click()
  }


  onMounted(() => {
    // fetchComments();
    editor.value = new Editor({
      extensions: [
        StarterKit,
        TextStyle,  // 必须放在 FontFamily 之前
        FontFamily.configure({
          types: ['textStyle'],  // 指定应用到的节点类型
        }),
        Image.configure({
          inline: true,
          HTMLAttributes: {
            class: 'editor-image'
          }
        }),
        Link.configure({
          HTMLAttributes: {
            class: 'editor-link'
          }
        }),
        TaskList,
        TaskItem,
        FontSize.configure({
        }),
        FloatComponent.configure({
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'], // 控制哪些节点可对齐
          alignments: ['left', 'center', 'right'], // 启用哪些对齐方式
          defaultAlignment: 'left', // 默认对齐
        }),
        Color.configure({
          types: ['textStyle'],
        }),
        Underline
      ],
      content: '',
      onUpdate: ({ editor }) => {
        // form.value.content = editor.getHTML()
      }
    })

  })
</script>

<style scoped lang="scss">
  .emoji-icon {
    transition: transform 0.3s ease;
    /* 过渡效果 */
    padding: 5px;
    cursor: pointer;
    font-size: 20px;
    width: 30px;
    height: 30px;
    display: block;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .emoji-icon:hover {
    font-size: 30px;
  }

  /* 确保编辑器容器支持浮动 */
  .editor-content :deep(.ProseMirror) {
    overflow: visible !important;
    min-height: 300px !important;
    /* 重要：确保编辑器容器可以包含浮动元素 */
    display: block !important;
    width: 100% !important;
  }

  /* 编辑器外层容器也需要支持浮动 */
  .editor-content {
    overflow: visible !important;
    display: block !important;
  }

  /* 确保段落等元素可以浮动 */
  .editor-content :deep(p),
  .editor-content :deep(div),
  .editor-content :deep(h1),
  .editor-content :deep(h2),
  .editor-content :deep(h3),
  .editor-content :deep(h4),
  .editor-content :deep(h5),
  .editor-content :deep(h6),
  .editor-content :deep(blockquote),
  .editor-content :deep(ul),
  .editor-content :deep(ol),
  .editor-content :deep(li),
  .editor-content :deep(img) {
    position: relative !important;
  }

  /* 清除浮动 */
  .editor-content :deep(.ProseMirror):after {
    content: "";
    display: table;
    clear: both;
  }

  /* TipTap浮动样式 - 通过data-float属性 */
  .editor-content :deep([data-float="left"]) {
    float: left !important;
    max-width: 50% !important;
    margin: 0 1rem 1rem 0 !important;
    display: block !important;
    position: relative !important;
  }

  .editor-content :deep([data-float="right"]) {
    float: right !important;
    max-width: 50% !important;
    margin: 0 0 1rem 1rem !important;
    display: block !important;
    position: relative !important;
  }

  /* 通过类名强制应用浮动样式 */
  .editor-content :deep(.float-left) {
    float: left !important;
    max-width: 50% !important;
    margin: 0 1rem 1rem 0 !important;
    display: block !important;
    position: relative !important;
  }

  .editor-content :deep(.float-right) {
    float: right !important;
    max-width: 50% !important;
    margin: 0 0 1rem 1rem !important;
    display: block !important;
    position: relative !important;
  }

  /* 最高优先级的浮动样式 */
  .editor-content :deep(p.float-left),
  .editor-content :deep(div.float-left),
  .editor-content :deep(p.float-right),
  .editor-content :deep(div.float-right),
  .editor-content :deep(p[data-float]),
  .editor-content :deep(div[data-float]) {
    position: relative !important;
    z-index: 1 !important;
  }

  .edits :deep(.el-select__wrapper) {
    height: 22px !important;
    font-size: 12px;
    line-height: 22px;
    /* 确保文字垂直居中 */
    min-height: 22px;
    padding: 0 5px !important;
    border-radius: 0 !important;
  }

  .edits :deep(.el-select) {

    height: 22px !important;
  }

  .bars {
    line-height: 1.5;
  }

  .bars a {
    float: left;
    border: 1px solid #F2F2F2;
    background: transparent url('../static/editor.gif') no-repeat 0 0;
    overflow: hidden;
    text-indent: -999px;
    width: 22px;
    height: 22px;
    cursor: pointer;
    margin: 0 2px;
    border: 1px solid #F2F2F2;
  }

  .bars a:hover {
    border-color: #09C;
    background-color: #FFF;
    text-decoration: none;
  }

  .bar1 {
    background-position: 0 0 !important;
  }

  .bar2 {
    background-position: -20px 0 !important;
  }

  .bar3 {
    background-position: -40px 0 !important;
  }

  .bar4 {
    background-position: -60px 0 !important;
  }

  .bar5 {
    background-position: -40px -20px !important;
  }

  .bar6 {
    background-position: -220px -40px !important;
  }

  .bar7 {
    background-position: -80px -20px !important;
  }

  .bar8 {
    background-position: -240px -40px !important;
  }

  .bar9 {
    background-position: -260px -40px !important;
  }

  .bar10 {
    background-position: -100px -60px !important;
  }

  .bar11 {
    background-position: -120px -60px !important;
  }

  .bar12 {
    background-position: -100px -20px !important;
  }

  .msgs {
    clear: both;
    font-size: 11px;
    text-align: center;
    min-width: 37px;
    margin: 0 auto;
  }

  .bar13 {
    background-position: 0px -60px !important;
  }

  .bar14 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -3px -80px !important;
  }

  .bar15 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -43px -80px !important;
  }

  .bar16 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -83px -80px !important;
  }

  .bar17 {
    background-position: -20px -40px !important;
  }

  .bar18 {
    background-position: -40px -40px !important;
  }

  .br2 {
    position: relative;
    float: left;
    border-left: 1px solid #FEFEFE;
    padding: 0 3px;
    border-right: 1px solid #DDD;
    height: 44px;
  }

  .nbr {
    border-right: none;
    padding-right: 0;
  }

  .nbl {
    border-left: none;
    padding-left: 0;
  }

  .comments-container {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .comments-title {
      margin: 0 0 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .comment-input {
      margin-bottom: 60px;

      .submit-btn {
        margin-top: 10px;
        float: right;
      }
    }

    .comments-list {
      min-height: 100px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #eee;

      .comment-item {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }
      }

      .comment-main {
        display: flex;
        gap: 12px;
      }

      .comment-content {
        flex: 1;
      }

      .comment-header {
        margin-bottom: 4px;
        display: flex;
        justify-content: space-between;

        .username {
          font-weight: 500;
          margin-right: 8px;
          color: #333;
        }

        .time {
          color: #999;
          font-size: 12px;
        }
      }

      .text {
        margin: 8px 0;
        line-height: 1.5;
        white-space: pre-wrap;
        color: #333;
      }

      .actions {
        margin-top: 8px;
      }

      .replies {
        margin-top: 12px;
        margin-left: 52px;
        padding: 12px;
        background: #f7f8fa;
        border-radius: 4px;

        .reply-item {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .reply-content {
          flex: 1;
        }
      }
    }
  }

  .editor-container {
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 8px;
    border-bottom: 1px solid $border-color-base;
    background-color: $background-color-light;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      padding: 8px;

      &.is-active {
        color: $primary-color;
        background-color: rgba($primary-color, 0.1);
      }
    }
  }

  .editor-content {
    padding: 16px;
    min-height: 300px;
    background-color: $background-color-white;

    :deep(.ProseMirror) {
      min-height: 300px;
      outline: none;

      >*+* {
        margin-top: 0.75em;
      }

      ul,
      ol {
        padding: 0 1rem;
      }

      h1 {
        font-size: 2em;
      }

      h2 {
        font-size: 1.5em;
      }

      h3 {
        font-size: 1.25em;
      }

      blockquote {
        padding-left: 1rem;
        border-left: 2px solid $border-color-base;
        color: $text-secondary;
      }

      code {
        background-color: $background-color-light;
        color: $text-regular;
        padding: 0.2em 0.4em;
        border-radius: $border-radius-small;
      }

      pre {
        background: $background-color-base;
        color: $text-regular;
        padding: 0.75em 1em;
        border-radius: $border-radius-base;

        code {
          background: none;
          color: inherit;
          padding: 0;
        }
      }

      img {
        max-width: 100%;
        height: auto;
      }

      a {
        color: $primary-color;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      ul[data-type="taskList"] {
        list-style: none;
        padding: 0;

        li {
          display: flex;
          align-items: center;

          >label {
            flex: 0 0 auto;
            margin-right: 0.5rem;
            user-select: none;
          }

          >div {
            flex: 1 1 auto;
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .create-post {
      padding: 16px;
    }

    .editor-toolbar {
      .el-button {
        padding: 6px;
      }
    }
  }

  .editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem 0;
  }

  .editor-link {
    color: $primary-color;
    text-decoration: underline;
  }
</style>