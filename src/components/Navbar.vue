<template>
  <el-header class="navbar">
    <div class="container navbar-container">
      <router-link to="/" class="logo">
        <!-- <el-icon :size="32">
          <Edit />
        </el-icon> -->
        <img style="width: 60px;" src="../static/logo.jpg" />
        <span class="pc">{{$t('title')}}</span>
      </router-link>

      <div class="flex">
        <div class="nav-links" @click="toHome">
          <router-link to="/home" replace class="nav-link">{{$t('home')}}</router-link>
        </div>
        <!-- <div class="nav-links">
          <router-link to="/mySpeak" class="nav-link">我要说</router-link>
        </div> -->
        <div class="nav-right">
          <template v-if="authStore.isAuthenticated">
            <el-button type="primary" :icon="Edit" @click="router.push('/create-post')">{{$t('post')}}</el-button>

            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar v-if="authStore.user?.avatar" :size="32" :src="authStore.user?.avatar" />
                <img style="width: 32px;height: 32px;border-radius: 50%;" v-else :size="32" src="../static/thumb.png" />
                <span class="username">{{ authStore.user?.nickname }}</span>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">{{$t('personal')}}</el-dropdown-item>
                  <el-dropdown-item command="settings">{{$t('setting')}}</el-dropdown-item>
                  <el-dropdown-item divided command="logout">{{$t('logOut')}}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button @click="router.push('/auth/login')">{{$t('login')}}</el-button>
            <el-button type="primary" @click="router.push('/auth/register')">{{$t('sigin')}}</el-button>
          </template>
          <el-popover class="box-item" title="" content="Bottom Center prompts info" placement="bottom">
            <template #reference>
              <div style="cursor: pointer;">{{currentLang === 'zh'?'中文':'English'}}
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </div>
            </template>
            <div class="lang-switcher">
              <div class="navs" @click="changeLang('zh')" :class="{ active: currentLang === 'zh' }">
                中文
              </div>
              <div class="navs" @click="changeLang('en')" :class="{ active: currentLang === 'en' }">
                English
              </div>
            </div>
          </el-popover>

        </div>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { Edit } from '@element-plus/icons-vue'
  import { ElMessageBox } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import axios from '@/utils/request'
  import { ElMessage } from 'element-plus'
  import { emitter } from '@/utils/eventBus'

  const router = useRouter()
  const authStore = useAuthStore()
  import { useI18n } from 'vue-i18n'
  import { ref, onMounted } from 'vue'

  const { locale } = useI18n()
  const currentLang = ref(locale.value)

  const changeLang = (lang: 'zh' | 'en') => {
    locale.value = lang
    currentLang.value = lang
    localStorage.setItem('lang', lang) // 保存到本地存储
    location.reload();
  }

  const toHome = () => {
    emitter.emit('to-home', { username: 'home' })
  }
  // 初始化时从本地存储读取
  onMounted(() => {
    const savedLang = localStorage.getItem('lang') || 'en'
    if (savedLang) {
      locale.value = savedLang
      currentLang.value = savedLang
    }
  })

  const { t } = useI18n()
  const handleCommand = async (command: string) => {

    switch (command) {
      case 'profile':
        router.push('/profile')
        break
      case 'settings':
        router.push('/profile?tab=settings')
        break
      case 'logout':
        try {
          await ElMessageBox.confirm(t('outmsg'), t('tips'), {
            confirmButtonText: t('confirms'),
            cancelButtonText: t('cancel'),
            type: 'warning'
          })
          let datas = await axios.post('/api/auth/logout',
            {}
          );
          if (datas.code == 200) {
            console.log("datas", datas)

            ElMessage.success(datas.msg)

            authStore.logout()
            router.push('/home')
          } else {
            ElMessage.error(datas.msg)
            authStore.logout()
            // ElMessage.console.error(datas.msg);
          }

        } catch {
          // User cancelled
        }
        break
    }
  }
</script>

<style lang="scss" scoped>
  .navs {
    text-align: center;
    padding: 10px;
    cursor: pointer;
  }

  .navs:hover {
    color: #42b983;
  }

  .lang-switcher button {
    margin: 0 5px;
    padding: 5px 10px;
    cursor: pointer;
  }

  .lang-switcher .active {
    /* background-color: #42b983; */
    color: #42b983;
  }

  .flex {
    display: flex;
  }

  .navbar {
    background-color: $background-color-white;
    border-bottom: 1px solid $border-color-light;
    padding: 0;
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }

  .navbar-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: $text-primary;
    font-size: $font-size-large;
    font-weight: bold;
    gap: 8px;
  }

  .nav-links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;

    .nav-link {
      height: 100%;
      padding: 0 10px;
      color: $text-regular;
      text-decoration: none;
      font-size: $font-size-medium;
      display: flex;
      align-items: center;
      justify-content: center;

      a {
        height: 100%;

      }

      &:hover,
      &.router-link-active {
        color: $primary-color;
      }
    }
  }

  .nav-right {
    margin-left: 30px;
    display: flex;
    align-items: center;
    gap: 16px;

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      .username {
        color: $text-primary;
        font-size: $font-size-base;
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .nav-links {
      display: none;
    }

    .nav-right {
      .username {
        display: none;
      }
    }
  }
  @media (max-width:751px) {
    .pc{
      display: none;
    }
  }
</style>