<template>
    <div>
        <span class="cor-nav" style="background-color: Black" @click="setEditorTip('Black')" title="黑色"></span>
        <span class="cor-nav" style="background-color: Sienna" @click="setEditorTip('Sienna')" title="赭色"></span>
        <span class="cor-nav" style="background-color: DarkOliveGreen" @click="setEditorTip('DarkOliveGreen')"
            title="暗橄榄绿色"></span>
        <span class="cor-nav" style="background-color: DarkGreen" @click="setEditorTip('DarkGreen')" title="暗绿色"></span>
        <span class="cor-nav" style="background-color: DarkSlateBlue" @click="setEditorTip('DarkSlateBlue')"
            title="暗灰蓝色"></span>
        <span class="cor-nav" style="background-color: Navy" @click="setEditorTip('Navy')" title="海军色"></span>
        <span class="cor-nav" style="background-color: Indigo" @click="setEditorTip('Indigo')" title="靛青色"></span>
        <span class="cor-nav" style="background-color: DarkSlateGray" @click="setEditorTip('DarkSlateGray')"
            title="墨绿色"></span><br>
        <span class="cor-nav" style="background-color: DarkRed" @click="setEditorTip('DarkRed')" title="暗红色"></span>
        <span class="cor-nav" style="background-color: DarkOrange" @click="setEditorTip('DarkOrange')"
            title="暗桔黄色"></span>
        <span class="cor-nav" style="background-color: Olive" @click="setEditorTip('Olive')" title="橄榄色"></span>
        <span class="cor-nav" style="background-color: Green" @click="setEditorTip('Green')" title="绿色"></span>
        <span class="cor-nav" style="background-color: Teal" @click="setEditorTip('Teal')" title="水鸭色"></span>
        <span class="cor-nav" style="background-color: Blue" @click="setEditorTip('Blue')" title="蓝色"></span>
        <span class="cor-nav" style="background-color: SlateGray" @click="setEditorTip('SlateGray')" title="灰石色"></span>
        <span class="cor-nav" style="background-color: DimGray" @click="setEditorTip('DimGray')" title="暗灰色"></span><br>
        <span class="cor-nav" style="background-color: Red" @click="setEditorTip('Red')" title="红色"></span>
        <span class="cor-nav" style="background-color: SandyBrown" @click="setEditorTip('SandyBrown')"
            title="沙褐色"></span>
        <span class="cor-nav" style="background-color: YellowGreen" @click="setEditorTip('YellowGreen')"
            title="黄绿色"></span>
        <span class="cor-nav" style="background-color: SeaGreen" @click="setEditorTip('SeaGreen')" title="海绿色"></span>
        <span class="cor-nav" style="background-color: MediumTurquoise" @click="setEditorTip('MediumTurquoise')"
            title="间绿宝石"></span>
        <span class="cor-nav" style="background-color: RoyalBlue" @click="setEditorTip('RoyalBlue')" title="皇家蓝"></span>
        <span class="cor-nav" style="background-color: Purple" @click="setEditorTip('Purple')" title="紫色"></span>
        <span class="cor-nav" style="background-color: Gray" @click="setEditorTip('Gray')" title="灰色"></span><br>
        <span class="cor-nav" style="background-color: Magenta" @click="setEditorTip('Magenta')" title="红紫色"></span>
        <span class="cor-nav" style="background-color: Orange" @click="setEditorTip('Orange')" title="橙色"></span>
        <span class="cor-nav" style="background-color: Yellow" @click="setEditorTip('Yellow')" title="黄色"></span>
        <span class="cor-nav" style="background-color: Lime" @click="setEditorTip('Lime')" title="酸橙色"></span>
        <span class="cor-nav" style="background-color: Cyan" @click="setEditorTip('Cyan')" title="青色"></span>
        <span class="cor-nav" style="background-color: DeepSkyBlue" @click="setEditorTip('DeepSkyBlue')"
            title="深天蓝色"></span>
        <span class="cor-nav" style="background-color: DarkOrchid" @click="setEditorTip('DarkOrchid')"
            title="暗紫色"></span>
        <span class="cor-nav" style="background-color: Silver" @click="setEditorTip('Silver')" title="银色"></span><br>
        <span class="cor-nav" style="background-color: Pink" @click="setEditorTip('Pink')" title="粉色"></span>
        <span class="cor-nav" style="background-color: Wheat" @click="setEditorTip('Wheat')" title="浅黄色"></span>
        <span class="cor-nav" style="background-color: LemonChiffon" @click="setEditorTip('LemonChiffon')"
            title="柠檬绸色"></span>
        <span class="cor-nav" style="background-color: PaleGreen" @click="setEditorTip('PaleGreen')" title="苍绿色"></span>
        <span class="cor-nav" style="background-color: PaleTurquoise" @click="setEditorTip('PaleTurquoise')"
            title="苍宝石绿"></span>
        <span class="cor-nav" style="background-color: LightBlue" @click="setEditorTip('LightBlue')" title="亮蓝色"></span>
        <span class="cor-nav" style="background-color: Plum" @click="setEditorTip('Plum')" title="洋李色"></span>
        <span class="cor-nav" style="background-color: White" @click="setEditorTip('White')" title="白色"></span>
    </div>
</template>
<script setup lang="ts">
    import { ref, onMounted } from 'vue'

    const props = defineProps({
        onChildClick: {}
    })
    const setEditorTip = (color) => {
        console.log(color)
         props.onChildClick({
            color:color
        })
    }

</script>
<style scoped>
    .cor-nav {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin: 2px;
        cursor: pointer;
    }
</style>