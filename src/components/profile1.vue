<template>
    <div class="home">
  
      <div class="container">
        <div class="filter-bar">
          <el-select size="large" @change="fetchPosts()" style="width: 150px;" v-model="params.categoryId" :placeholder="$t('category')" clearable>
            <el-option v-for="item in categories" :key="item.categoryId" :label="item.categoryName"
              :value="item.categoryId" />
          </el-select>
  
          <el-radio-group v-model="params.sortType" @change="fetchPosts()" size="large">
            <el-radio-button label="latest">{{$t('new')}}</el-radio-button>
            <el-radio-button label="hot">{{$t('hot')}}</el-radio-button>
          </el-radio-group>
  
          <el-input v-model="params.keyword" placeholder="search" :prefix-icon="Search" clearable @change="handleSearch" />
        </div>
  
        <!-- Post List -->
        <div class="post-list">
          <div v-for="post in posts" :key="post.postId" class="post-item" @click="handlePostClick(post.postId)">
            <div class="post-header">
              <!-- <div class="author-info">
                <el-avatar :src="post.author.avatar" />
                <span class="author-name">{{ post.author.name }}</span>
              </div> -->
              <h2 class="post-title">{{ post.title }}</h2>
              <span class="post-time">{{ post.createTime }}</span>
  
            </div>
  
            <div class="post-content">{{post.summary}}</div>
  
            <div class="post-footer">
              <div class="post-tags">
                <el-tag v-for="tag in post.tags" :key="tag" size="small" class="tag">
                  {{ tag.tagName }}
                </el-tag>
              </div>
              <div class="post-actions">
                <el-button :icon="Star" circle />
                <span class="count">{{ post.collectCount }}</span>
                <!-- <el-button :icon="ChatRound" circle /> -->
                 <el-button circle >
               <img style="width: 15px;" src="@/static/zan1.png" />
              </el-button>
                <span class="count">{{ post.likeCount }}</span>
                <el-button @click.stop="toEdit(post.postId)" :icon="Edit" circle />
                <!-- <el-icon><Edit /></el-icon> -->
              </div>
            </div>
          </div>
        </div>
  
        <!-- Pagination -->
        <div class="pagination">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
  
      <!-- Float Button -->
      <el-button class="float-btn" type="primary" circle :icon="Plus" @click="handleCreate" />
    </div>
  </template>
  
  <script setup lang="ts">
    import { ref, onMounted } from 'vue'
    import { useRouter } from 'vue-router'
    import { Star, ChatRound, Share, Plus, Search,Edit } from '@element-plus/icons-vue'
    import type { Post } from '@/types'
    import { ElMessage } from 'element-plus'
    import axios from '@/utils/request'
    const router = useRouter()
  
    // Data
    const posts = ref < Post[] > ([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const category = ref('')
    const sortBy = ref('latest')
    const searchQuery = ref('')
  
    const categories = ref([])
    const banners = []
    const params = ref({
      "categoryId": '',
      "sort": "",
      "keyword": "",
      "postStatus": "",
      "isTop": "",
      "pageNum": 1,
      "pageSize": 10,
      "sortType": "latest"
    })
    const getCates = async () => {
  
      const cates = await axios.get('/api/post/category/list', {})
      if (cates.code == 200) {
        categories.value = cates.data;
        console.log("cates-categories--1111", categories)
  
      }
    }
    const getBanner = async () => {
      try {
  
        const datas = await axios.get('/api/post/carousels', {});
        console.log("datas===", datas)
        if (banners.code == 200) {
          banners = datas.data
        }
      } catch {
  
      }
  
    }
    // Methods
    const getTab = async () => {
  
      try {
  
        const tabs = await axios.get('/api/post/tags', {});
        if (tabs.code == 200) {
          tabs = datas.data
        }
      } catch {
  
      }
    }
    const fetchPosts = async () => {
      try {
        const data = await axios.get('/api/user_post/list', params.value)
        if (data.code === 200) {
          posts.value = data.rows
          total.value = data.total
        } else {
        }
      } catch (error) {
        console.error('Failed to fetch posts:', error)
      }
    }
  
    const handleSearch = () => {
      fetchPosts()
    }
  
    const handleSizeChange = (val: number) => {
      pageSize.value = val
      fetchPosts()
    }
  
    const handleCurrentChange = (val: number) => {
      currentPage.value = val
      fetchPosts()
    }
  
    const handleCreate = () => {
      router.push('/create-post')
    }
    const toEdit = (id: number) => {
      router.push(`/create-edit/${id}`)
    }
    const handlePostClick = (id: number) => {
      router.push(`/post/${id}`)
    }
    
    // Lifecycle
    onMounted(() => {
      getCates();
      getBanner();
      fetchPosts()
    })
  </script>
  
  <style lang="scss" scoped>
    .home {
      background: #fff;
      padding-bottom: 80px;
    }
  
    .banner {
      margin-bottom: 24px;
  
      .banner-item {
        height: 100%;
        background-color: $primary-color;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 24px;
      }
    }
  
    .filter-bar {
      padding-top: 20px;
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      flex-wrap: wrap;
  
      .el-input {
        max-width: 300px;
      }
    }
  
    .post-list {
      border-top: 2px solid #eee;
      display: flex;
      flex-direction: column;
      padding: 0 0 0px 0;
      gap: 16px;
    }
  
    .post-item {
      border-bottom: 1px solid #eee;
      cursor: pointer;
      transition: transform 0.2s ease;
      padding: 10px 0;
  
      &:hover {
        transform: translateY(-2px);
      }
  
      .post-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        /* margin-bottom: 16px; */
  
        .author-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }
  
        .post-time {
          color: $text-secondary;
          font-size: $font-size-small;
        }
      }
  
      .post-title {
        font-size: $font-size-large;
        margin-bottom: 12px;
        color: $text-primary;
      }
  
      .post-content {
        color: $text-regular;
        margin-bottom: 16px;
        line-height: 1.6;
        max-height: 200px;
        overflow: hidden;
        position: relative;
  
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 40px;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
        }
  
        :deep(h1),
        :deep(h2),
        :deep(h3),
        :deep(h4),
        :deep(h5),
        :deep(h6) {
          margin: 12px 0 8px;
          color: $text-primary;
          font-weight: 600;
        }
  
        :deep(p) {
          margin: 8px 0;
        }
  
        :deep(ul),
        :deep(ol) {
          padding-left: 24px;
          margin: 8px 0;
        }
  
        :deep(li) {
          margin: 4px 0;
        }
  
        :deep(blockquote) {
          margin: 16px 0;
          padding: 8px 16px;
          border-left: 4px solid $border-color-base;
          background-color: $background-color-base;
          color: $text-secondary;
        }
  
        :deep(pre) {
          margin: 16px 0;
          padding: 16px;
          background-color: $background-color-base;
          border-radius: 4px;
          overflow-x: auto;
  
          code {
            font-family: monospace;
            white-space: pre;
            color: $text-primary;
          }
        }
  
        :deep(code) {
          background-color: $background-color-base;
          padding: 2px 4px;
          border-radius: 4px;
          font-family: monospace;
          font-size: 0.9em;
        }
      }
  
      .post-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
  
        .post-tags {
          display: flex;
          gap: 8px;
        }
  
        .post-actions {
          display: flex;
          align-items: center;
          gap: 8px;
  
          .count {
            color: $text-secondary;
            font-size: $font-size-small;
          }
        }
      }
    }
  
    .pagination {
      margin-top: 24px;
      display: flex;
      justify-content: center;
    }
  
    .float-btn {
      position: fixed;
      right: 40px;
      bottom: 40px;
      width: 60px;
      height: 60px;
    }
  
    @media (max-width: $breakpoint-sm) {
      .filter-bar {
        padding-top: 20px;
  
        .el-input {
          width: 100%;
          max-width: none;
        }
      }
  
      .post-item {
        .post-footer {
          flex-direction: column;
          gap: 12px;
          align-items: flex-start;
        }
      }
    }
  </style>