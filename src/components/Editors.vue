<template>
  <div class="create-post">
    <div class="container">
      <h1>{{$t('posts.postMessage')}}</h1>
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="handleSubmit">
        <el-form-item :label="$t('posts.title')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('posts.title')" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('posts.description')" prop="summary">
          <el-input v-model="form.summary" :placeholder="$t('posts.description')" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('posts.catogory')" prop="category">
          <el-select v-model="form.categoryId" :placeholder="$t('posts.catogory')">
            <el-option v-for="item in categories" :key="item.categoryId" :label="item.categoryName"
              :value="item.categoryId" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('posts.label')" prop="tags">
          <el-select v-model="form.tagIds" multiple filterable default-first-option :placeholder="$t('posts.label')"
            :max-collapse-tags="2">
            <el-option v-for="tag in tabs" :key="tag" :label="tag.tagName" :value="tag.tagId" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('posts.content')" prop="content" v-loading="loadingEditor">
          <div class="editor-container edits">
            <div class="editor-toolbar">
              <div>
                <div class="bars br2  nbl">
                  <div>
                    <el-select v-model="fontFamilyValue" @change="selectFont" :placeholder="$t('font')"
                      style="width: 80px;" class="custom-select" size="mini" clearable>
                      <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-select v-model="fontSizeValue" @change="selectFontSize" placeholder="size"
                      style="width: 50px;font-size: 12px;" size="mini" clearable>
                      <el-option v-for="item in fontSize" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </div>
                  <div>
                    <a :class="{ 'is-active': editor?.isActive('bold') }"
                      @click="editor?.chain().focus().toggleBold().run()" class="bar1"></a>
                    <a :class="{ 'is-active': editor?.isActive('italic') }"
                      @click="editor?.chain().focus().toggleItalic().run()" class="bar2"></a>
                    <a :class="{ 'is-active': editor?.isActive('strike') }"
                      @click="editor?.chain().focus().toggleStrike().run()" class="bar3"></a>

                    <el-popover width="auto" class="box-item" title="" content="Bottom Center prompts info"
                      placement="bottom">
                      <template #reference>
                        <a :class="{ 'is-active': editor?.isActive('bulletList') }" class="bar4"></a>
                      </template>
                      <div style="max-height: 200px;overflow: auto;display: flex;flex-wrap: wrap;">

                        <CorlorList :onChildClick="handleChildClick"></CorlorList>

                      </div>
                    </el-popover>


                    <a :class="{ 'is-active': editor?.isActive('link') }" @click="addLink" class="bar5"></a>
                  </div>
                </div>
                <div class="bars br2  ">
                  <div>
                    <a @click="setAlign('auto')" title="自动排版" class="bar6"></a>
                    <a @click="setAlign('left')" title="居左" class="bar7"></a>
                    <a @click="setAlign('center')" title="居中" class="bar8"></a>
                    <a @click="setAlign('right')" title="居右" class="bar9"></a>
                  </div>
                  <div>
                    <a @click="setFloating('left')" title="左浮动" class="bar10"></a>
                    <a @click="setFloating('right')" title="右浮动" class="bar11"></a>
                    <a :class="{ 'is-active': editor?.isActive('orderedList') }"
                      @click="editor?.chain().focus().toggleOrderedList().run()" title="排序的列表" class="bar12"></a>
                    <a :class="{ 'is-active': editor?.isActive('bulletList') }"
                      @click="editor?.chain().focus().toggleBulletList().run()" title="未排序的列表" class="bar13"></a>
                  </div>
                </div>
                <div class="bars br2  ">
                  <div style="display: flex;">
                    <el-popover width="300px" class="box-item" title="" content="Bottom Center prompts info"
                      placement="bottom">
                      <template #reference>
                        <div style="cursor: pointer;">
                          <a title="图片" class="bar14"></a>
                          <div class="msgs">{{$t('expression')}}</div>
                        </div>
                      </template>
                      <div style="max-height: 300px;overflow: auto;display: flex;flex-wrap: wrap;">
                        <span @click="onSelectEmoji(item)" class="emoji-icon" v-for="item,i in emoji"
                          :key="i">{{item}}</span>
                      </div>
                    </el-popover>

                    <el-popover width="250px" class="box-item" title="" content="Bottom Center prompts info"
                      placement="bottom">
                      <template #reference>
                        <div @click="uploadImage" style="margin: 0 5px;cursor: pointer;">
                          <a title="图片" class="bar15"></a>
                          <div class="msgs">{{$t('image')}}</div>
                        </div>
                      </template>
                      <div>
                        {{$t('upImg')}}<br />
                        {{$t('fileSize')}}:{{$t('lessThan')}} 100MB,<br />
                        {{$t('fileavailable')}}:jpg,jpeg,gif,png,bmp
                      </div>
                    </el-popover>
                    <el-popover width="200px" class="box-item" title="" content="Bottom Center prompts info"
                      placement="bottom">
                      <template #reference>
                        <div @click="uploadFile" style="cursor: pointer;">
                          <a title="附件" class="bar16"></a>
                          <div class="msgs">{{$t('annex')}}</div>
                        </div>
                      </template>
                      <div>
                        {{$t('upFile')}}<br />
                        {{$t('fileSize')}}:{{$t('lessThan')}} 100MB,<br />
                        {{$t('fileavailable')}}:jpg,jpeg,gif,png,bmp

                      </div>
                    </el-popover>
                  </div>

                </div>
                <div class="bars br2 nbr">
                  <a @click="editor?.chain().focus().redo().run()" class="bar17"></a>
                  <br />
                  <a @click="editor?.chain().focus().undo().run()" class="bar18"></a>
                </div>
            </div>
            </div>
            <div class="editor-content">
              <div style="max-height: 500px;overflow: auto;">
                <editor-content :editor="editor" />
              </div>
            </div>

          </div>
        </el-form-item>
        <div>
          <div v-for="item,i in form.attachments" :key="i">
            <a :download="item.fileName" target="_blank" :href="item.fileUrl">{{item.fileName}}1</a>
          </div>
        </div>
        <div class="form-footer">
          <el-button @click="handleCancel">{{$t('cancel')}}</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{$t('confirm')}}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">

  import { ref, computed, onBeforeUnmount, onMounted } from 'vue'
  import CorlorList from './corlor-list.vue'
  import { useRouter } from 'vue-router'
  import { Editor, EditorContent } from '@tiptap/vue-3'
  import StarterKit from '@tiptap/starter-kit'
  import Image from '@tiptap/extension-image'
  import Link from '@tiptap/extension-link'
  import TaskList from '@tiptap/extension-task-list'
  import TaskItem from '@tiptap/extension-task-item'
  import { Extension } from '@tiptap/core'
  import TextStyle from '@tiptap/extension-text-style'
  import FontFamily from '@tiptap/extension-font-family'
  import Color from '@tiptap/extension-color'
  import TextAlign from '@tiptap/extension-text-align'
  import { ElMessage } from 'element-plus'
  import * as ElementPlusIconsVue from '@element-plus/icons-vue'
  import type { FormInstance } from 'element-plus'


  import axios from '@/utils/request'
  import emoji from '@/utils/emoji'
  import fontFamily from '@/utils/font-family'
  import fontSize from '@/utils/font-size'
  import { FontSize } from '@/utils/FontSize'
  import { FloatComponent } from '@/utils/Floating'
  const router = useRouter()
  const formRef = ref < FormInstance > ()
  const loading = ref(false)
  const loadingEditor = ref(false)


  const fontFamilyValue = ref('')
  const fontSizeValue = ref('')

  import { useI18n } from 'vue-i18n'
  const { t } = useI18n()
  const editor = ref < Editor | null > (null)
  const {
    Bold,
    Italic,
    DeleteFilled,
    List,
    Sort,
    ChatSquare,
    Back,
    Right,
    Picture,
    Files,
    Link: LinkIcon,
    Check,
    Minus
  } = ElementPlusIconsVue



  const handleChildClick = (color) => {
    editor.value?.chain().focus().setColor(color.color).run()

  }
  // 在组件挂载后初始化编辑器
  onMounted(() => {
    getCates();
    getTab();
    editor.value = new Editor({

      extensions: [
        StarterKit,
        TextStyle,  // 必须放在 FontFamily 之前
        FontFamily.configure({
          types: ['textStyle'],  // 指定应用到的节点类型
        }),
        Image.configure({
          inline: true,
          HTMLAttributes: {
            class: 'editor-image'
          }
        }),
        Link.configure({
          HTMLAttributes: {
            class: 'editor-link'
          }
        }),
        TaskList,
        TaskItem,
        FontSize.configure({
        }),
        FloatComponent.configure({
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'], // 控制哪些节点可对齐
          alignments: ['left', 'center', 'right'], // 启用哪些对齐方式
          defaultAlignment: 'left', // 默认对齐
        }),
        Color.configure({
          types: ['textStyle'],
        })
      ],
      content: form.value.content,
      onUpdate: ({ editor }) => {
        form.value.content = editor.getHTML()
      }
    })
  })

  // 在组件卸载前销毁编辑器
  onBeforeUnmount(() => {
    if (editor.value) {
      editor.value.destroy()
    }
  })
  const getTab = async () => {

    try {
      const tab = await axios.get('/api/post/tags', {});
      if (tab.code == 200) {
        tabs.value = tab.data
      }
    } catch {

    }
  }
  const form = ref({
    title: '',
    categoryId: '',
    tagIds: [],
    content: '',
    coverImage: '',
    attachments: [],
    summary: '',
  })

  const rules = {
    title: [
      { required: true, message: t('pmsg1'), trigger: 'blur' },
      { min: 2, max: 100, message: t('pmsg2'), trigger: 'blur' }
    ],
    categoryId: [
      { required: true, message: t('pmsg3'), trigger: 'change' }
    ],
    tagIds: [
      { required: true, message: 'select tag', trigger: 'change' },
      {
        validator: (_: any, value: string[]) => {
          if (value.length > 3) {
            return Promise.reject('max 3 tag')
          }
          return Promise.resolve()
        },
        trigger: 'change'
      }
    ],
    content: [
      {
        validator: (_: any, value: string) => {
          if (!editor.value?.getText().trim()) {
            return Promise.reject('请输入内容')
          }
          return Promise.resolve()
        },
        trigger: 'blur'
      }
    ]
  }

  const categories = ref([])
  const tabs = ref([])



  const handleSubmit = async () => {
    if (!formRef.value || !editor.value) return
    const postData = {
      ...form.value,
      content: editor.value.getHTML()
    }
    console.log(postData)
    try {
      loading.value = true
      await formRef.value.validate()

      const postData = {
        ...form.value,
        content: editor.value.getHTML()
      }
      const ret = await axios.postBody('/api/user_post/submit', postData)
      if (ret.code == 200) {
        ElMessage.success(ret.msg)
        setTimeout(d => {
          router.push({
            path: '/home'
          })
        }, 2000)
      } else {
        ElMessage.Error(ret.msg)

      }

    } catch (error) {
      console.error('Failed to create post:', error)
      ElMessage.error(error instanceof Error ? error.message : '发布失败，请重试')
    } finally {
      loading.value = false
    }
  }

  const handleCancel = () => {
    router.back()
  }
  const getCates = async () => {
    const cates = await axios.get('/api/post/category/list', {})

    if (cates.code == 200) {
      categories.value = cates.data
    }
  }
  // 添加链接
  const addLink = () => {
    const url = window.prompt('enter url')
    if (url) {
      editor.value?.chain().focus().setLink({ href: url }).run()
    }
  }
  // 浮动
  const setFloating = (type) => {
    editor.value?.chain()
      .focus()
      .setFloat(type)
      .run()
  }
  // 对齐
  const setAlign = (alignment) => {
    editor.value?.chain().focus().setTextAlign(alignment).run()
  }
  // 上传图片
  const uploadImage = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.jpg,.jpeg,.gif,.png,.bmp'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return
      if (file.size > 100 * 1024 * 1024) {
        ElMessage.warning('The file cannot exceed 100MB')
        return
      }
      try {
        loadingEditor.value = true
        // 如果文件大于10MB，使用分片上传
        if (file.size > 10 * 1024 * 1024) {
          // 动态导入分片上传工具
          const { uploadFileInChunks } = await import('@/utils/upload-helpers')

          // 创建进度条元素
          const progressContainer = document.createElement('div')
          progressContainer.style.position = 'fixed'
          progressContainer.style.top = '50%'
          progressContainer.style.left = '50%'
          progressContainer.style.transform = 'translate(-50%, -50%)'
          progressContainer.style.padding = '20px'
          progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          progressContainer.style.borderRadius = '5px'
          progressContainer.style.color = 'white'
          progressContainer.style.zIndex = '9999'

          const progressText = document.createElement('div')
          progressText.textContent = '正在上传图片: 0%'
          progressText.style.textAlign = 'center'
          progressText.style.marginBottom = '10px'

          const progressBar = document.createElement('div')
          progressBar.style.width = '200px'
          progressBar.style.height = '10px'
          progressBar.style.backgroundColor = '#444'
          progressBar.style.borderRadius = '5px'
          progressBar.style.overflow = 'hidden'

          const progressInner = document.createElement('div')
          progressInner.style.width = '0%'
          progressInner.style.height = '100%'
          progressInner.style.backgroundColor = '#409EFF'
          progressInner.style.transition = 'width 0.3s'

          progressBar.appendChild(progressInner)
          progressContainer.appendChild(progressText)
          progressContainer.appendChild(progressBar)
          document.body.appendChild(progressContainer)

          try {
            // 分片上传文件，并更新进度
            const url = await uploadFileInChunks(file, 'forum', (progress) => {
              progressInner.style.width = `${progress}%`
              progressText.textContent = `正在上传图片: ${progress}%`
            })

            // 上传成功，插入图片
            if (url) {
              editor.value?.chain().focus().setImage({ src: url }).run()
            }
          } finally {
            // 移除进度条
            document.body.removeChild(progressContainer)
          }
        } else {
          // 小文件使用普通上传
          const formData = new FormData()
          formData.append('file', file)
          const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
          if (data.code == 200 && data.url) {
            editor.value?.chain().focus().setImage({ src: data.url }).run()
          }
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('图片上传失败')
      } finally {
        loadingEditor.value = false
      }
    }
    input.click()
  }
  // 选择表情
  const onSelectEmoji = (emoji) => {
    editor.value?.chain().focus().insertContent(`${emoji}`).run()
  }
  // 设置字体
  const selectFont = () => {
    editor.value?.chain().focus().setFontFamily(fontFamilyValue.value).run()
  }
  // 设置字体大小
  const selectFontSize = (size: string) => {
    console.log(size)
    editor.value?.chain()
      .focus()
      .setFontSize(size) // 使用自定义命令
      .run()
  }
  // 上传文件
  const uploadFile = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.zip,.doc,.docx,.xls,.xlsx'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      console.log(file)
      if (!file) return
      if (file.size > 100 * 1024 * 1024) {
        ElMessage.warning('The file cannot exceed 100MB')
        return
      }
      try {

        loadingEditor.value = true

        // 如果文件大于10MB，使用分片上传
        if (file.size > 10 * 1024 * 1024) {
          // 动态导入分片上传工具
          const { uploadFileInChunks } = await import('@/utils/upload-helpers')

          // 创建进度条元素
          const progressContainer = document.createElement('div')
          progressContainer.style.position = 'fixed'
          progressContainer.style.top = '50%'
          progressContainer.style.left = '50%'
          progressContainer.style.transform = 'translate(-50%, -50%)'
          progressContainer.style.padding = '20px'
          progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
          progressContainer.style.borderRadius = '5px'
          progressContainer.style.color = 'white'
          progressContainer.style.zIndex = '9999'

          const progressText = document.createElement('div')
          progressText.textContent = '正在上传文件: 0%'
          progressText.style.textAlign = 'center'
          progressText.style.marginBottom = '10px'

          const progressBar = document.createElement('div')
          progressBar.style.width = '200px'
          progressBar.style.height = '10px'
          progressBar.style.backgroundColor = '#444'
          progressBar.style.borderRadius = '5px'
          progressBar.style.overflow = 'hidden'

          const progressInner = document.createElement('div')
          progressInner.style.width = '0%'
          progressInner.style.height = '100%'
          progressInner.style.backgroundColor = '#409EFF'
          progressInner.style.transition = 'width 0.3s'

          progressBar.appendChild(progressInner)
          progressContainer.appendChild(progressText)
          progressContainer.appendChild(progressBar)
          document.body.appendChild(progressContainer)

          try {
            // 分片上传文件，并更新进度
            const url = await uploadFileInChunks(file, 'forum', (progress) => {
              progressInner.style.width = `${progress}%`
              progressText.textContent = `正在上传文件: ${progress}%`
            })

            // 上传成功，插入文件链接
            if (url) {
              // editor.value?.chain().focus().insertContent(`<a download='${file.name}' target='_blank' href="${url}">${file.name}</a>`).run()
              let attachmentss = {
                "fileName": file.name,
                "fileSize": file.size,
                "fileUrl": url,
                "fileType": "1",
              }
              form.value.attachments.push(attachmentss)
            }
          } finally {
            // 移除进度条
            document.body.removeChild(progressContainer)
          }
        } else {
          // 小文件使用普通上传
          const formData = new FormData()
          formData.append('file', file)
          const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
          if (data.code == 200 && data.url) {
            // editor.value?.chain().focus().insertContent(`<a download='${file.name}' target='_blank' href="${data.url}">${file.name}</a>`).run()
            let attachmentss = {
              "fileName": file.name,
              "fileSize": file.size,
              "fileUrl": data.url,
              "fileType": "1",
            }
            form.value.attachments.push(attachmentss)
          }
        }
      } catch (error) {
        console.error('error1:', error)
        ElMessage.error('文件上传失败')
      } finally {
        loadingEditor.value = false
      }
    }
    input.click()
  }
</script>

<style lang="scss" scoped>
  /* 浮动组件基础样式 */
  .float-component {
    border: 1px dashed #ccc;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }

  /* 浮动状态样式 */
  .float-component.is-floating {
    max-width: 50%;
  }

  .float-component[style*="float: left"] {
    margin-right: 1.5rem;
  }

  .float-component[style*="float: right"] {
    margin-left: 1.5rem;
  }

  /* 选中状态 */
  .ProseMirror-selectednode .float-component {
    border-color: #000;
    background-color: #f0f0f0;
  }


  .emoji-icon {
    transition: transform 0.3s ease;
    /* 过渡效果 */
    padding: 5px;
    cursor: pointer;
    font-size: 20px;
    width: 30px;
    height: 30px;
    display: block;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .emoji-icon:hover {
    font-size: 30px;
  }

  /* 清除浮动 */
  .ProseMirror>* {
    overflow: hidden;
  }

  /* 浮动元素样式 */
  [style*="float: left"],
  [style*="float: right"] {
    width: 50%;
    /* 控制浮动元素宽度 */
    margin: 0 1em 1em 0;
    /* 左浮动右边距 */
  }

  [style*="float: right"] {
    margin: 0 0 1em 1em;
    /* 右浮动左边距 */
  }

  .edits :deep(.el-select__wrapper) {
    height: 22px !important;
    font-size: 12px;
    line-height: 22px;
    /* 确保文字垂直居中 */
    min-height: 22px;
    padding: 0 5px !important;
    border-radius: 0 !important;
  }

  .edits :deep(.el-select) {

    height: 22px !important;
  }

  .bars {
    line-height: 1.5;
  }

  .bars a {
    float: left;
    border: 1px solid #F2F2F2;
    background: transparent url('../static/editor.gif') no-repeat 0 0;
    overflow: hidden;
    text-indent: -999px;
    width: 22px;
    height: 22px;
    cursor: pointer;
    margin: 0 2px;
    border: 1px solid #F2F2F2;
  }

  .bars a:hover {
    border-color: #09C;
    background-color: #FFF;
    text-decoration: none;
  }

  .bar1 {
    background-position: 0 0 !important;
  }

  .bar2 {
    background-position: -20px 0 !important;
  }

  .bar3 {
    background-position: -40px 0 !important;
  }

  .bar4 {
    background-position: -60px 0 !important;
  }

  .bar5 {
    background-position: -40px -20px !important;
  }

  .bar6 {
    background-position: -220px -40px !important;
  }

  .bar7 {
    background-position: -80px -20px !important;
  }

  .bar8 {
    background-position: -240px -40px !important;
  }

  .bar9 {
    background-position: -260px -40px !important;
  }

  .bar10 {
    background-position: -100px -60px !important;
  }

  .bar11 {
    background-position: -100px -60px !important;
  }

  .bar12 {
    background-position: -100px -20px !important;
  }

  .msgs {
    clear: both;
    font-size: 11px;
    text-align: center;
    width: 37px;
    margin: 0 auto;
  }

  .bar13 {
    background-position: 0px -60px !important;
  }

  .bar14 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -3px -80px !important;
  }

  .bar15 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -43px -80px !important;
  }

  .bar16 {
    padding-top: 27px;
    width: 35px !important;
    height: 15px;
    background-position: -83px -80px !important;
  }

  .bar17 {
    background-position: -20px -40px !important;
  }

  .bar18 {
    background-position: -40px -40px !important;
  }

  .br2 {
    position: relative;
    float: left;
    border-left: 1px solid #FEFEFE;
    padding: 0 3px;
    border-right: 1px solid #DDD;
    height: 44px;
  }

  .nbr {
    border-right: none;
    padding-right: 0;
  }

  .nbl {
    border-left: none;
    padding-left: 0;
  }

  .create-post {
    padding: 24px 0;

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 16px;
    }

    h1 {
      margin-bottom: 24px;
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
    }
  }

  .editor-container {
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 4px;
    border-bottom: 1px solid #DDD;
    background: #F2F2F2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      padding: 8px;

      &.is-active {
        color: $primary-color;
        background-color: rgba($primary-color, 0.1);
      }
    }
  }

  .editor-content {
    padding: 16px;
    min-height: 300px;
    background-color: $background-color-white;

    :deep(.ProseMirror) {
      min-height: 300px;
      outline: none;

      >*+* {
        margin-top: 0.75em;
      }

      ul,
      ol {
        padding: 0 1rem;
      }

      h1 {
        font-size: 2em;
      }

      h2 {
        font-size: 1.5em;
      }

      h3 {
        font-size: 1.25em;
      }

      blockquote {
        padding-left: 1rem;
        border-left: 2px solid $border-color-base;
        color: $text-secondary;
      }

      code {
        background-color: $background-color-light;
        color: $text-regular;
        padding: 0.2em 0.4em;
        border-radius: $border-radius-small;
      }

      pre {
        background: $background-color-base;
        color: $text-regular;
        padding: 0.75em 1em;
        border-radius: $border-radius-base;

        code {
          background: none;
          color: inherit;
          padding: 0;
        }
      }

      img {
        max-width: 100%;
        height: auto;
      }

      a {
        color: $primary-color;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      ul[data-type="taskList"] {
        list-style: none;
        padding: 0;

        li {
          display: flex;
          align-items: center;

          >label {
            flex: 0 0 auto;
            margin-right: 0.5rem;
            user-select: none;
          }

          >div {
            flex: 1 1 auto;
          }
        }
      }
    }
  }

  .form-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  @media (max-width: $breakpoint-sm) {
    .create-post {
      padding: 16px;
    }

    .editor-toolbar {
      .el-button {
        padding: 6px;
      }
    }
  }

  .editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem 0;
  }

  .editor-link {
    color: $primary-color;
    text-decoration: underline;
  }
</style>