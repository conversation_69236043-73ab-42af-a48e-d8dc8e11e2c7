import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import './styles/index.scss'
import App from './App.vue'
import router from './router'
import i18n from './lang/index'
import './font/font.css'


 
// 在开发环境中导入模拟数据
if (import.meta.env.DEV) {
  import('./mocks').then(() => {
    // console.log('Mock data initialized')
  }).catch(error => {
    console.error('Failed to initialize mock data:', error)
  })
}

const app = createApp(App)
// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
// app.use(VueEmojiPicker)

app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(i18n)

app.mount('#app') 