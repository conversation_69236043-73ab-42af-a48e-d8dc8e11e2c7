// 保存原始的 fetch 函数
const originalFetch = window.fetch

interface Post {
  id: number
  title: string
  content: string
  author: {
    id: number
    name: string
    avatar: string
  }
  category: string
  tags: string[]
  likes: number
  comments: number
  views: number
  createTime: string
  isTop: boolean
  isHot: boolean
  isEssence: boolean
}

interface Comment {
  id: number
  username: string
  avatar: string
  content: string
  createTime: string
  likes: number
  replies?: Comment[]
}

// 生成模拟帖子数据
const generateMockPosts = (count: number): Post[] => {
  const categories = ['技术', '生活', '问答', '分享', '讨论']
  const tags = {
    '技术': ['Vue', 'React', 'TypeScript', 'Node.js', 'Python', '前端', '后端', '数据库', '算法', '面试'],
    '生活': ['美食', '旅行', '读书', '电影', '音乐', '摄影', '运动', '游戏'],
    '问答': ['求助', '建议', '经验', '疑问', '讨论'],
    '分享': ['经验分享', '资源分享', '工具分享'],
    '讨论': ['技术讨论', '生活话题', '职场讨论']
  }

  const generateContent = (category: string, index: number) => {
    switch (category) {
      case '技术':
        return `
          <h2>技术分享</h2>
          <p>这是第 ${index} 篇技术文章，主要讨论了以下几个方面：</p>
          <ul>
            <li>前端框架的选择与比较</li>
            <li>性能优化的最佳实践</li>
            <li>代码质量与可维护性</li>
          </ul>
          <pre><code>const example = () => {
  console.log('Hello World');
}</code></pre>
          <p>更多技术细节请查看全文...</p>
        `
      case '生活':
        return `
          <h2>生活随笔</h2>
          <p>今天想分享一下关于${['美食', '旅行', '读书', '摄影'][Math.floor(Math.random() * 4)]}的一些心得：</p>
          <blockquote>
            生活中处处都有值得记录的美好时刻...
          </blockquote>
          <p>这是第 ${index} 篇生活随笔，希望能带给大家一些生活的灵感。</p>
        `
      case '问答':
        return `
          <h2>问题解答</h2>
          <p>这是第 ${index} 个问题的详细解答：</p>
          <ol>
            <li>问题描述</li>
            <li>解决方案</li>
            <li>注意事项</li>
          </ol>
          <p>如果还有疑问，欢迎在评论区讨论。</p>
        `
      case '分享':
        return `
          <h2>资源分享</h2>
          <p>这是第 ${index} 篇分享，包含了以下资源：</p>
          <ul>
            <li>实用工具推荐</li>
            <li>学习资源整理</li>
            <li>经验心得总结</li>
          </ul>
          <p>希望这些资源对大家有帮助！</p>
        `
      default:
        return `
          <h2>讨论话题</h2>
          <p>这是第 ${index} 个讨论话题，让我们一起来探讨：</p>
          <blockquote>
            有趣的观点和想法...
          </blockquote>
          <p>欢迎在评论区发表你的看法！</p>
        `
    }
  }

  return Array.from({ length: count }, (_, i) => {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const categoryTags = tags[category as keyof typeof tags]
    const tagCount = Math.min(3, Math.floor(Math.random() * categoryTags.length) + 1)
    const selectedTags = Array.from({ length: tagCount }, () => 
      categoryTags[Math.floor(Math.random() * categoryTags.length)]
    )

    return {
      id: i + 1,
      title: `${category}相关文章 ${i + 1}`,
      content: generateContent(category, i + 1),
      author: {
        id: 1,
        name: 'Test User',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      category,
      tags: selectedTags,
      likes: Math.floor(Math.random() * 100),
      comments: Math.floor(Math.random() * 50),
      views: Math.floor(Math.random() * 1000),
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      isTop: Math.random() > 0.8,
      isHot: Math.random() > 0.9,
      isEssence: Math.random() > 0.85
    }
  })
}

// 初始化模拟数据
const mockPosts: Post[] = generateMockPosts(50)

// 模拟评论数据
const mockComments = new Map<number, Comment[]>()

// 生成随机评论
const generateMockComments = (postId: number): Comment[] => {
  const comments: Comment[] = []
  const count = Math.floor(Math.random() * 5) + 3 // 3-7条评论

  for (let i = 0; i < count; i++) {
    const comment: Comment = {
      id: postId * 1000 + i, // 使用帖子ID作为前缀，避免ID重复
      username: `用户${Math.floor(Math.random() * 1000)}`,
      avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
      content: `这是第${i + 1}条评论，Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
      createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      likes: Math.floor(Math.random() * 100),
      replies: []
    }

    // 随机添加回复
    if (Math.random() > 0.5) {
      const replyCount = Math.floor(Math.random() * 3) + 1
      for (let j = 0; j < replyCount; j++) {
        comment.replies?.push({
          id: postId * 1000 + i * 100 + j, // 使用帖子ID作为前缀，避免ID重复
          username: `用户${Math.floor(Math.random() * 1000)}`,
          avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
          content: `回复 @${comment.username}：这是第${j + 1}条回复`,
          createTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
          likes: Math.floor(Math.random() * 50)
        })
      }
    }

    comments.push(comment)
  }

  return comments
}

// 重写 fetch 函数
const mockFetch = async function (input: RequestInfo | URL, init?: RequestInit) {
  const inputUrl = input instanceof URL ? input.toString() : input.toString()
  
  // 处理登录请求
  if (inputUrl.includes('/api/auth/login') && init?.method === 'POST') {
    const body = JSON.parse(init?.body as string)
    if (body.email === '<EMAIL>' && body.password === '123456') {
      return new Response(JSON.stringify({
        code: 200,
        data: {
          token: 'mock-token',
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
          }
        }
      }), {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }
    return new Response(JSON.stringify({
      code: 401,
      message: '邮箱或密码错误'
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  // 处理图片上传请求
  if (inputUrl.includes('/api/upload') && init?.method === 'POST') {
    // 模拟上传成功，返回一个随机的图片 URL
    const randomImage = `https://picsum.photos/800/600?random=${Date.now()}`
    return new Response(JSON.stringify({
      code: 200,
      data: {
        url: randomImage
      }
    }))
  }

  // 处理获取帖子详情请求
  if (inputUrl.match(/\/api\/posts\/\d+$/)) {
    const id = inputUrl.split('/').pop()
    const post = mockPosts.find(p => p.id === Number(id))
    if (post) {
      return new Response(JSON.stringify({
        code: 200,
        data: post
      }))
    }
    return new Response(JSON.stringify({
      code: 404,
      message: '帖子不存在'
    }))
  }

  // 处理发帖请求
  if (inputUrl.includes('/api/posts') && init?.method === 'POST') {

    const body = JSON.parse(init?.body as string)
    const newPost = {
      id: Date.now(),
      ...body,
      author: {
        id: 1,
        name: 'Test User',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      likes: 0,
      comments: 0,
      views: 0,
      createTime: new Date().toISOString(),
      isTop: false,
      isHot: false,
      isEssence: false
    }
    console.log("newPost",newPost,'-----')
    mockPosts.unshift(newPost)
    return new Response(JSON.stringify({
      code: 200,
      data: newPost
    }))
  }

  // 处理获取帖子列表请求
  if (inputUrl.includes('/api/posts') && (!init?.method || init?.method === 'GET')) {
    try {
      // 构造完整的 URL
      const baseUrl = window.location.origin
      const fullUrl = new URL(inputUrl, baseUrl)
      
      const page = parseInt(fullUrl.searchParams.get('page') || '1')
      const pageSize = parseInt(fullUrl.searchParams.get('pageSize') || '10')
      const category = fullUrl.searchParams.get('category')
      const searchQuery = fullUrl.searchParams.get('searchQuery')
      const sortBy = fullUrl.searchParams.get('sortBy')
      
      let filteredPosts = [...mockPosts]
      
      if (category) {
        filteredPosts = filteredPosts.filter(post => post.category === category)
      }
      
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filteredPosts = filteredPosts.filter(post => 
          post.title.toLowerCase().includes(query) || 
          post.content.toLowerCase().includes(query)
        )
      }
      
      if (sortBy === 'hot') {
        filteredPosts.sort((a, b) => b.views - a.views)
      } else {
        filteredPosts.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      }
      
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const paginatedPosts = filteredPosts.slice(start, end)
      
      return new Response(JSON.stringify({
        code: 200,
        data: {
          list: paginatedPosts,
          total: filteredPosts.length,
          page,
          pageSize
        }
      }), {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    } catch (error) {
      console.error('Error in mock fetch:', error)
      return new Response(JSON.stringify({
        code: 500,
        message: 'Internal Server Error'
      }), {
        headers: {
          'Content-Type': 'application/json'
        },
        status: 500
      })
    }
  }

  // 获取评论列表
  if (inputUrl.match(/\/api\/posts\/\d+\/comments/) && (!init || init.method === 'GET')) {
    const postId = parseInt(inputUrl.split('/')[3])

    if (!mockComments.has(postId)) {
      mockComments.set(postId, generateMockComments(postId))
    }
    return new Response(JSON.stringify({
      code: 200,
      message: 'success',
      data: mockComments.get(postId)
    }))
  }

  // 发表评论
  if (inputUrl.match(/\/api\/posts\/\d+\/comments/) && init?.method === 'POST') {
    const postId = parseInt(inputUrl.split('/')[3])
    const body = JSON.parse(init.body as string)
    
    if (!mockComments.has(postId)) {
      mockComments.set(postId, [])
    }

    const newComment: Comment = {
      id: postId * 1000 + (mockComments.get(postId)?.length || 0), // 使用帖子ID作为前缀，避免ID重复
      username: '当前用户',
      avatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
      content: body.content,
      createTime: new Date().toISOString(),
      likes: 0,
      replies: []
    }

    mockComments.get(postId)?.unshift(newComment)

    return new Response(JSON.stringify({
      code: 200,
      message: 'success',
      data: newComment
    }))
  }

  // 点赞评论
  if (inputUrl.match(/\/api\/comments\/\d+\/like/) && init?.method === 'POST') {
    return new Response(JSON.stringify({
      code: 200,
      message: 'success'
    }))
  }

  // 处理其他请求
  return originalFetch(input, init)
}

// 在开发环境中使用模拟数据
if (import.meta.env.DEV) {
  window.fetch = mockFetch
  console.log('Mock data initialized')
}

// 导出模拟数据
export { mockPosts } 