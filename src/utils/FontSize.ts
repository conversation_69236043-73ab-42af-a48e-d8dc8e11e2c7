import { Extension } from '@tiptap/core'

export interface FontSizeOptions {
  types: string[],
  sizes: string[],
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    fontSize: {
      setFontSize: (size: string) => ReturnType,
      unsetFontSize: () => ReturnType,
    }
  }
}

export const FontSize = Extension.create<FontSizeOptions>({
  name: 'fontSize',

  addOptions() {
    return {
      ...this.parent?.(),
      types: ['textStyle'],
      sizes: ['12px', '14px', '16px', '18px', '20px'],
    }
  },

  addGlobalAttributes() {
    return [{
      types:['textStyle'],
      attributes: {
        fontSize: {
          default: null,
          parseHTML: element => element.style.fontSize || null,
          renderHTML: attributes => {
            if (!attributes.fontSize) return {}
            return { style: `font-size: ${attributes.fontSize}` }
          },
        },
      },
    }]
  },

  addCommands() {
    return {
      setFontSize: size => ({ chain }) => {
        // if (!this.options.sizes.includes(size)) {
        //   console.warn(`Invalid font size: ${size}. Allowed sizes: ${this.options.sizes.join(', ')}`)
        //   return false
        // }
        return chain()
          .setMark('textStyle', { fontSize: size })
          .run()
      },
      unsetFontSize: () => ({ chain }) => {
        return chain()
          .setMark('textStyle', { fontSize: null })
          .removeEmptyTextStyle()
          .run()
      },
    }
  },
})