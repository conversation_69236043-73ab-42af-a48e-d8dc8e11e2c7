import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
// 数据返回的接口
// 定义请求响应参数，不含data
interface Result {
    code: number;
    msg: string
}

// 请求响应参数，包含data
interface ResultData<T = any> extends Result {
    data?: T;
}
// http://blog.frp.85me.cn
const URL: string = ''
// const URL: string = 'https://blog.mdiyer.cn/prod-api'
enum RequestEnums {
    TIMEOUT = 3600000,
    OVERDUE = 401, // 登录失效
    FAIL = 999, // 请求失败
    SUCCESS = 200, // 请求成功
}
const config = {
    // 默认地址
    baseURL: URL as string,
    // 设置超时时间
    timeout: RequestEnums.TIMEOUT as number,
    // 跨域时候允许携带凭证
    withCredentials: true
}

class RequestHttp {
    // 定义成员变量并指定类型
    service: AxiosInstance;
    public constructor(config: AxiosRequestConfig) {
        // 实例化axios
        this.service = axios.create(config);

        /**
         * 请求拦截器
         * 客户端发送请求 -> [请求拦截器] -> 服务器
         * token校验(JWT) : 接受服务器返回的token,存储到vuex/pinia/本地储存当中
         */
        this.service.interceptors.request.use(
            (config: any) => {
                const token = localStorage.getItem('token') || '';
                let lang = localStorage.getItem('lang') || 'en-US'
                if (lang == 'zh') {
                    lang = 'zh-CN'
                } else {
                    lang = 'en-US'
                }
                return {
                    ...config,
                    headers: {
                        'Authorization': 'Bearer ' + token, // 请求头中携带token信息
                        'i18n': lang,//en-US英文,zh-CN中文
                    }
                }
            },
            (error: AxiosError) => {
                
                // 请求报错
                Promise.reject(error)
            }
        )

        /**
         * 响应拦截器
         * 服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
         */
        this.service.interceptors.response.use(
            (response: AxiosResponse) => {
                const { data, config } = response; // 解构
                if (data.code === RequestEnums.OVERDUE) {
                    // 登录信息失效，应跳转到登录页面，并清空本地的token
                    localStorage.setItem('token', '');
                    console.log("router-----", router)
                    ElMessage.error("Please log in"); // 此处也可以使用组件提示报错信息

                    router.push({
                        path: '/auth/login'
                    })
                    return Promise.reject(data);
                }
                // 全局错误信息拦截（防止下载文件得时候返回数据流，没有code，直接报错）
                if (data.code && data.code !== RequestEnums.SUCCESS) {
                    ElMessage.error(data.msg); // 此处也可以使用组件提示报错信息
                    return Promise.reject(data)
                }
                return data;
            },
            (error: AxiosError) => {
                const { response } = error;

                // 处理超时
                if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                    ElMessage.error('The request timed out');
                } else {
                    // 其他错误（如 401、404、500 等）
                    ElMessage.error(error.message || 'Network error');
                }

                if (response) {
                    this.handleCode(response.status)
                }
                if (!window.navigator.onLine) {
                    // ElMessage.error('Network error');
                    // 可以跳转到错误页面，也可以不做操作
                    // return router.replace({
                    //   path: '/404'
                    // });
                }
            }
        )
    }
    handleCode(code: number): void {
        switch (code) {
            case 401:
                ElMessage.error('Login failed, please log in again');
                break;
            default:
                // ElMessage.error('failed');
                break;
        }
    }

    // 常用方法封装
    get<T>(url: string, params?: object): Promise<ResultData<T>> {
        return this.service.get(url, { params });
    }
    post<T>(url: string, datas?: object): Promise<ResultData<T>> {
        return this.service.post(url, null, {
            params: datas
        });
    }
    postBody<T>(url: string, datas?: object): Promise<ResultData<T>> {
        return this.service.post(url, datas);
    }
    put<T>(url: string, params?: object): Promise<ResultData<T>> {
        return this.service.put(url, params);
    }
    delete<T>(url: string, params?: object): Promise<ResultData<T>> {
        return this.service.delete(url, { params });
    }
}

// 导出一个实例对象
export default new RequestHttp(config);