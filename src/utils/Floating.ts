import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

export const FloatComponent = Extension.create({
  name: 'floatComponent',

  addOptions() {
    return {
      floatDirections: ['left', 'right', 'none'],
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: ['paragraph', 'heading'],
        attributes: {
          float: {
            default: null,
            parseHTML: element => element.getAttribute('data-float'),
            renderHTML: attributes => {
              if (!attributes.float) {
                return {}
              }
              return {
                'data-float': attributes.float,
                class: `float-${attributes.float}`,
                style: `float: ${attributes.float}; max-width: 50%; margin: ${attributes.float === 'left' ? '0 1rem 1rem 0' : '0 0 1rem 1rem'};`
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      setFloat: (floatDirection) => ({ commands, state, view }) => {
        if (!this.options.floatDirections.includes(floatDirection)) {
          return false
        }

        const { selection } = state
        const { from, to } = selection

        // 如果没有选区，使用光标位置
        const startPos = selection.empty ? from : from
        const endPos = selection.empty ? from : to

        // 更新节点属性
        return commands.updateAttributes('paragraph', {
          float: floatDirection === 'none' ? null : floatDirection
        })
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('floatComponent'),
        props: {
          decorations: (state) => {
            // 这里可以添加装饰器来增强浮动效果
            return null
          },
        },
      }),
    ]
  },
})