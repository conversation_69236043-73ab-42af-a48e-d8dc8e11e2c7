/**
 * 文件上传工具函数
 */
import axios from '@/utils/request'

/**
 * 生成文件唯一标识
 * @param {File} file 文件对象
 * @returns {string} 文件唯一标识
 */
function generateFileIdentifier(file) {
  // 使用文件名、大小和最后修改时间组合生成唯一标识
  const lastModified = file.lastModified ? file.lastModified.toString() : Date.now().toString()
  return `${file.name}-${file.size}-${lastModified}`
}

/**
 * 分片上传文件
 * @param {File} file 文件对象
 * @param {string} bizType 业务类型
 * @param {Function} progressCallback 进度回调函数
 * @param {Function} messageCallback 消息回调函数
 * @returns {Promise<string>} 文件URL
 */
export async function uploadFileInChunks(file, bizType, progressCallback, messageCallback) {
  // 生成文件唯一标识
  const identifier = generateFileIdentifier(file)

  console.log('🚀 开始分片上传:', {
    fileName: file.name,
    fileSize: file.size,
    identifier,
    bizType
  })

  if (progressCallback) {
    progressCallback(0) // 初始进度0%
  }

  // 分片大小5MB，满足MinIO的ComposeObject要求
  const chunkSize = 5 * 1024 * 1024
  const totalChunks = Math.ceil(file.size / chunkSize)

  console.log('📊 分片信息:', {
    chunkSize: chunkSize / (1024 * 1024) + 'MB',
    totalChunks
  })

  // 检查分片上传状态
  console.log('🔍 检查分片状态...')
  const checkUrl = `/api/oss/chunk/check?identifier=${encodeURIComponent(identifier)}&chunkNumber=1&bizType=${encodeURIComponent(bizType)}`
  const checkResponse = await axios.get(checkUrl)

  console.log('📋 分片检查响应:', checkResponse)

  // 检查响应数据结构，C端可能有不同的响应格式
  const responseData = checkResponse.data || checkResponse

  console.log('📋 实际数据对象:', responseData)

  // 如果文件已完全上传（有fileUrl说明文件已完成），直接返回URL
  if (responseData && responseData.fileUrl) {
    console.log('✅ 文件已完全上传，直接返回URL:', responseData.fileUrl)
    if (progressCallback) {
      progressCallback(100)
    }
    return responseData.fileUrl
  }

  // 获取已上传的分片列表
  const uploadedChunks = responseData && responseData.uploadedChunks ? responseData.uploadedChunks : []

  console.log('📦 已上传分片列表:', uploadedChunks)
  console.log('📈 当前进度:', responseData?.progress || 0)
  console.log('🔍 uploadedChunks类型:', typeof uploadedChunks, '是否为数组:', Array.isArray(uploadedChunks))
  console.log('🔍 uploadedChunks长度:', uploadedChunks.length)

  // 如果有已上传的分片，显示断点续传提示
  if (uploadedChunks.length > 0) {
    const resumeProgress = Math.round((uploadedChunks.length / totalChunks) * 100)
    console.log(`🔄 检测到已上传的分片，正在恢复上传进度... (${uploadedChunks.length}/${totalChunks}) - ${resumeProgress}%`)

    // 显示断点续传检测提示
    if (messageCallback) {
      messageCallback('🔄 检测到已上传的分片，正在恢复上传进度...')
    }

    if (progressCallback) {
      progressCallback(resumeProgress)
    }

    // 延迟3秒让用户看到第一个提示
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log(`✅ 已恢复上传进度，继续上传剩余分片... (${uploadedChunks.length}/${totalChunks})`)

    // 显示继续上传提示
    if (messageCallback) {
      messageCallback('✅ 已恢复上传进度，继续上传剩余分片...')
    }

    // 再延迟2秒让用户看到第二个提示
    await new Promise(resolve => setTimeout(resolve, 1000))
  } else {
    console.log('🆕 没有已上传的分片，开始全新上传')
    if (messageCallback) {
      messageCallback('正在上传文件...')
    }
  }

  // 上传所有分片
  console.log('🔄 开始分片上传循环...')
  console.log('🔍 准备检查的分片列表:', uploadedChunks)
  for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
    console.log(`🔍 检查分片 ${chunkNumber}, 是否在已上传列表中:`, uploadedChunks.includes(chunkNumber))

    // 如果分片已上传，跳过
    if (uploadedChunks.includes(chunkNumber)) {
      console.log(`⏭️ 跳过已上传分片 ${chunkNumber}/${totalChunks}`)
      if (progressCallback) {
        // 计算当前进度
        const currentProgress = Math.round((chunkNumber / totalChunks) * 100)
        progressCallback(currentProgress)
      }
      continue
    }

    console.log(`📤 开始上传分片 ${chunkNumber}/${totalChunks}`)

    // 计算分片范围
    const start = (chunkNumber - 1) * chunkSize
    const end = Math.min(file.size, start + chunkSize)
    const chunk = file.slice(start, end)

    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', chunk)
    formData.append('chunkNumber', chunkNumber)
    formData.append('totalChunks', totalChunks)
    formData.append('chunkSize', chunkSize)
    formData.append('currentChunkSize', chunk.size)
    formData.append('totalSize', file.size)
    formData.append('identifier', identifier)
    formData.append('filename', file.name)
    formData.append('bizType', bizType)

    // 上传分片
    const response = await axios.postBody('/api/oss/chunk/upload', formData)

    console.log(`📤 分片 ${chunkNumber} 上传响应:`, response)

    if (response.code !== 200) {
      throw new Error('分片上传失败: ' + response.msg)
    }

    // 更新进度
    if (progressCallback) {
      // 计算当前进度
      const currentProgress = Math.round((chunkNumber / totalChunks) * 100)
      progressCallback(currentProgress)
    }

    // 如果所有分片都已上传，返回文件URL
    if (response.data && response.data.fileUrl) {
      console.log('🎉 所有分片上传完成，返回文件URL:', response.data.fileUrl)
      if (progressCallback) {
        progressCallback(100)
      }
      return response.data.fileUrl
    }
  }

  // 如果需要手动合并分片
  // 直接在URL中添加参数，而不是使用params对象
  const mergeUrl = `/api/oss/chunk/merge?identifier=${encodeURIComponent(identifier)}&filename=${encodeURIComponent(file.name)}&totalChunks=${totalChunks}&totalSize=${file.size}&bizType=${encodeURIComponent(bizType)}`
  const mergeResponse = await axios.post(mergeUrl)

  if (mergeResponse.code !== 200) {
    throw new Error('合并分片失败: ' + mergeResponse.msg)
  }

  if (progressCallback) {
    progressCallback(100)
  }

  return mergeResponse.url
}
