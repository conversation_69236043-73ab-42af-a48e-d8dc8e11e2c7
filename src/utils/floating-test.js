/**
 * 浮动功能测试工具
 * 用于验证左右浮动功能是否正常工作
 */

// 测试浮动功能的辅助函数
export function testFloatingFunction() {
  console.log('开始测试浮动功能...')
  
  // 模拟选中文本
  const testText = '这是一段测试文本，用于验证浮动功能是否正常工作。'
  
  // 测试左浮动
  const leftFloatStyle = 'float: left; margin: 0 1rem 1rem 0; max-width: 50%; clear: both;'
  const leftFloatContent = `<div style="${leftFloatStyle}">${testText}</div>`
  console.log('左浮动HTML:', leftFloatContent)
  
  // 测试右浮动
  const rightFloatStyle = 'float: right; margin: 0 0 1rem 1rem; max-width: 50%; clear: both;'
  const rightFloatContent = `<div style="${rightFloatStyle}">${testText}</div>`
  console.log('右浮动HTML:', rightFloatContent)
  
  // 测试清除浮动
  console.log('清除浮动HTML:', testText)
  
  console.log('浮动功能测试完成')
}

// 验证浮动样式是否正确应用
export function validateFloatingStyles(element) {
  if (!element) return false
  
  const computedStyle = window.getComputedStyle(element)
  const float = computedStyle.float
  
  if (float === 'left' || float === 'right') {
    console.log(`元素应用了${float}浮动`)
    console.log('margin:', computedStyle.margin)
    console.log('max-width:', computedStyle.maxWidth)
    return true
  }
  
  return false
}
