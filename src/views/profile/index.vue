<template>
  <div class="profile container">
    <el-row :gutter="24">
      <!-- User Info Card -->
      <el-col :xs="24" :md="8">
        <el-card class="user-card">
          <div class="user-header">
            <el-avatar :size="100" v-if="authStore.user?.avatar" :src="authStore.user?.avatar" />
            <img style="width: 100px;height: 100px;border-radius: 50%;" v-else src="../../static/thumb.png" />

            <h2>{{ authStore.user?.nickname }}</h2>
            <p class="bio">{{ authStore.user?.bio }}</p>
          </div>

          <el-divider />

          <div class="user-stats">
            <div class="stat-item">
              <h3>{{ userData.collectCount||0 }}</h3>
              <span>{{$t('collection')}}</span>
            </div>
            <div class="stat-item">
              <h3>{{ userData.commentCount||0 }}</h3>
              <span>{{$t('comment')}}</span>
            </div>
            <div class="stat-item">
              <h3>{{ userData.likeCount||0 }}</h3>
              <span>{{$t('praise')}}</span>
            </div>
          </div>

          <el-divider />

          <div class="user-actions">
            <el-button type="primary" @click="handleEditProfile">
              {{$t('edits')}}
            </el-button>
            <el-button type="primary">
              <router-link :to="{ name: 'ResetPassword' }" style="color: #fff;">{{$t('changePassword')}}</router-link>


            </el-button>

          </div>
        </el-card>
      </el-col>

      <!-- Activity Tabs -->
      <el-col :xs="24" :md="16">
        <el-card>
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="$t('myPost')" name="posts">
              <profile1></profile1>
            </el-tab-pane>

            <el-tab-pane :label="$t('myComment')" name="comments">
              <profile2></profile2>
            </el-tab-pane>
            <el-tab-pane :label="$t('myCollection')" name="favorites">
              <profile3></profile3>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- Edit Profile Dialog -->
    <el-dialog v-model="dialogVisible" :title="$t('edits')" width="400px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="50px">
        <el-form-item :label="$t('nick')" prop="name">
          <el-input v-model="form.nickname" />
        </el-form-item>

        <el-form-item :label="$t('avatar')">
          <div style="display: flex;">
            <el-upload class="avatar-uploader" action="" :on-change="handleChange" :show-file-list="false"
              :before-upload="beforeAvatarUpload" :auto-upload="false">
              <img v-if="form.avatar" :src="form.avatar" class="avatar" />
              <img v-else src="../../static/thumb.png" class="avatar" />
            </el-upload>
            <div class="flex-wrap" style="margin-left: 30px;">
              <img @click="form.avatar=item" v-for="item,i in thumbs" :key="i" :src="item"
                style="width: 36px;margin: 0 5px;cursor: pointer;" />
            </div>
          </div>
        </el-form-item>

        <el-form-item :label="$t('introduction')" prop="bio">
          <el-input v-model="form.bio" type="textarea" :rows="3" placeholder="introduction" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{$t('cancel')}}</el-button>
          <el-button type="primary" @click="handleSaveProfile">
            {{$t('save')}}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import profile1 from '@/components/profile1.vue'
  import profile2 from '@/components/profile2.vue'
  import profile3 from '@/components/profile3.vue'

  import { ref, onMounted } from 'vue'
  import { Star, ChatRound, Plus } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance } from 'element-plus'
  import type { User, Post } from '@/types'
  import { useRouter } from 'vue-router'
  import axios from '@/utils/request'
  import { useAuthStore } from '@/stores/auth'
  const authStore = useAuthStore()

  // Data
  const activeTab = ref('posts')
  const loading = ref(false)
  const dialogVisible = ref(false)
  const formRef = ref < FormInstance > ();
  const userData = ref({})
  const router = useRouter()
  const userInfo = ref({
    nickname: '',
    avatar: '',
    bio: '',

  })
  const handlePostClick = (id: number) => {
    router.push(`/post/${id}`)
  }
  const thumbs = ref([
    'https://minio.mdiyer.cn/blog/2025/05/23/deb65a159b0c42fda77c293eec1c64b5.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/55e6cdcb988c4c18b1d08f57277a7de0.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/247fc4035757423295eae04b38c1ed05.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/c8a61a959bca4972895d4c688ddf33f3.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/cfd2ab0b0a4f43178eadb4e2401bc219.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/7c88f624a8974623b7a64cb126f70f9c.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/5a74e948f90c4ad0a3ff39801bb86773.png',
    'https://minio.mdiyer.cn/blog/2025/05/23/20d51a7bb2814901b9dbe77ce2c1ff11.png',

  ])

  const posts = ref < Post > ([]);
  interface Comment {
    id: number
    username: string
    avatar: string
    content: string
    createTime: string
    likes: number
    replies?: Comment[]
  }
  const list = ref < Comment > ([]);

  const form = ref({
    nickname: '',
    avatar: '',
    bio: ''
  })

  const rules = {
    nickname: [
      { required: true, message: '请输入昵称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    // bio: [
    //   { max: 200, message: '简介不能超过200个字符', trigger: 'blur' }
    // ]
  }



  // Methods

  const userStats = async () => {
    try {
      const ret = await axios.get('/api/interaction/user/stats', {})
      if (ret.code == 200) {
        userData.value = ret.data
      }

    } catch (error) {
    }
  }
  const fetchUserInfo = async () => {
    try {
      const userInfo = await axios.get("/api/auth/info", {});
      if (userInfo.code == 200) {
        await authStore.setUserInfo(userInfo)
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error)
    }
  }

  const handleEditProfile = () => {
    form.value = {
      nickname: authStore.user.nickname,
      avatar: authStore.user.avatar,
      bio: authStore.user.bio
    }
    console.log("form.value", form.value)
    dialogVisible.value = true
  }

  const beforeAvatarUpload = (file: File) => {
    console.log(file)
    const isJPG = file.type === 'image/jpeg'
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isJPG) {
      ElMessage.error('头像只能是 JPG 格式!')
    }
    if (!isLt2M) {
      ElMessage.error('头像大小不能超过 2MB!')
    }


    return isJPG && isLt2M
  }
  const handleChange = async (file, fileList) => {
    //console.log("file",file)
    const formData = new FormData()
    formData.append('file', file.raw)
    //console.log("formData",formData)
    const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
    if (data.code == 200 && data.url) {
      form.value.avatar = data.url;
    } else {
      ElMessage.error('头像上传失败，请重试')
    }
  }

  const handleSaveProfile = async () => {
    if (!formRef.value) return
    console.log("formRef==", formRef)

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {

          const ret = await axios.postBody("/api/auth/profile", form.value);
          if (ret.code == 200) {
            fetchUserInfo()
            ElMessage.success('success')
            dialogVisible.value = false;

          }
        } catch (error) {
          console.error('Failed to save profile:', error)
          ElMessage.error('fail')
        }
      }
    })
  }

  // Lifecycle
  onMounted(() => {
    fetchUserInfo()
    userStats();
  })
</script>

<style lang="scss" scoped>
  .profile {
    padding: 24px 0;
  }

  .user-card {
    .user-header {
      text-align: center;
      padding: 20px 0;

      h2 {
        margin: 16px 0 8px;
        color: $text-primary;
      }

      .bio {
        color: $text-secondary;
        font-size: $font-size-small;
      }
    }

    .user-stats {
      display: flex;
      justify-content: space-around;
      padding: 16px 0;

      .stat-item {
        text-align: center;

        h3 {
          color: $text-primary;
          margin: 0 0 4px;
        }

        span {
          color: $text-secondary;
          font-size: $font-size-small;
        }
      }
    }

    .user-actions {
      text-align: center;
      padding: 16px 0;
    }
  }

  .post-list {
    .post-item {
      cursor: pointer;
      padding: 16px 0;
      border-bottom: 1px solid $border-color-lighter;

      &:last-child {
        border-bottom: none;
      }

      h3 {
        color: $text-primary;
        margin: 0 0 8px;
      }

      p {
        color: $text-regular;
        margin: 0 0 8px;
      }

      .post-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $text-secondary;
        font-size: $font-size-small;

        .post-stats {
          display: flex;
          align-items: center;
          gap: 16px;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
    line-height: 178px;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .profile-form {
    max-width: 600px;
    margin: 0 auto;
  }

  .form-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }

  @media (max-width: $breakpoint-md) {
    .profile {
      padding: 16px;

      .el-col {
        margin-bottom: 16px;
      }
    }
  }
</style>