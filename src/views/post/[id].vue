<template>
  <div class="post-container">
    <div class="post-content-container">
      <!-- 帖子内容 -->
      <div class="post-content" v-html="post.content"></div>

      <!-- 评论区 -->
      <div class="comments-section">
        111
        <Comments :post-id="Number(id)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import Comments from '@/components/Comments.vue'
import type { Post } from '@/types'

const route = useRoute()
const id = route.params.id
const post = ref<Post>({
  id: 0,
  title: '',
  content: '',
  author: {
    id: 0,
    name: '',
    avatar: ''
  },
  category: '',
  tags: [],
  likes: 0,
  comments: 0,
  views: 0,
  createTime: '',
  isTop: false,
  isHot: false,
  isEssence: false
})

// 获取帖子详情
const fetchPost = async () => {
  try {
    const response = await fetch(`/api/posts/${id}`)
    const result = await response.json()
    if (result.code === 200) {
      post.value = result.data
    }
  } catch (error) {
    console.error('Failed to fetch post:', error)
    ElMessage.error('获取帖子详情失败')
  }
}

onMounted(() => {
  fetchPost()
})
</script>

<style scoped lang="scss">
.post-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .post-content-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .post-content {
      margin-bottom: 40px;
      line-height: 1.8;
      color: #333;

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
      }

      :deep(pre) {
        background: #f6f8fa;
        padding: 16px;
        border-radius: 4px;
        overflow-x: auto;
      }

      :deep(code) {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 14px;
      }
    }

    .comments-section {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  }
}
</style> 