<template>
  <div class="post-detail">
    <div class="container">
      <template v-if="loading">
        <div class="loading-placeholder">
          <el-skeleton :rows="10" animated />
        </div>
      </template>

      <template v-else-if="post">
        <div class="post-header">
          <h1 class="title">{{ post.title }}</h1>
          <h3 style="margin-bottom: 20px;">{{post.summary}}</h3>
          <div class="meta">
            <div class="author">
              <el-avatar v-if="post.avatar" :src="post.avatar" :size="32" />
              <img style="width: 32px;height: 32px;border-radius: 50%;" v-else :size="32" src="../../static/thumb.png" />

              <span class="name">{{ post.nickname }}</span>
            </div>

            <div class="info">
              <span class="time">{{ post.createTime }}</span>
              <el-tag size="small" class="category">{{ post.category.categoryName }}</el-tag>
              <el-tag v-for="tag in post.tags" :key="tag" size="small" class="tag" type="info">
                {{ tag.tagName }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="post-content" v-html="post.content"></div>
        <div class="post-content" style="border-bottom: 1px solid #eee;" v-if="post.attachments?.length">
          <div v-for="item,i in post.attachments">
            <a :download="item.fileName" target="_blank" :href="item.fileUrl">{{item.fileName}}</a>
          </div>
        </div>
        
        <div class="post-footer">
          <div class="actions">
            <el-button @click="collect(!post.collected)" :type="post.collected?'primary':''" :icon="Star" circle />
            <span class="count">{{ post.collectCount }}</span>
            <!-- <el-button @click="likeComment(!post.isLiked)" :type="post.isLiked?'primary':''" :icon="ChatRound" circle /> -->
             <el-button  @click="likeComment(!post.isLiked)" circle  :type="post.isLiked?'primary':''">
               <img v-if="post.isLiked" style="width: 15px;" src="@/static/zan2.png" />
               <img v-else style="width: 15px;" src="@/static/zan1.png" />
              </el-button>
            <span class="count">{{ post.likeCount }}</span>
            <el-button @click="shareBlock(post)" :icon="Share" circle />
          </div>
        </div>

        <div class="comments-section">
          
          <Comments :postId="id"></Comments>
        </div>
      </template>

      <template v-else>
        <el-empty description="帖子不存在或已被删除" />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Comments from '@/components/Comments.vue'

  import { ref, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { Star, ChatRound, Share } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import type { Post } from '@/types'
  import axios from '@/utils/request'

  const route = useRoute()
  const router = useRouter()
  const loading = ref(true)
  const post = ref < Post | null > (null)
  const id = route.params.id;
  // let comments=[];
  const fetchPost = async () => {
    try {
      loading.value = true
      const ret = await axios.get(`/api/post/detail/${route.params.id}`,)
      if (ret.code == 200) {
        post.value = ret.data
      }
    } catch (error) {
    } finally {
      loading.value = false
    }
  }
   const shareBlock =async (data) => {
    let url = window.location.host + '/post/' + data.postId;
    try {
      await navigator.clipboard.writeText(url);
      ElMessage.success('The link has been copied');
    } catch {
      ElMessage.error('Replication failed');
    }
  }
  const shares= async()=>{
    
    const result = await axios.post("/api/interaction/sharee", {postId:route.params.id})
      if (result.code == 200) {
        // ElMessage.success("success")
      } else {
        ElMessage.error('fail')

      }
  }
  // 点赞
  const likeComment = async (isLike: boolean) => {
    try {
      const params = {
        "isLike": isLike, //是否点赞
        "contentId": id, //内容ID
        "contentType": 0//内容类型（0帖子 2评论）
      }
      const result = await axios.postBody("/api/interaction/like", params)
      if (result.code == 200) {
        fetchPost();
        ElMessage.success("success")
      } else {
        ElMessage.error('fail')

      }
    } catch (error) {
      console.error('Failed to like comment:', error)
    }
  }


  // 收藏
  const collect = async (isLike: boolean) => {
    try {
      const params = {
        "isCollect": isLike, //是否收藏
        "contentId": Number(id), //内容ID
        "contentType": 0//内容类型（0帖子 2评论）
      }
      const result = await axios.postBody("/api/interaction/collect", params)
      if (result.code == 200) {
        fetchPost();
        ElMessage.success("success")
      } else {
        ElMessage.error('fail')
      }
    } catch (error) {
      // console.error('Failed to like comment:', error)
    }
  }


  onMounted(() => {
    fetchPost()
  })
</script>

<style lang="scss" scoped>
  .post-detail {
    padding: 10px 0;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;
    }

    .loading-placeholder {
      padding: 24px;
      background: white;
      /* border-radius: $border-radius-base; */
      /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
    }

    .post-header {
      /* margin-bottom: 24px; */
      /* padding: 24px; */
      background: white;
      /* border-radius: $border-radius-base; */
      /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
      border-bottom: 1px solid #eee;
      padding: 20px 0;

      .title {
        margin: 0 0 16px;
        font-size: 28px;
        font-weight: 600;
        color: $text-primary;
      }

      .meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;

        .author {
          display: flex;
          align-items: center;
          gap: 8px;

          .name {
            color: $text-regular;
            font-size: $font-size-base;
          }
        }

        .info {
          display: flex;
          align-items: center;
          gap: 12px;

          .time {
            color: $text-secondary;
            font-size: $font-size-small;
          }

          .category {
            background-color: $primary-color;
            color: white;
          }

          .tag {
            background-color: $background-color;
          }
        }
      }
    }

    .post-content {
      /* margin-bottom: 24px; */
      padding: 20px 0;
      background: white;
      /* border-radius: $border-radius-base; */
      /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
      /* border-bottom: 1px solid #eee; */

      :deep(p) {
        margin: 1em 0;
        line-height: 1.75;
      }

      :deep(h1, h2, h3, h4, h5, h6) {
        margin: 1.5em 0 0.5em;
        font-weight: 600;
        color: $text-primary;
      }

      :deep(ul, ol) {
        padding-left: 2em;
        margin: 1em 0;
      }

      :deep(code) {
        background-color: rgba($text-regular, 0.1);
        padding: 0.2em 0.4em;
        border-radius: $border-radius-small;
        font-family: monospace;
      }

      :deep(pre) {
        background-color: #282c34;
        padding: 1em;
        border-radius: $border-radius-base;
        overflow-x: auto;

        code {
          background-color: transparent;
          padding: 0;
          color: #abb2bf;
        }
      }

      :deep(blockquote) {
        margin: 1em 0;
        padding: 0.5em 1em;
        border-left: 4px solid $primary-color;
        background-color: rgba($primary-color, 0.1);
        color: $text-regular;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: $border-radius-base;
      }
    }

    .post-footer {
      /* margin-bottom: 24px; */
      padding: 16px 24px;
      background: white;
      /* border-radius: $border-radius-base; */
      /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
      border-bottom: 1px solid #eee;

      .actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;

        .count {
          font-size: $font-size-base;
          color: $text-regular;
          margin-right: 16px;
        }
      }
    }

    .comments-section {
      padding: 20px 0;
      background: white;
      /* border-radius: $border-radius-base; */
      /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */

      h2 {
        margin: 0 0 24px;
        font-size: 20px;
        font-weight: 600;
        color: $text-primary;
      }
    }
  }
</style>