<template>
  <div class="register">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="handleSubmit">
      <el-form-item :label="$t('buttons.email')" prop="email">
        <el-input v-model="form.email" :placeholder="$t('msg4')" :prefix-icon="Message">
          <!-- <template #append>
            <el-button :disabled="!!countdown" @click="handleSendCode" :loading="sendingCode">
              {{ countdown ? `${countdown}s` : $t('getCode') }}
            </el-button>
          </template> -->
        </el-input>
      </el-form-item>

      <!-- <el-form-item :label="$t('verificationCode')" prop="code">
        <el-input v-model="form.code" placeholder="verification Code" :prefix-icon="Key" maxlength="6" />
      </el-form-item> -->

      <el-form-item :label="$t('buttons.password')" prop="password">
        <el-input v-model="form.password" type="password" :placeholder="$t('msg1')"  :prefix-icon="Lock" show-password />
      </el-form-item>

      <el-form-item :label="$t('confirmPassword')" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" :placeholder="$t('msg2')" :prefix-icon="Lock"
          show-password />
      </el-form-item>

      <!-- <el-form-item>
        <el-checkbox v-model="form.agreement" class="agreement">
          我已阅读并同意
          <el-link type="primary" :underline="false">服务协议</el-link>
          和
          <el-link type="primary" :underline="false">隐私政策</el-link>
        </el-checkbox>
      </el-form-item> -->

      <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading">{{$t('sigin')}}</el-button>

      <div class="login-link">
        {{$t('buttons.havAccount')}} ?
        <router-link :to="{ name: 'Login' }">{{$t('login')}}</router-link>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { Message, Lock, Key } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import type { FormInstance } from 'element-plus'
  import axios from '@/utils/request'
  import axios1 from 'axios';
  const router = useRouter()
  const authStore = useAuthStore()
  const formRef = ref < FormInstance > ()
  const loading = ref(false)
  const sendingCode = ref(false)
  const countdown = ref(0)
  import { useI18n } from 'vue-i18n'
  const {t}=useI18n()

  const form = ref({
    email: '',
    code: '',
    password: '',
    confirmPassword: '',
    agreement: true
  })

  const validatePass = (rule: any, value: string, callback: Function) => {
    if (value === '') {
      callback(new Error(t('msg1')))
    } else {
      if (form.value.confirmPassword !== '') {
        if (formRef.value) {
          formRef.value.validateField('confirmPassword')
        }
      }
      callback()
    }
  }

  const validatePass2 = (rule: any, value: string, callback: Function) => {
    if (value === '') {
      callback(new Error(t('msg2')))
    } else if (value !== form.value.password) {
      callback(new Error(t('msg3')))
    } else {
      callback()
    }
  }

  const validateAgreement = (rule: any, value: boolean, callback: Function) => {
    callback()

    // if (!value) {
    //   callback(new Error('请阅读并同意服务协议和隐私政策'))
    // } else {
    //   callback()
    // }
  }

  const rules = {
    email: [
      { required: true, message: t('msg4'), trigger: 'blur' },
      { type: 'email', message: t('msg5'), trigger: 'blur' }
    ],
    // code: [
    //   { required: true, message: '请输入验证码', trigger: 'blur' },
    //   // { len: 6, message: '验证码为6位数字', trigger: 'blur' }
    // ],
    password: [
      { validator: validatePass, trigger: 'blur' },
      { min: 6, message: t('msg6'), trigger: 'blur' }
    ],
    confirmPassword: [
      { validator: validatePass2, trigger: 'blur' }
    ],
    agreement: [
      { validator: validateAgreement, trigger: 'change' }
    ]
  }

  const startCountdown = () => {
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  }

  const handleSendCode = async () => {


    try {
      await formRef.value?.validateField('email')
      sendingCode.value = true
      let datas = axios.post('/api/auth/send-code',
        {
          email: form.value.email,
        }
      );
      if (datas.code == 200) {
        ElMessage.success('验证码已发送到您的邮箱')
        startCountdown()
      } else {
        ElMessage.error(datas.msg)
      }
    } catch (error) {
      console.error('Send code failed:', error)
      ElMessage.error('发送验证码失败，请重试')
    } finally {
      sendingCode.value = false
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      loading.value = true
      await formRef.value.validate()
      let datas = await axios.post('/api/auth/register',
        {
          email: form.value.email,
          code: form.value.code,
          password: form.value.password
        }
      );
      if (datas.code == 200) {
        ElMessage.success(datas.msg)
        router.push('/auth/login')
      } else {
        ElMessage.error(datas.msg)

      }

    } catch (error) {
      console.error('Registration failed:', error)
      // ElMessage.error('注册失败，请重试')
    } finally {
      loading.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .register {
    .agreement {
      margin-bottom: 24px;
    }

    .submit-btn {
      width: 100%;
      margin-bottom: 16px;
    }

    .login-link {
      text-align: center;
      color: $text-regular;
      font-size: $font-size-small;

      a {
        color: $primary-color;
        text-decoration: none;
        margin-left: 4px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
</style>