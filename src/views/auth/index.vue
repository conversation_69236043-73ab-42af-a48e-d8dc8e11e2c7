<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <el-icon :size="64" class="logo-icon"><Edit /></el-icon>
          <h1>{{$t('title')}}</h1>
        </div>
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Edit } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $background-color-base;
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
}

.auth-card {
  background: $background-color-white;
  border-radius: $border-radius-base;
  padding: 40px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;

  .logo-icon {
    color: $primary-color;
    margin-bottom: 16px;
  }

  h1 {
    font-size: $font-size-extra-large;
    color: $text-primary;
    margin: 0;
  }
}

@media (max-width: $breakpoint-sm) {
  .auth-container {
    padding: 20px;
  }
}
</style> 