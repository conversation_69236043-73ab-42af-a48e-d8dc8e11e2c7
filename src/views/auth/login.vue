<template>
  <div class="login">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="handleSubmit">
      <el-form-item :label="$t('buttons.email')" prop="email">
        <el-input v-model="form.email" :placeholder="$t('msg4')" :prefix-icon="Message" />
      </el-form-item>

      <el-form-item :label="$t('buttons.password')" prop="password">
        <el-input v-model="form.password" type="password" :placeholder="$t('msg1')" :prefix-icon="Lock" show-password />
      </el-form-item>

      <div class="form-footer">
        <el-checkbox v-model="form.remember">{{$t('buttons.rememberMe')}}</el-checkbox>
        <!-- <el-link type="primary" :underline="false">忘记密码？</el-link> -->
        <el-popover width="auto" class="box-item" title="" content="Bottom Center prompts info" placement="bottom">

          <template #reference>
            <router-link :to="{ name: 'ResetPassword' }">{{$t('buttons.forgotPassword')}}？</router-link>
          </template>
          <div>
            {{ contactInfo }}
          </div>
        </el-popover>
        <!-- <el-link type="primary" :underline="false">忘记密码？</el-link> -->


      </div>

      <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading">
        {{$t('buttons.login')}}
      </el-button>

      <div class="register-link">
        {{$t('buttons.noAccount')}} ？
        <router-link :to="{ name: 'Register' }">
          {{$t('buttons.sigin')}}
        </router-link>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { Message, Lock } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import type { FormInstance } from 'element-plus'

  import axios from '@/utils/request'
  import { useI18n } from 'vue-i18n'
  import { computed } from 'vue'
  const { t, locale } = useI18n()

  const router = useRouter()
  const route = useRoute()
  const authStore = useAuthStore()
  const formRef = ref < FormInstance > ()
  const loading = ref(false)

  const form = ref({
    email: '',
    password: '',
    remember: false
  })

  const rules = {
    email: [
      { required: true, message: t('msg4'), trigger: 'blur' },
      { type: 'email', message: t('msg5'), trigger: 'blur' }
    ],
    password: [
      { required: true, message: t('msg1'), trigger: 'blur' },
      { min: 6, message: t('msg6'), trigger: 'blur' }
    ]
  }

  // 计算属性：联系信息（避免vue-i18n解析@符号）
  const contactInfo = computed(() => {
    const atSymbol = '@'
    if (locale.value === 'zh') {
      return `联系后台邮箱：peakhy${atSymbol}126.com或598090079${atSymbol}qq.com`
    } else {
      return `Contact the backend email: peakhy${atSymbol}126.com or 598090079${atSymbol}qq.com`
    }
  })

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      loading.value = true
      await formRef.value.validate()

      let datas = await axios.post('/api/auth/login', {
        email: form.value.email,
        password: form.value.password
      });

      if (datas.code == 200) {
        await authStore.login(datas)
        ElMessage.success(datas.msg)

        const userInfo = await axios.get("/api/auth/info", {});
        if (userInfo.code == 200) {
          console.log("userInfo.data", userInfo.data)
          await authStore.setUserInfo(userInfo)
        }

        const redirectPath = typeof route.query.redirect === 'string' ? route.query.redirect : '/home'
        router.push(redirectPath)
      } else {
        ElMessage.error(datas.msg || '登录失败')
      }
    } catch (error) {
      console.error('Login failed:', error)
      ElMessage.error(t('loginFailed') || '登录失败，请检查邮箱和密码')
    } finally {
      loading.value = false
    }
    // console.log("datas", datas)

    // } catch (error) {
    //   console.error('Login failed:', error)
    //   ElMessage.error('登录失败，请检查邮箱和密码')
    // } finally {
    //   loading.value = false
    // }
  }
</script>

<style lang="scss" scoped>
  .login {
    .form-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .submit-btn {
      width: 100%;
      margin-bottom: 16px;
    }

    .register-link {
      text-align: center;
      color: $text-regular;
      font-size: $font-size-small;

      a {
        color: $primary-color;
        text-decoration: none;
        margin-left: 4px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
</style>