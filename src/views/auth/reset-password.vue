<template>
    <div class="register">
        <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="handleSubmit">
            <el-form-item :label="$t('buttons.email')" prop="email">
                <el-input v-model="form.email" :placeholder="$t('msg4')" :prefix-icon="Message" />
            </el-form-item>

            <el-form-item :label="$t('oldPassword')" prop="oldPassword">
                <el-input v-model="form.oldPassword" type="password" :placeholder="$t('oldPasswordPlaceholder')" :prefix-icon="Lock"
                    show-password />
            </el-form-item>

            <el-form-item :label="$t('newPassword')" prop="newPassword">
                <el-input v-model="form.newPassword" type="password" :placeholder="$t('newPasswordPlaceholder')" :prefix-icon="Lock"
                    show-password />
            </el-form-item>

            <el-form-item :label="$t('confirmPassword')" prop="confirmPassword">
                <el-input v-model="form.confirmPassword" type="password" :placeholder="$t('confirmPasswordPlaceholder')" :prefix-icon="Lock"
                    show-password />
            </el-form-item>

            <!-- <el-form-item>
                <el-checkbox v-model="form.agreement" class="agreement">
                    我已阅读并同意
                    <el-link type="primary" :underline="false">服务协议</el-link>
                    和
                    <el-link type="primary" :underline="false">隐私政策</el-link>
                </el-checkbox>
            </el-form-item> -->

            <el-button type="primary" native-type="submit" class="submit-btn" :loading="loading">{{$t('changePassword')}}</el-button>

            <div class="login-link">
                {{$t('buttons.havAccount')}} ?
                <router-link :to="{ name: 'Login' }">{{$t('login')}}</router-link>
            </div>
        </el-form>
    </div>
</template>

<script setup lang="ts">
    import { ref } from 'vue'
    import { useRouter } from 'vue-router'
    import { Message, Lock } from '@element-plus/icons-vue'
    import { ElMessage } from 'element-plus'
    import { useAuthStore } from '@/stores/auth'
    import type { FormInstance } from 'element-plus'
    import axios from '@/utils/request'
    import axios1 from 'axios';
    const router = useRouter()
    const authStore = useAuthStore()
    const formRef = ref < FormInstance > ()
    const loading = ref(false)
    const sendingCode = ref(false)
    const countdown = ref(0)
    import { useI18n } from 'vue-i18n'
    const {t}=useI18n()
    const form = ref({
        email: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
        agreement: true
    })

    const validateOldPassword = (rule: any, value: string, callback: Function) => {
        if (value === '') {
            callback(new Error(t('oldPasswordRequired')))
        } else {
            callback()
        }
    }

    const validateNewPassword = (rule: any, value: string, callback: Function) => {
        if (value === '') {
            callback(new Error(t('newPasswordRequired')))
        } else {
            if (form.value.confirmPassword !== '') {
                if (formRef.value) {
                    formRef.value.validateField('confirmPassword')
                }
            }
            callback()
        }
    }

    const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
        if (value === '') {
            callback(new Error(t('confirmPasswordRequired')))
        } else if (value !== form.value.newPassword) {
            callback(new Error(t('passwordMismatch')))
        } else {
            callback()
        }
    }

    const validateAgreement = (rule: any, value: boolean, callback: Function) => {
        callback()
        // if (!value) {
        //     callback(new Error('请阅读并同意服务协议和隐私政策'))
        // } else {
        //     callback()
        // }
    }

    const rules = {
        email: [
            { required: true, message: t('msg4'), trigger: 'blur' },
            { type: 'email', message: t('msg5'), trigger: 'blur' }
        ],
        oldPassword: [
            { validator: validateOldPassword, trigger: 'blur' },
            { min: 6, message: t('msg6'), trigger: 'blur' }
        ],
        newPassword: [
            { validator: validateNewPassword, trigger: 'blur' },
            { min: 6, message: t('msg6'), trigger: 'blur' }
        ],
        confirmPassword: [
            { validator: validateConfirmPassword, trigger: 'blur' }
        ]
    }



    const handleSubmit = async () => {
        if (!formRef.value) return

        try {
            loading.value = true
            await formRef.value.validate()

            let datas = await axios.post('/api/auth/password/change',
                {
                    email: form.value.email,
                    oldPassword: form.value.oldPassword,
                    newPassword: form.value.newPassword
                }
            );
            if (datas.code == 200) {
                ElMessage.success(t('changePasswordSuccess'))
                router.push('/auth/login')
            } else {
                ElMessage.error(datas.msg)
            }

        } catch (error) {
            console.error('Password change failed:', error)
            ElMessage.error(t('changePasswordFailed'))
        } finally {
            loading.value = false
        }
    }
</script>

<style lang="scss" scoped>
    .register {
        .agreement {
            margin-bottom: 24px;
        }

        .submit-btn {
            width: 100%;
            margin-bottom: 16px;
        }

        .login-link {
            text-align: center;
            color: $text-regular;
            font-size: $font-size-small;

            a {
                color: $primary-color;
                text-decoration: none;
                margin-left: 4px;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
</style>