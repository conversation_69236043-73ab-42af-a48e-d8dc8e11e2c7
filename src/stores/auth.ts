import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const isAuthenticated = ref(!!token.value)

  const setToken = (newToken: string | null) => {
    token.value = newToken
    isAuthenticated.value = !!newToken

    if (newToken) {
      localStorage.setItem('token', newToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  const setUser = (newUser: User | null) => {
    user.value = newUser
  }

  const login = (data:any) => {
      if (data.code === 200) {
        setToken(data.token)
        // setUser(data.user)
        return true
      }
      throw new Error(data.msg)
  }
  const setUserInfo = (data:any) => {
    if (data.code === 200) {
      setUser(data.data)
      return true
    }
    throw new Error(data.msg)
}
  const loginss = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      })

      const data = await response.json()
      
      if (data.code === 200) {
        setToken(data.data.token)
        setUser(data.data.user)
        return true
      }

      throw new Error(data.message)
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  const register = async (formData: {
    email: string
    code: string
    password: string
  }) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.code === 200) {
        return true
      }

      throw new Error(data.message)
    } catch (error) {
      console.error('Register error:', error)
      throw error
    }
  }

  const sendVerificationCode = async (email: string) => {
    try {
      const response = await fetch('/api/auth/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      })

      const data = await response.json()

      if (data.code === 200) {
        return true
      }

      throw new Error(data.message)
    } catch (error) {
      console.error('Send code error:', error)
      throw error
    }
  }

  const logout = () => {
    setToken(null)
    setUser(null)
  }

  return {
    token,
    user,
    isAuthenticated,
    login,
    register,
    sendVerificationCode,
    logout,
    setUserInfo,
  }
}) 