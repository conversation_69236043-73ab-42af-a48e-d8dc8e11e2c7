
export default {
    title:'M DIY co-create community',
    home:'home',
    login:'login',
    sigin:'sigin',
    logOut:'login out',
    personal:'personal center',
    setting:'setting',
    hot:'hot',
    new:'new',
    verificationCode:'Verification code',
    confirmPassword:'Confirm password',
    getCode:'get Code',
    resetPassword:'reset Password',
    post:'post',
    selectCategory:'Select category',
    cancel:'cancel',
    confirm:'confirm',
    comment:'comment',
    Reply:'Reply',
    noData:'no data',
    collection:'collection',
    praise:'praise',
    edits:'edit',
    myPost:'my Post',
    myComment:'my Comment',

    myCollection:'my Collection',
    introduction:'introduction',
    avatar:'avatar',
    nick:'nick',
    save:'save',
    outmsg:'Are you sure you want to log out',
    tips:'tips',
    confirms:'confirm',

    msg1:'Please enter your password',
    msg2:'Please enter the password again',
    msg3:'The two password entries do not match!',
    msg4:'Please enter your email',
    msg5:'Please enter the correct email format',
    msg6:'The password must be at least 6 characters long',
    msg7:'password',
    msg8:'Please enter new password',

    oldPassword:'Old Password',
    newPassword:'New Password',
    oldPasswordPlaceholder:'Please enter your old password',
    newPasswordPlaceholder:'Please enter your new password',
    confirmPasswordPlaceholder:'Please enter your new password again',
    oldPasswordRequired:'Please enter your old password',
    newPasswordRequired:'Please enter your new password',
    confirmPasswordRequired:'Please enter your new password again',
    passwordMismatch:'The two new password entries do not match!',
    resetPasswordFailed:'Password reset failed, please try again',
    changePassword:'Change Password',
    changePasswordSuccess:'Password changed successfully',
    changePasswordFailed:'Password change failed, please try again',
    loginFailed:'Login failed, please check your email and password',
    pmsg1:'Please enter a title',
    pmsg2:'The title length should be between 2 and 100 characters',
    pmsg3:'Please select a category',
    font:'font',
    upImg:'Click the image to add to the post content',
    fileSize:'File size',
    fileavailable:'Extensions are available',
    lessThan:'Less than',
    upFile:'Click Attachment to add to post content',
    expression:'expression',
    image:'image',
    annex:'annex',
    contacts:'Contact the backend email: peakhy' + '@' + '126.com or 598090079' + '@' + 'qq.com',

    posts:{
        postMessage:'Post a message',

        title:'title',
        description:'description',
        catogory:'catogory',
        label:'label',
        content:'content'
    },
    routers:{
        logOut:'Log Out',
        restore:'Restore data',
        首页:'home page',
        系统管理:'system',
        机构管理:'organizational',
        用户管理:'user',
        角色管理:'role',
        权限管理:'authority',
        '测试1-1-1':'test',
        商品管理:'commodity',
        手机分类:'mobile',
        分类管理:'classify',
        分类测试:'testes',
        系统工具:'until',
        日志管理:'day',
        接口文档:'nnterface ',
    },
    buttons:{
        login:'login',
        sigin:'sigin',
        email:'email',
        password:'password',
        forgotPassword :'Forgot password',
        rememberMe:'Remember me',
        noAccount:'No account',
        havAccount:'haved account',

    }

}
