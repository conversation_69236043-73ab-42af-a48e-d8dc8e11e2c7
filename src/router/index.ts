import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/auth',
    name: 'Auth',
    component: () => import('@/views/auth/index.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/login.vue'),
        meta: {
          title: 'Douguo M DIYer'
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/register.vue'),
        meta: {
          title: 'Douguo M DIYer'
        }
      },
      {
        path: 'resetpassword',
        name: 'ResetPassword',
        component: () => import('@/views/auth/reset-password.vue'),
        meta: {
          title: '<PERSON>uo M DIYer'
        }
      }
    ]
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: {
      title: '<PERSON><PERSON> M DIYer'
    }
  },
  {
    path: '/mySpeak',
    name: 'mySpeak',
    component: () => import('@/views/my-speak/index.vue'),
    meta: {
      title: 'Douguo M DIYer'
    }
  },
  {
    path: '/create-post',
    name: 'CreatePost',
    component: () => import('@/views/create-post/index.vue'),
    meta: {
      title: 'create-post',
      requiresAuth: true
    }
  },
  {
    path: '/create-edit/:id',
    name: 'CreatePostEdit',
    component: () => import('@/views/create-post/edits.vue'),
    meta: {
      title: 'Douguo M DIYer',
      requiresAuth: true
    }
  },
  {
    path: '/post/:id',
    name: 'PostDetail',
    component: () => import('@/views/post/index.vue'),
    meta: {
      title: 'Douguo M DIYer'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/profile/index.vue'),
    meta: {
      title: 'Douguo M DIYer',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard for auth and setting page title
router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title || 'M DIY co-create community'}`
  
  if (to.meta.requiresAuth && !localStorage.getItem('token')) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router 