export interface Author {
  id: number
  name: string
  avatar: string
}

export interface Post {
  id: number
  title: string
  content: string
  author: Author
  category: string
  tags: string[]
  likes: number
  comments: number
  views: number
  createTime: string
}

export interface User {
  userId: number
  nickname: string
  avatar: string
  email: string
  username: string
  postCount?: number
  commentCount?: number
  likeCount?: number
}

export interface Comment {
  id: number
  content: string
  author: Author
  createTime: string
  likes: number
  replies?: Comment[]
}

export interface ApiResponse<T> {
  code: number
  data: T
  message?: string
} 