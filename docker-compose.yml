version: '3'

services:
  # 应用服务
  blog-admin:
    image: blog-admin:latest
    container_name: blog-admin
    restart: always
    ports:
      - "8080:8080"
    environment:
      # 数据库连接配置 - 使用外部数据库
      - SPRING_DATASOURCE_DRUID_MASTER_URL=*********************************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_MASTER_USERNAME=root
      - SPRING_DATASOURCE_DRUID_MASTER_PASSWORD=123456
      # Redis 配置 - 使用外部Redis
      - SPRING_REDIS_HOST=localhost
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_DATABASE=0
      # 日志配置
      - LOGGING_FILE_PATH=/app/logs
      - LOGGING_LEVEL_COM_BLOG=debug
      # 字符编码配置
      - LANG=zh_CN.UTF-8
      - LC_ALL=zh_CN.UTF-8
      - JAVA_OPTS=-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.language=zh -Duser.region=CN -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8 -Dlogback.charset=UTF-8
    volumes:
      - /data/docker/server/blog-admin/logs:/app/logs
      - /data/docker/server/blog-admin/uploadPath:/app/uploadPath
    network_mode: "host"

