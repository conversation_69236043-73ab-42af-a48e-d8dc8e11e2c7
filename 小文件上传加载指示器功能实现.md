# 小文件上传加载指示器功能实现

## 需求背景

在文件上传功能中：
- **大于10MB的文件**：使用分片上传，有进度条显示
- **小于10MB的文件**：使用普通上传，缺少用户交互体验

现在需要为小于10MB的文件添加加载指示器，提升用户体验。

## 技术环境

### B端 (blog-ui)
- **Vue版本**：Vue 2
- **UI框架**：Element UI
- **文件位置**：`blog-ui/src/views/forum/postList/index.vue`
- **编辑器组件**：`blog-ui/src/components/TiptapEditor/index.vue`

### C端 (block)
- **Vue版本**：Vue 3
- **UI框架**：Element Plus
- **文件位置**：`block/src/views/create-post/index.vue`

## 实现方案

### 1. B端实现 (Vue 2 + Element UI)

#### 图片上传加载指示器
```javascript
// 小于等于10MB，使用普通上传
console.log('文件大小小于10MB，使用普通上传')

// 显示简单的加载提示
this.$message({
  message: '正在上传图片...',
  type: 'info',
  duration: 0,
  showClose: false,
  customClass: 'upload-loading-message'
})

url = await simpleUpload(file, this.uploadUrl)

// 关闭加载提示
this.$message.closeAll()
```

#### 文件上传加载指示器
```javascript
// 小于等于10MB，使用普通上传
console.log('文件大小小于10MB，使用普通上传')

// 显示简单的加载提示
this.$message({
  message: '正在上传文件...',
  type: 'info',
  duration: 0,
  showClose: false,
  customClass: 'upload-loading-message'
})

url = await simpleUpload(file, this.uploadUrl)

// 关闭加载提示
this.$message.closeAll()
```

#### B端CSS样式
```scss
/* 上传加载提示样式 */
:deep(.upload-loading-message) {
  .el-message__content {
    display: flex;
    align-items: center;
  }
  
  .el-message__content::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #409EFF;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 2. C端实现 (Vue 3 + Element Plus)

#### 图片上传加载指示器
```javascript
// 小文件使用普通上传

// 显示加载提示
ElMessage({
  message: '正在上传图片...',
  type: 'info',
  duration: 0,
  showClose: false,
  customClass: 'upload-loading-message'
})

const formData = new FormData()
formData.append('file', file)
const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)

// 关闭加载提示
ElMessage.closeAll()

if (data.code == 200 && data.url) {
  editor.value?.chain().focus().setImage({ src: data.url }).run()
}
```

#### 文件上传加载指示器
```javascript
// 小文件使用普通上传

// 显示加载提示
ElMessage({
  message: '正在上传文件...',
  type: 'info',
  duration: 0,
  showClose: false,
  customClass: 'upload-loading-message'
})

const formData = new FormData()
formData.append('file', file)
const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)

// 关闭加载提示
ElMessage.closeAll()

if (data.code == 200 && data.url) {
  // 处理上传成功逻辑
}
```

#### C端CSS样式
```scss
/* 上传加载提示样式 */
:deep(.upload-loading-message) {
  .el-message__content {
    display: flex;
    align-items: center;
  }
  
  .el-message__content::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #409EFF;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

## 功能特点

### 1. 视觉效果
- **转圈动画**：使用CSS动画实现的旋转加载指示器
- **蓝色主题**：与Element UI/Plus的主题色保持一致
- **合适大小**：16x16px的加载图标，不会过于突兀

### 2. 用户体验
- **即时反馈**：文件选择后立即显示加载状态
- **明确提示**：区分图片和文件上传的提示文本
- **自动关闭**：上传完成后自动关闭加载提示

### 3. 技术实现
- **无阻塞**：duration设为0，不会自动消失
- **不可关闭**：showClose设为false，避免用户误操作
- **自定义样式**：使用customClass添加转圈动画

## 修改的文件

### B端文件
- `blog-ui/src/components/TiptapEditor/index.vue`
  - 第1273-1285行：图片上传加载指示器
  - 第1439-1451行：文件上传加载指示器
  - 第2913-2936行：CSS样式

### C端文件
- `block/src/views/create-post/index.vue`
  - 第472-486行：图片上传加载指示器
  - 第605-619行：文件上传加载指示器
  - 第1061-1084行：CSS样式

## 使用场景

### 适用情况
- 文件大小 < 10MB
- 网络较慢时的用户体验优化
- 需要明确上传状态反馈的场景

### 不适用情况
- 文件大小 ≥ 10MB（使用分片上传进度条）
- 网络极快的情况（加载指示器可能一闪而过）

## 测试验证

### 测试用例

1. **小图片上传**：
   - 选择 < 10MB 的图片
   - 验证显示"正在上传图片..."
   - 验证有转圈动画
   - 验证上传完成后自动关闭

2. **小文件上传**：
   - 选择 < 10MB 的文件
   - 验证显示"正在上传文件..."
   - 验证有转圈动画
   - 验证上传完成后自动关闭

3. **大文件上传**：
   - 选择 ≥ 10MB 的文件
   - 验证使用分片上传进度条
   - 验证不显示简单加载指示器

### 预期结果

- ✅ 小文件上传有明确的加载反馈
- ✅ 加载指示器样式美观
- ✅ 上传完成后状态正确重置
- ✅ 不影响大文件的分片上传功能

## 优化建议

### 1. 错误处理优化
```javascript
try {
  // 上传逻辑
} catch (error) {
  ElMessage.closeAll() // 确保错误时也关闭加载提示
  ElMessage.error('上传失败')
}
```

### 2. 超时处理
```javascript
// 可以考虑添加超时机制
setTimeout(() => {
  ElMessage.closeAll()
  ElMessage.error('上传超时')
}, 30000) // 30秒超时
```

### 3. 多文件上传
```javascript
// 对于多文件上传，可以显示当前进度
ElMessage({
  message: `正在上传文件 (${currentIndex}/${totalFiles})...`,
  // ...
})
```

现在小文件上传已经有了良好的用户交互体验，用户可以清楚地知道文件正在上传中。
