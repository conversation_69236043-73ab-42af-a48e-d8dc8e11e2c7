# 评论附件功能测试文档

## 功能概述
为评论系统添加附件支持，复用现有的 `forum_post_attachment` 表，通过 `content_type` 字段区分不同类型的附件。

## 数据库变更

### 复用现有表：forum_post_attachment
```sql
-- 现有表结构，无需修改
CREATE TABLE `forum_post_attachment` (
  `attachment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `post_id` bigint NOT NULL COMMENT '帖子ID/评论ID',
  `content_type` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '内容类型(0帖子内容 1用户帖子内容 forum_comment评论内容)',
  `file_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件名称',
  `file_url` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '文件URL',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件类型',
  `create_time` datetime DEFAULT NULL COMMENT '上传时间',
  PRIMARY KEY (`attachment_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_content_type` (`content_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='帖子附件表';
```

### 评论附件存储规则
- `post_id` 字段存储评论ID
- `content_type` 字段设置为 `'forum_comment'`

## API接口变更

### 1. 发表评论接口 POST /api/comment

**请求参数示例：**
```json
{
  "contentType": "0",
  "contentId": 1,
  "parentId": 0,
  "content": "这是一条带附件的评论",
  "attachments": [
    {
      "fileName": "document.pdf",
      "fileUrl": "http://localhost:8080/profile/upload/2025/05/26/document.pdf",
      "fileSize": 1024000,
      "fileType": "application/pdf"
    },
    {
      "fileName": "image.jpg",
      "fileUrl": "http://localhost:8080/profile/upload/2025/05/26/image.jpg",
      "fileSize": 512000,
      "fileType": "image/jpeg"
    }
  ]
}
```

**参数说明：**
- `contentType`: 内容类型（0帖子 1用户发帖）
- `contentId`: 内容ID
- `parentId`: 父评论ID（0表示一级评论）
- `content`: 评论内容
- `attachments`: 附件列表（可选）
  - `fileName`: 文件名称
  - `fileUrl`: 文件URL
  - `fileSize`: 文件大小（字节）
  - `fileType`: 文件类型

### 2. 获取评论列表接口 GET /api/comment/list

**响应数据示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "commentId": 1,
      "contentType": "0",
      "contentId": 1,
      "parentId": 0,
      "content": "这是一条带附件的评论",
      "userId": 1,
      "userName": "admin",
      "userAvatar": "http://localhost:8080/profile/avatar/default.jpg",
      "likeCount": 0,
      "isLiked": false,
      "createTime": "2025-05-26 10:30:00",
      "attachments": [
        {
          "attachmentId": 1,
          "fileName": "document.pdf",
          "fileUrl": "http://localhost:8080/profile/upload/2025/05/26/document.pdf",
          "fileSize": 1024000,
          "fileType": "application/pdf"
        },
        {
          "attachmentId": 2,
          "fileName": "image.jpg",
          "fileUrl": "http://localhost:8080/profile/upload/2025/05/26/image.jpg",
          "fileSize": 512000,
          "fileType": "image/jpeg"
        }
      ],
      "replies": []
    }
  ],
  "total": 1
}
```

## 测试步骤

### 1. 数据库准备
1. 确保 `forum_post_attachment` 表存在（无需额外创建表）
2. 确保数据库连接正常

### 2. 发表带附件的评论
```bash
curl -X POST "http://localhost:8080/api/comment" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "contentType": "0",
    "contentId": 1,
    "parentId": 0,
    "content": "这是一条带附件的评论",
    "attachments": [
      {
        "fileName": "test.pdf",
        "fileUrl": "http://localhost:8080/profile/upload/2025/05/26/test.pdf",
        "fileSize": 1024000,
        "fileType": "application/pdf"
      }
    ]
  }'
```

### 3. 查询评论列表
```bash
curl -X GET "http://localhost:8080/api/comment/list?contentType=0&contentId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 验证点
- [ ] 评论成功创建
- [ ] 附件信息正确保存到 `forum_post_attachment` 表，`content_type` 为 `'forum_comment'`
- [ ] 评论列表返回附件信息
- [ ] 回复评论也支持附件
- [ ] 删除评论时同时删除附件
- [ ] 数据库中评论附件与帖子附件正确区分

## 注意事项

1. **文件上传**：附件需要先通过文件上传接口上传，获得fileUrl后再提交评论
2. **文件类型**：建议限制允许的文件类型和大小
3. **权限控制**：确保用户只能删除自己的评论和附件
4. **存储清理**：删除评论时需要同时清理对应的附件文件

## 错误处理

- 评论内容为空：返回400错误
- 用户未登录：返回401错误
- 附件URL无效：返回400错误
- 数据库操作失败：返回500错误
