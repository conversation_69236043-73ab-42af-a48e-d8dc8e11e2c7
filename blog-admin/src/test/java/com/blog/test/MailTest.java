package com.blog.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;

@SpringBootTest
public class MailTest {
    @Autowired
    private JavaMailSender mailSender;

    @Test
    void testSend() {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom("<EMAIL>"); // ����������һ��
        message.setTo("<EMAIL>");
        message.setSubject("����");
        message.setText("��������");

        try {
            mailSender.send(message);
            System.out.println("���ͳɹ�");
        } catch (MailException e) {
            e.printStackTrace();
            System.out.println("����ʧ��: " + e.getMessage());
        }
    }
}
