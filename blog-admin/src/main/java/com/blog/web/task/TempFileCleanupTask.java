package com.blog.web.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.stream.Stream;

/**
 * 临时文件清理任务
 * 定期清理上传过程中产生的临时文件
 */
@Component
public class TempFileCleanupTask {
    private static final Logger log = LoggerFactory.getLogger(TempFileCleanupTask.class);
    
    @Value("${spring.servlet.multipart.location:${java.io.tmpdir}/blog-upload-temp}")
    private String tempLocation;
    
    // 文件过期时间，24小时
    private static final long FILE_EXPIRY_HOURS = 24;
    
    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupTempFiles() {
        log.info("开始清理临时文件...");
        
        try {
            Path tempDir = Paths.get(tempLocation);
            
            // 如果目录不存在，创建它
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
                log.info("临时目录不存在，已创建: {}", tempDir);
                return;
            }
            
            // 获取当前时间
            Instant now = Instant.now();
            int deletedCount = 0;
            
            // 遍历临时目录中的所有文件
            try (Stream<Path> paths = Files.walk(tempDir)) {
                deletedCount = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        try {
                            // 获取文件的最后修改时间
                            Instant lastModified = Files.getLastModifiedTime(path).toInstant();
                            // 计算文件的存在时间
                            Duration duration = Duration.between(lastModified, now);
                            // 如果文件存在时间超过了过期时间，则删除
                            return duration.toHours() >= FILE_EXPIRY_HOURS;
                        } catch (Exception e) {
                            log.warn("获取文件 {} 的最后修改时间失败", path, e);
                            return false;
                        }
                    })
                    .mapToInt(path -> {
                        try {
                            // 尝试删除文件
                            Files.delete(path);
                            log.debug("已删除临时文件: {}", path);
                            return 1;
                        } catch (Exception e) {
                            log.warn("删除临时文件 {} 失败", path, e);
                            return 0;
                        }
                    })
                    .sum();
            }
            
            log.info("临时文件清理完成，共删除 {} 个文件", deletedCount);
        } catch (Exception e) {
            log.error("清理临时文件时发生错误", e);
        }
    }
    
    /**
     * 应用启动时执行一次清理
     */
    //@PostConstruct
    public void cleanupOnStartup() {
        log.info("应用启动，执行临时文件清理...");
        cleanupTempFiles();
    }
}
