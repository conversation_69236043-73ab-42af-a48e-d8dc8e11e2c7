package com.blog.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.enums.BusinessType;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.system.domain.SensitiveWord;
import com.blog.system.service.ISensitiveWordService;

/**
 * 敏感词管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/word")
public class SysWordController extends BaseController
{
    @Autowired
    private ISensitiveWordService sensitiveWordService;

    /**
     * 查询敏感词列表
     */
    @PreAuthorize("@ss.hasPermi('system:word:list')")
    @GetMapping("/list")
    public TableDataInfo list(SensitiveWord sensitiveWord)
    {
        startPage();
        List<SensitiveWord> list = sensitiveWordService.selectSensitiveWordList(sensitiveWord);
        return getDataTable(list);
    }

    /**
     * 导出敏感词列表
     */
    @PreAuthorize("@ss.hasPermi('system:word:export')")
    @Log(title = "敏感词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SensitiveWord sensitiveWord)
    {
        List<SensitiveWord> list = sensitiveWordService.selectSensitiveWordList(sensitiveWord);
        ExcelUtil<SensitiveWord> util = new ExcelUtil<SensitiveWord>(SensitiveWord.class);
        util.exportExcel(response, list, "敏感词数据");
    }

    /**
     * 获取敏感词详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:word:query')")
    @GetMapping(value = "/{wordId}")
    public AjaxResult getInfo(@PathVariable("wordId") Long wordId)
    {
        return success(sensitiveWordService.selectSensitiveWordById(wordId));
    }

    /**
     * 新增敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:add')")
    @Log(title = "敏感词", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SensitiveWord sensitiveWord)
    {
        return toAjax(sensitiveWordService.insertSensitiveWord(sensitiveWord));
    }

    /**
     * 修改敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:edit')")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SensitiveWord sensitiveWord)
    {
        return toAjax(sensitiveWordService.updateSensitiveWord(sensitiveWord));
    }

    /**
     * 删除敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:remove')")
    @Log(title = "敏感词", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wordIds}")
    public AjaxResult remove(@PathVariable Long[] wordIds)
    {
        return toAjax(sensitiveWordService.deleteSensitiveWordByIds(wordIds));
    }

    /**
     * 刷新敏感词缓存
     */
    @PreAuthorize("@ss.hasPermi('system:word:refresh')")
    @Log(title = "敏感词", businessType = BusinessType.CLEAN)
    @GetMapping("/refresh")
    public AjaxResult refresh()
    {
        sensitiveWordService.initSensitiveWordCache();
        return success();
    }

    /**
     * 设置敏感词指定状态
     */
    @PreAuthorize("@ss.hasPermi('system:word:edit')")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @PutMapping("/specified")
    public AjaxResult setSpecified(@RequestBody SensitiveWord sensitiveWord)
    {
        return toAjax(sensitiveWordService.updateSensitiveWordSpecified(sensitiveWord.getWordId(), sensitiveWord.getIsSpecified()));
    }

    /**
     * 批量设置指定敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:edit')")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSpecified")
    public AjaxResult batchSetSpecified(@RequestBody List<String> words)
    {
        return toAjax(sensitiveWordService.batchSetSpecifiedWords(words));
    }

    /**
     * 获取所有指定的敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:list')")
    @GetMapping("/specified")
    public AjaxResult getSpecifiedWords()
    {
        return success(sensitiveWordService.getSpecifiedSensitiveWords());
    }

    /**
     * 导入敏感词
     */
    @PreAuthorize("@ss.hasPermi('system:word:import')")
    @Log(title = "敏感词", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SensitiveWord> util = new ExcelUtil<SensitiveWord>(SensitiveWord.class);
        List<SensitiveWord> wordList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = sensitiveWordService.importSensitiveWord(wordList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    public void importTemplate(HttpServletResponse response)
    {
        try
        {
            ExcelUtil<SensitiveWord> util = new ExcelUtil<SensitiveWord>(SensitiveWord.class);
            util.importTemplateExcel(response, "敏感词数据");
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
}
