package com.blog.web.controller.forum;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.blog.forum.util.LangUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumCategoryI18n;
import com.blog.forum.domain.ForumPostCategory;
import com.blog.forum.mapper.ForumCategoryI18nMapper;
import com.blog.forum.service.IForumPostCategoryService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 帖子分类Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/postCategory")
public class ForumPostCategoryController extends BaseController
{
    @Autowired
    private IForumPostCategoryService forumPostCategoryService;

    @Autowired
    private ForumCategoryI18nMapper forumCategoryI18nMapper;

    /**
     * 查询帖子分类列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumPostCategory forumPostCategory)
    {
        startPage();

        // 获取当前语言ID，默认使用英语
        Long langId = LangUtils.getCurrentLangId();

        // 调用支持多语言的查询方法
        List<ForumPostCategory> list = forumPostCategoryService.selectForumPostCategoryListWithI18n(forumPostCategory, langId);

        return getDataTable(list);
    }

    /**
     * 导出帖子分类列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:export')")
    @Log(title = "帖子分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumPostCategory forumPostCategory)
    {
        List<ForumPostCategory> list = forumPostCategoryService.selectForumPostCategoryList(forumPostCategory);
        ExcelUtil<ForumPostCategory> util = new ExcelUtil<ForumPostCategory>(ForumPostCategory.class);
        util.exportExcel(response, list, "帖子分类数据");
    }

    /**
     * 获取帖子分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        // 记录日志
        System.out.println("获取分类详情，categoryId: " + categoryId);

        // 获取分类基本信息
        ForumPostCategory category = forumPostCategoryService.selectForumPostCategoryByCategoryId(categoryId);
        if (category == null) {
            return AjaxResult.error("分类不存在");
        }

        // 确保排序字段不为空
        if (category.getSort() == null) {
            category.setSort(0L);  // 设置默认值
        }

        // 获取分类的多语言信息
        ForumCategoryI18n queryParam = new ForumCategoryI18n();
        queryParam.setCategoryId(categoryId);
        List<ForumCategoryI18n> i18nList = forumCategoryI18nMapper.selectForumCategoryI18nList(queryParam);

        // 记录日志
        System.out.println("多语言数据数量: " + (i18nList != null ? i18nList.size() : 0));

        // 将多语言数据转换为以语言代码为键的Map
        Map<String, Map<String, Object>> i18nMap = new java.util.HashMap<>();

        // 获取所有支持的语言
        List<com.blog.forum.domain.ForumLanguage> languages = forumPostCategoryService.getAllLanguages();

        // 记录日志
        System.out.println("支持的语言数量: " + (languages != null ? languages.size() : 0));

        // 为每种语言创建一个条目
        for (com.blog.forum.domain.ForumLanguage language : languages) {
            String langCode = language.getLangCode();
            Long langId = language.getLangId();

            // 记录日志
            System.out.println("处理语言: " + langCode + ", ID: " + langId);

            // 查找该语言的多语言数据
            java.util.Optional<ForumCategoryI18n> i18nOpt = i18nList.stream()
                    .filter(i18n -> i18n.getLangId().equals(langId))
                    .findFirst();

            Map<String, Object> langData = new java.util.HashMap<>();
            langData.put("langId", langId);

            if (i18nOpt.isPresent()) {
                // 如果找到了多语言数据，使用它
                langData.put("categoryName", i18nOpt.get().getCategoryName());
                System.out.println("语言 " + langCode + " 找到数据: " + i18nOpt.get().getCategoryName());
            } else {
                // 如果没有找到多语言数据，使用空字符串
                langData.put("categoryName", "");
                System.out.println("语言 " + langCode + " 没有找到数据");
            }

            i18nMap.put(langCode, langData);
        }

        // 将多语言数据添加到返回结果中
        if (category.getParams() == null) {
            category.setParams(new java.util.HashMap<>());
        }
        category.getParams().put("i18n", i18nMap);

        // 记录日志
        System.out.println("返回的数据: " + category);

        return AjaxResult.success(category);
    }

    /**
     * 新增帖子分类
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:add')")
    @Log(title = "帖子分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> requestData)
    {
        // 从请求中提取基本分类信息
        ForumPostCategory forumPostCategory = new ForumPostCategory();

        // 设置基本属性
        if (requestData.containsKey("categoryName")) {
            forumPostCategory.setCategoryName((String) requestData.get("categoryName"));
        }
        if (requestData.containsKey("sort")) {
            forumPostCategory.setSort(Long.valueOf(requestData.get("sort").toString()));
        }
        if (requestData.containsKey("status")) {
            forumPostCategory.setStatus((String) requestData.get("status"));
        }
        if (requestData.containsKey("remark")) {
            forumPostCategory.setRemark((String) requestData.get("remark"));
        }

        // 插入基本分类信息
        int rows = forumPostCategoryService.insertForumPostCategory(forumPostCategory);

        Long currentLangId = LangUtils.getCurrentLangId();

        // 如果插入成功且包含多语言数据，则处理多语言信息
        if (rows > 0 && requestData.containsKey("i18n")) {
            Map<String, Map<String, Object>> i18nData = (Map<String, Map<String, Object>>) requestData.get("i18n");
            for (Map.Entry<String, Map<String, Object>> entry : i18nData.entrySet()) {
                String langCode = entry.getKey();
                Map<String, Object> langData = entry.getValue();

                // 创建多语言对象
                ForumCategoryI18n i18n = new ForumCategoryI18n();
                i18n.setCategoryId(forumPostCategory.getCategoryId());

                Long langId = null;
                // 设置语言ID
                if (langData.containsKey("langId")) {
                    langId = Long.valueOf(langData.get("langId").toString());

                    i18n.setLangId(langId);
                } else {
                    // 如果没有提供语言ID，则根据语言代码获取
                    langId = forumPostCategoryService.getLangIdByCode(langCode);
                    if (langId != null) {
                        i18n.setLangId(langId);
                    } else {
                        continue; // 跳过无效的语言代码
                    }
                }

                String categoryName = "";
                // 设置分类名称
                if (langData.containsKey("categoryName")) {
                    categoryName = (String) langData.get("categoryName");
                    i18n.setCategoryName(categoryName);
                }
                if (langId != null && langId.equals(currentLangId) && StrUtil.isEmpty(categoryName)) {
                    forumPostCategory.setCategoryName(categoryName);
                }


                // 插入多语言数据
                forumCategoryI18nMapper.insertForumCategoryI18n(i18n);
            }
        }

        return toAjax(rows);
    }

    /**
     * 修改帖子分类
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:edit')")
    @Log(title = "帖子分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Map<String, Object> requestData)
    {
        // 从请求中提取基本分类信息
        ForumPostCategory forumPostCategory = new ForumPostCategory();

        // 设置分类ID
        if (requestData.containsKey("categoryId")) {
            forumPostCategory.setCategoryId(Long.valueOf(requestData.get("categoryId").toString()));
        } else {
            return AjaxResult.error("缺少分类ID");
        }

        // 设置基本属性
        if (requestData.containsKey("categoryName")) {
            forumPostCategory.setCategoryName((String) requestData.get("categoryName"));
        }
        if (requestData.containsKey("sort")) {
            forumPostCategory.setSort(Long.valueOf(requestData.get("sort").toString()));
        }
        if (requestData.containsKey("status")) {
            forumPostCategory.setStatus((String) requestData.get("status"));
        }
        if (requestData.containsKey("remark")) {
            forumPostCategory.setRemark((String) requestData.get("remark"));
        }

        // 更新基本分类信息
        int rows = forumPostCategoryService.updateForumPostCategory(forumPostCategory);

        // 如果更新成功且包含多语言数据，则处理多语言信息
        if (rows > 0 && requestData.containsKey("i18n")) {
            Map<String, Map<String, Object>> i18nData = (Map<String, Map<String, Object>>) requestData.get("i18n");
            for (Map.Entry<String, Map<String, Object>> entry : i18nData.entrySet()) {
                String langCode = entry.getKey();
                Map<String, Object> langData = entry.getValue();

                // 获取语言ID
                Long langId = null;
                if (langData.containsKey("langId")) {
                    langId = Long.valueOf(langData.get("langId").toString());
                } else {
                    // 如果没有提供语言ID，则根据语言代码获取
                    langId = forumPostCategoryService.getLangIdByCode(langCode);
                    if (langId == null) {
                        continue; // 跳过无效的语言代码
                    }
                }

                // 查询是否已存在该语言的分类翻译
                ForumCategoryI18n queryParam = new ForumCategoryI18n();
                queryParam.setCategoryId(forumPostCategory.getCategoryId());
                queryParam.setLangId(langId);
                List<ForumCategoryI18n> existingI18n = forumCategoryI18nMapper.selectForumCategoryI18nList(queryParam);

                // 创建或更新多语言对象
                ForumCategoryI18n i18n = new ForumCategoryI18n();
                i18n.setCategoryId(forumPostCategory.getCategoryId());
                i18n.setLangId(langId);

                // 设置分类名称
                if (langData.containsKey("categoryName")) {
                    i18n.setCategoryName((String) langData.get("categoryName"));
                }

                // 插入或更新多语言数据
                if (existingI18n.isEmpty()) {
                    // 插入新的多语言数据
                    forumCategoryI18nMapper.insertForumCategoryI18n(i18n);
                } else {
                    // 更新现有的多语言数据
                    i18n.setI18nId(existingI18n.get(0).getI18nId());
                    forumCategoryI18nMapper.updateForumCategoryI18n(i18n);
                }
            }
        }

        return toAjax(rows);
    }

    /**
     * 删除帖子分类
     */
    @PreAuthorize("@ss.hasPermi('forum:postCategory:remove')")
    @Log(title = "帖子分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        // 删除分类的多语言数据
        for (Long categoryId : categoryIds) {
            ForumCategoryI18n queryParam = new ForumCategoryI18n();
            queryParam.setCategoryId(categoryId);
            List<ForumCategoryI18n> i18nList = forumCategoryI18nMapper.selectForumCategoryI18nList(queryParam);
            for (ForumCategoryI18n i18n : i18nList) {
                forumCategoryI18nMapper.deleteForumCategoryI18nByI18nId(i18n.getI18nId());
            }
        }

        // 删除分类基本信息
        return toAjax(forumPostCategoryService.deleteForumPostCategoryByCategoryIds(categoryIds));
    }
}
