package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumUserPost;
import com.blog.forum.service.IForumUserPostService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 用户发帖Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/userPost")
public class ForumUserPostController extends BaseController
{
    @Autowired
    private IForumUserPostService forumUserPostService;

    /**
     * 查询用户发帖列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumUserPost forumUserPost)
    {
        startPage();
        List<ForumUserPost> list = forumUserPostService.selectForumUserPostList(forumUserPost);
        return getDataTable(list);
    }

    /**
     * 导出用户发帖列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:export')")
    @Log(title = "用户发帖", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumUserPost forumUserPost)
    {
        List<ForumUserPost> list = forumUserPostService.selectForumUserPostList(forumUserPost);
        ExcelUtil<ForumUserPost> util = new ExcelUtil<ForumUserPost>(ForumUserPost.class);
        util.exportExcel(response, list, "用户发帖数据");
    }

    /**
     * 获取用户发帖详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:query')")
    @GetMapping(value = "/{userPostId}")
    public AjaxResult getInfo(@PathVariable("userPostId") Long userPostId)
    {
        return success(forumUserPostService.selectForumUserPostByUserPostId(userPostId));
    }

    /**
     * 新增用户发帖
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:add')")
    @Log(title = "用户发帖", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumUserPost forumUserPost)
    {
        return toAjax(forumUserPostService.insertForumUserPost(forumUserPost));
    }

    /**
     * 修改用户发帖
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:edit')")
    @Log(title = "用户发帖", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumUserPost forumUserPost)
    {
        return toAjax(forumUserPostService.updateForumUserPost(forumUserPost));
    }

    /**
     * 删除用户发帖
     */
    @PreAuthorize("@ss.hasPermi('forum:userPost:remove')")
    @Log(title = "用户发帖", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userPostIds}")
    public AjaxResult remove(@PathVariable Long[] userPostIds)
    {
        return toAjax(forumUserPostService.deleteForumUserPostByUserPostIds(userPostIds));
    }
}
