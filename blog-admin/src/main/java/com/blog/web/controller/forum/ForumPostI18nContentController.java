package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumPostI18nContent;
import com.blog.forum.service.IForumPostI18nContentService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 多语言帖子内容Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/postI18nContent")
public class ForumPostI18nContentController extends BaseController
{
    @Autowired
    private IForumPostI18nContentService forumPostI18nContentService;

    /**
     * 查询多语言帖子内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumPostI18nContent forumPostI18nContent)
    {
        startPage();
        List<ForumPostI18nContent> list = forumPostI18nContentService.selectForumPostI18nContentList(forumPostI18nContent);
        return getDataTable(list);
    }

    /**
     * 导出多语言帖子内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:export')")
    @Log(title = "多语言帖子内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumPostI18nContent forumPostI18nContent)
    {
        List<ForumPostI18nContent> list = forumPostI18nContentService.selectForumPostI18nContentList(forumPostI18nContent);
        ExcelUtil<ForumPostI18nContent> util = new ExcelUtil<ForumPostI18nContent>(ForumPostI18nContent.class);
        util.exportExcel(response, list, "多语言帖子内容数据");
    }

    /**
     * 获取多语言帖子内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:query')")
    @GetMapping(value = "/{contentId}")
    public AjaxResult getInfo(@PathVariable("contentId") Long contentId)
    {
        return success(forumPostI18nContentService.selectForumPostI18nContentByContentId(contentId));
    }

    /**
     * 新增多语言帖子内容
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:add')")
    @Log(title = "多语言帖子内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumPostI18nContent forumPostI18nContent)
    {
        return toAjax(forumPostI18nContentService.insertForumPostI18nContent(forumPostI18nContent));
    }

    /**
     * 修改多语言帖子内容
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:edit')")
    @Log(title = "多语言帖子内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumPostI18nContent forumPostI18nContent)
    {
        return toAjax(forumPostI18nContentService.updateForumPostI18nContent(forumPostI18nContent));
    }

    /**
     * 删除多语言帖子内容
     */
    @PreAuthorize("@ss.hasPermi('forum:postI18nContent:remove')")
    @Log(title = "多语言帖子内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{contentIds}")
    public AjaxResult remove(@PathVariable Long[] contentIds)
    {
        return toAjax(forumPostI18nContentService.deleteForumPostI18nContentByContentIds(contentIds));
    }
}
