package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumComment;
import com.blog.forum.domain.dto.AdminCommentQueryDTO;
import com.blog.forum.domain.vo.CommentDetailVO;
import com.blog.forum.service.IForumCommentService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 评论Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/comment")
public class ForumCommentController extends BaseController
{
    @Autowired
    private IForumCommentService forumCommentService;

    /**
     * 查询评论列表
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminCommentQueryDTO queryDTO)
    {
        startPage();
        List<CommentDetailVO> list = forumCommentService.selectAdminCommentList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 查询评论的回复列表
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:list')")
    @GetMapping("/replies/{parentId}")
    public AjaxResult getReplies(@PathVariable("parentId") Long parentId)
    {
        List<CommentDetailVO> list = forumCommentService.selectCommentReplies(parentId);
        return success(list);
    }

    /**
     * 导出评论列表
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:export')")
    @Log(title = "评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumComment forumComment)
    {
        List<ForumComment> list = forumCommentService.selectForumCommentList(forumComment);
        ExcelUtil<ForumComment> util = new ExcelUtil<ForumComment>(ForumComment.class);
        util.exportExcel(response, list, "评论数据");
    }

    /**
     * 获取评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:query')")
    @GetMapping(value = "/{commentId}")
    public AjaxResult getInfo(@PathVariable("commentId") Long commentId)
    {
        return success(forumCommentService.selectForumCommentByCommentId(commentId));
    }

    /**
     * 新增评论
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:add')")
    @Log(title = "评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumComment forumComment)
    {
        return toAjax(forumCommentService.insertForumComment(forumComment));
    }

    /**
     * 修改评论
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:edit')")
    @Log(title = "评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumComment forumComment)
    {
        return toAjax(forumCommentService.updateForumComment(forumComment));
    }

    /**
     * 删除评论
     */
    @PreAuthorize("@ss.hasPermi('forum:comment:remove')")
    @Log(title = "评论", businessType = BusinessType.DELETE)
	@DeleteMapping("/{commentIds}")
    public AjaxResult remove(@PathVariable Long[] commentIds)
    {
        return toAjax(forumCommentService.deleteForumCommentByCommentIds(commentIds));
    }
}
