package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumUserPostImage;
import com.blog.forum.service.IForumUserPostImageService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 用户发帖图片Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/forum/userPostImage")
public class ForumUserPostImageController extends BaseController
{
    @Autowired
    private IForumUserPostImageService forumUserPostImageService;

    /**
     * 查询用户发帖图片列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumUserPostImage forumUserPostImage)
    {
        startPage();
        List<ForumUserPostImage> list = forumUserPostImageService.selectForumUserPostImageList(forumUserPostImage);
        return getDataTable(list);
    }

    /**
     * 导出用户发帖图片列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:export')")
    @Log(title = "用户发帖图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumUserPostImage forumUserPostImage)
    {
        List<ForumUserPostImage> list = forumUserPostImageService.selectForumUserPostImageList(forumUserPostImage);
        ExcelUtil<ForumUserPostImage> util = new ExcelUtil<ForumUserPostImage>(ForumUserPostImage.class);
        util.exportExcel(response, list, "用户发帖图片数据");
    }

    /**
     * 获取用户发帖图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(forumUserPostImageService.selectForumUserPostImageByImageId(imageId));
    }

    /**
     * 新增用户发帖图片
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:add')")
    @Log(title = "用户发帖图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumUserPostImage forumUserPostImage)
    {
        return toAjax(forumUserPostImageService.insertForumUserPostImage(forumUserPostImage));
    }

    /**
     * 修改用户发帖图片
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:edit')")
    @Log(title = "用户发帖图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumUserPostImage forumUserPostImage)
    {
        return toAjax(forumUserPostImageService.updateForumUserPostImage(forumUserPostImage));
    }

    /**
     * 删除用户发帖图片
     */
    @PreAuthorize("@ss.hasPermi('forum:userPostImage:remove')")
    @Log(title = "用户发帖图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable Long[] imageIds)
    {
        return toAjax(forumUserPostImageService.deleteForumUserPostImageByImageIds(imageIds));
    }
}
