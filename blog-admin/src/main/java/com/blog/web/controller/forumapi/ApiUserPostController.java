package com.blog.web.controller.forumapi;

import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.domain.dto.UserPostDTO;
import com.blog.forum.service.PostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

// ForumUserPostController.java
@RestController
@RequestMapping("/api/user_post")
@Api(tags = "用户发帖接口")
@RequiredArgsConstructor
public class ApiUserPostController extends BaseController {
    @Autowired
    private PostService userPostService;
    @GetMapping("/list")
    @ApiOperation("获取用户发帖列表")
    public TableDataInfo getUserPostList(PostListDTO postListDTO) {
        Long currentUserId = SecurityUtils.getLoginUser().getUser().getUserId();
        startPage();
        return getDataTable(userPostService.getUserPostList(postListDTO, currentUserId));
    }
    @GetMapping("/detail/{postId}")
    @ApiOperation("获取用户发帖详情")
    public AjaxResult getUserPostDetail(@PathVariable Long postId) {
        return AjaxResult.success(userPostService.getUserPostDetail(postId));
    }
    @PostMapping("/submit")
    @ApiOperation("发表/更新用户帖子(需要登录)")
    public AjaxResult submitUserPost(@RequestBody UserPostDTO userPostDTO) {
        return userPostService.submitUserPost(userPostDTO);
    }
}
