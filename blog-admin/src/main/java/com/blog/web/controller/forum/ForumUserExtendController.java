package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.forum.service.IForumUserExtendService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.core.domain.entity.SysUser;
import com.blog.system.service.ISysUserService;
import com.blog.common.utils.SecurityUtils;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/userExtend")
public class ForumUserExtendController extends BaseController
{
    @Autowired
    private IForumUserExtendService forumUserExtendService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询用户管理列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumUserExtend forumUserExtend)
    {
        startPage();
        List<ForumUserExtend> list = forumUserExtendService.selectForumUserExtendList(forumUserExtend);
        return getDataTable(list);
    }

    /**
     * 导出用户管理列表
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:export')")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumUserExtend forumUserExtend)
    {
        List<ForumUserExtend> list = forumUserExtendService.selectForumUserExtendList(forumUserExtend);
        ExcelUtil<ForumUserExtend> util = new ExcelUtil<ForumUserExtend>(ForumUserExtend.class);
        util.exportExcel(response, list, "用户管理数据");
    }

    /**
     * 获取用户管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(forumUserExtendService.selectForumUserExtendByUserId(userId));
    }

    /**
     * 新增用户管理
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumUserExtend forumUserExtend)
    {
        return toAjax(forumUserExtendService.insertForumUserExtend(forumUserExtend));
    }

    /**
     * 修改用户管理
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumUserExtend forumUserExtend)
    {
        return toAjax(forumUserExtendService.updateForumUserExtend(forumUserExtend));
    }

    /**
     * 删除用户管理
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(forumUserExtendService.deleteForumUserExtendByUserIds(userIds));
    }

    /**
     * 重置用户密码
     */
    @PreAuthorize("@ss.hasPermi('forum:userExtend:resetPassword')")
    @Log(title = "重置用户密码", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestParam Long userId, @RequestParam String newPassword)
    {
        try {
            // 获取用户信息
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }

            // 检查用户状态
            if ("1".equals(user.getStatus())) {
                return AjaxResult.error("用户已被禁用，无法重置密码");
            }

            // 加密新密码
            String encryptedPassword = SecurityUtils.encryptPassword(newPassword);
            user.setPassword(encryptedPassword);

            // 更新密码
            if (userService.updateUser(user) > 0) {
                return AjaxResult.success("密码重置成功");
            } else {
                return AjaxResult.error("密码重置失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("密码重置失败：" + e.getMessage());
        }
    }
}
