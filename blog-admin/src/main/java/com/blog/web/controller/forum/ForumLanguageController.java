package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.service.IForumLanguageService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 语言类型Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/language")
public class ForumLanguageController extends BaseController
{
    @Autowired
    private IForumLanguageService forumLanguageService;

    /**
     * 查询语言类型列表
     */
    @PreAuthorize("@ss.hasPermi('forum:language:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumLanguage forumLanguage)
    {
        startPage();
        List<ForumLanguage> list = forumLanguageService.selectForumLanguageList(forumLanguage);
        return getDataTable(list);
    }

    /**
     * 导出语言类型列表
     */
    @PreAuthorize("@ss.hasPermi('forum:language:export')")
    @Log(title = "语言类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumLanguage forumLanguage)
    {
        List<ForumLanguage> list = forumLanguageService.selectForumLanguageList(forumLanguage);
        ExcelUtil<ForumLanguage> util = new ExcelUtil<ForumLanguage>(ForumLanguage.class);
        util.exportExcel(response, list, "语言类型数据");
    }

    /**
     * 获取语言类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:language:query')")
    @GetMapping(value = "/{langId}")
    public AjaxResult getInfo(@PathVariable("langId") Long langId)
    {
        return success(forumLanguageService.selectForumLanguageByLangId(langId));
    }

    /**
     * 新增语言类型
     */
    @PreAuthorize("@ss.hasPermi('forum:language:add')")
    @Log(title = "语言类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumLanguage forumLanguage)
    {
        return toAjax(forumLanguageService.insertForumLanguage(forumLanguage));
    }

    /**
     * 修改语言类型
     */
    @PreAuthorize("@ss.hasPermi('forum:language:edit')")
    @Log(title = "语言类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumLanguage forumLanguage)
    {
        return toAjax(forumLanguageService.updateForumLanguage(forumLanguage));
    }

    /**
     * 删除语言类型
     */
    @PreAuthorize("@ss.hasPermi('forum:language:remove')")
    @Log(title = "语言类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{langIds}")
    public AjaxResult remove(@PathVariable Long[] langIds)
    {
        return toAjax(forumLanguageService.deleteForumLanguageByLangIds(langIds));
    }

    /**
     * 获取启用的语言列表
     */
    @GetMapping("/listEnabled")
    public AjaxResult listEnabled()
    {
        ForumLanguage forumLanguage = new ForumLanguage();
        forumLanguage.setStatus("0"); // 0表示启用
        List<ForumLanguage> list = forumLanguageService.selectForumLanguageList(forumLanguage);
        return success(list);
    }
}
