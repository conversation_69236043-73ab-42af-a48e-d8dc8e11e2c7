package com.blog.web.controller.forumapi;

import com.blog.common.core.controller.BaseController;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.service.IForumPostService;
import com.blog.forum.service.IForumUserPostService;
import com.blog.forum.service.PostService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/post")
public class ApiPostController extends BaseController {
    @Autowired
    private IForumPostService postService;

    @Autowired
    private IForumUserPostService userPostService;

    @Autowired
    private PostService apiPostService;
    @GetMapping("/carousels")
    @ApiOperation("轮播图")
    public AjaxResult carouselList() {
        return AjaxResult.success(postService.selectCarouselList());
    }
    @GetMapping("/list")
    @ApiOperation("获取帖子列表")
    public TableDataInfo getPostList(PostListDTO postListDTO) {
        startPage();
        return getDataTable(apiPostService.getPostList(postListDTO));
    }
    @GetMapping("/detail/{postId}")
    @ApiOperation("获取帖子详情")
    public AjaxResult getPostDetail(@PathVariable Long postId) {
        return AjaxResult.success(apiPostService.getPostDetail(postId));
    }
    @GetMapping("/category/list")
    @ApiOperation("获取分类列表")
    public AjaxResult getCategoryList() {
        return AjaxResult.success(apiPostService.getCategoryList());
    }

    @ApiOperation("获取可用标签列表")
    @GetMapping("/tags")
    public AjaxResult tags() {
        return success(userPostService.selectAvailableTags());
    }
}
