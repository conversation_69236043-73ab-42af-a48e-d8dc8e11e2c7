package com.blog.web.controller.oss;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.exception.ServiceException;
import com.blog.forum.domain.dto.ChunkUploadDTO;
import com.blog.forum.domain.dto.ChunkUploadResponseDTO;
import com.blog.forum.service.FileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/oss")
public class OssController {

    private static final Logger log = LoggerFactory.getLogger(OssController.class);

    @Value("${spring.servlet.multipart.max-file-size:100MB}")
    private String maxFileSize;

    @Autowired
    private FileService fileService;

    /**
     * 处理文件大小超出限制异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public AjaxResult handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件大小超出限制", e);
        return AjaxResult.error("文件大小超出限制，最大允许上传" + maxFileSize);
    }


    /**
     * 文件上传
     * 当文件大小超过 spring.servlet.multipart.max-file-size 时，会直接返回错误提示
     * 当文件大小在该限制之内，但超过10MB时，会自动使用分片上传
     *
     * @param file 上传的文件
     * @param bizType 业务类型
     * @return 上传结果
     */
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file,
                             @RequestParam("bizType") String bizType) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 记录上传信息
            log.info("文件上传: 文件名={}, 大小={}KB, 类型={}, 业务类型={}",
                    file.getOriginalFilename(),
                    file.getSize() / 1024,
                    file.getContentType(),
                    bizType);

            // 上传文件
            String url = fileService.upload(file, bizType);
            return AjaxResult.success().put("url", url);
        }catch (ServiceException e) {
            // 捕获业务异常
            log.error("文件上传业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            // 捕获其他异常
            log.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败，请联系管理员");
        }
    }

    /**
     * 检查分片是否存在
     * 在上传分片前调用，检查是否需要跳过上传
     *
     * @param identifier 文件标识（MD5）
     * @param chunkNumber 分片编号
     * @param bizType 业务类型
     * @return 检查结果
     */
    @GetMapping("/chunk/check")
    public AjaxResult checkChunk(
            @RequestParam("identifier") String identifier,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("bizType") String bizType) {
        try {
            ChunkUploadResponseDTO response = fileService.checkChunk(identifier, chunkNumber, bizType);
            return AjaxResult.success().put("data", response);
        } catch (Exception e) {
            log.error("检查分片失败", e);
            return AjaxResult.error("检查分片失败: " + e.getMessage());
        }
    }

    /**
     * 上传分片
     *
     * @param file 分片文件
     * @param chunkNumber 当前分片编号
     * @param totalChunks 总分片数
     * @param chunkSize 分片大小
     * @param currentChunkSize 当前分片大小
     * @param totalSize 文件总大小
     * @param identifier 文件标识（MD5）
     * @param filename 文件名
     * @param bizType 业务类型
     * @return 上传结果
     */
    @PostMapping("/chunk/upload")
    public AjaxResult uploadChunk(
            @RequestParam("file") MultipartFile file,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("totalChunks") Integer totalChunks,
            @RequestParam("chunkSize") Long chunkSize,
            @RequestParam("currentChunkSize") Long currentChunkSize,
            @RequestParam("totalSize") Long totalSize,
            @RequestParam("identifier") String identifier,
            @RequestParam("filename") String filename,
            @RequestParam("bizType") String bizType) {
        try {
            // 构建分片上传DTO
            ChunkUploadDTO chunkUploadDTO = new ChunkUploadDTO();
            chunkUploadDTO.setChunkNumber(chunkNumber);
            chunkUploadDTO.setTotalChunks(totalChunks);
            chunkUploadDTO.setChunkSize(chunkSize);
            chunkUploadDTO.setCurrentChunkSize(currentChunkSize);
            chunkUploadDTO.setTotalSize(totalSize);
            chunkUploadDTO.setIdentifier(identifier);
            chunkUploadDTO.setFilename(filename);
            chunkUploadDTO.setBizType(bizType);

            // 上传分片
            ChunkUploadResponseDTO response = fileService.uploadChunk(file, chunkUploadDTO);
            return AjaxResult.success().put("data", response);
        } catch (Exception e) {
            log.error("分片上传失败", e);
            return AjaxResult.error("分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 合并分片
     *
     * @param identifier 文件标识（MD5）
     * @param filename 文件名
     * @param totalChunks 总分片数
     * @param totalSize 文件总大小
     * @param bizType 业务类型
     * @return 合并结果
     */
    @PostMapping("/chunk/merge")
    public AjaxResult mergeChunks(
            @RequestParam("identifier") String identifier,
            @RequestParam("filename") String filename,
            @RequestParam("totalChunks") Integer totalChunks,
            @RequestParam("totalSize") Long totalSize,
            @RequestParam("bizType") String bizType) {
        try {
            // 合并分片
            String url = fileService.mergeChunks(identifier, filename, totalChunks, totalSize, bizType);
            return AjaxResult.success().put("url", url);
        } catch (Exception e) {
            log.error("合并分片失败", e);
            return AjaxResult.error("合并分片失败: " + e.getMessage());
        }
    }
    /**
     * 批量文件上传
     *
     * @param files 上传的文件数组
     * @param bizType 业务类型
     * @return 上传结果
     */
    @PostMapping("/uploadBatch")
    public AjaxResult uploadBatch(@RequestParam("files") MultipartFile[] files,
                                  @RequestParam("bizType") String bizType) {
        try {
            // 检查文件数组是否为空
            if (files == null || files.length == 0) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 记录上传信息
            log.info("批量文件上传: 文件数量={}, 业务类型={}", files.length, bizType);

            // 上传文件
            List<String> urls = fileService.uploadBatch(files, bizType);
            return AjaxResult.success().put("urls", urls);
        } catch (ServiceException e) {
            // 捕获业务异常
            log.error("批量文件上传业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            // 捕获其他异常
            log.error("批量文件上传失败", e);
            return AjaxResult.error("文件上传失败，请联系管理员");
        }
    }
}
