package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumCategoryI18n;
import com.blog.forum.service.IForumCategoryI18nService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 分类多语言内容Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/forum/forumCategoryi18n")
public class ForumCategoryI18nController extends BaseController
{
    @Autowired
    private IForumCategoryI18nService forumCategoryI18nService;

    /**
     * 查询分类多语言内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumCategoryI18n forumCategoryI18n)
    {
        startPage();
        List<ForumCategoryI18n> list = forumCategoryI18nService.selectForumCategoryI18nList(forumCategoryI18n);
        return getDataTable(list);
    }

    /**
     * 导出分类多语言内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:export')")
    @Log(title = "分类多语言内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumCategoryI18n forumCategoryI18n)
    {
        List<ForumCategoryI18n> list = forumCategoryI18nService.selectForumCategoryI18nList(forumCategoryI18n);
        ExcelUtil<ForumCategoryI18n> util = new ExcelUtil<ForumCategoryI18n>(ForumCategoryI18n.class);
        util.exportExcel(response, list, "分类多语言内容数据");
    }

    /**
     * 获取分类多语言内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:query')")
    @GetMapping(value = "/{i18nId}")
    public AjaxResult getInfo(@PathVariable("i18nId") Long i18nId)
    {
        return success(forumCategoryI18nService.selectForumCategoryI18nByI18nId(i18nId));
    }

    /**
     * 新增分类多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:add')")
    @Log(title = "分类多语言内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumCategoryI18n forumCategoryI18n)
    {
        return toAjax(forumCategoryI18nService.insertForumCategoryI18n(forumCategoryI18n));
    }

    /**
     * 修改分类多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:edit')")
    @Log(title = "分类多语言内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumCategoryI18n forumCategoryI18n)
    {
        return toAjax(forumCategoryI18nService.updateForumCategoryI18n(forumCategoryI18n));
    }

    /**
     * 删除分类多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumCategoryi18n:remove')")
    @Log(title = "分类多语言内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{i18nIds}")
    public AjaxResult remove(@PathVariable Long[] i18nIds)
    {
        return toAjax(forumCategoryI18nService.deleteForumCategoryI18nByI18nIds(i18nIds));
    }
}
