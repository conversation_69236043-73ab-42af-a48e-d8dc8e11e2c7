package com.blog.web.controller.test;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * CORS 测试控制器
 */
@RestController
@RequestMapping("/api/test")
public class CorsTestController {

    /**
     * 测试 CORS 配置
     */
    @GetMapping("/cors")
    public Map<String, Object> testCors() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "CORS test successful");
        result.put("data", "This is a test response from the server");
        return result;
    }
}
