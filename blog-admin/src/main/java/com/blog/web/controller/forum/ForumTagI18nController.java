package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumTagI18n;
import com.blog.forum.service.IForumTagI18nService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 标签多语言内容Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/forum/forumTagi18n")
public class ForumTagI18nController extends BaseController
{
    @Autowired
    private IForumTagI18nService forumTagI18nService;

    /**
     * 查询标签多语言内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumTagI18n forumTagI18n)
    {
        startPage();
        List<ForumTagI18n> list = forumTagI18nService.selectForumTagI18nList(forumTagI18n);
        return getDataTable(list);
    }

    /**
     * 导出标签多语言内容列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:export')")
    @Log(title = "标签多语言内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumTagI18n forumTagI18n)
    {
        List<ForumTagI18n> list = forumTagI18nService.selectForumTagI18nList(forumTagI18n);
        ExcelUtil<ForumTagI18n> util = new ExcelUtil<ForumTagI18n>(ForumTagI18n.class);
        util.exportExcel(response, list, "标签多语言内容数据");
    }

    /**
     * 获取标签多语言内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:query')")
    @GetMapping(value = "/{i18nId}")
    public AjaxResult getInfo(@PathVariable("i18nId") Long i18nId)
    {
        return success(forumTagI18nService.selectForumTagI18nByI18nId(i18nId));
    }

    /**
     * 新增标签多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:add')")
    @Log(title = "标签多语言内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumTagI18n forumTagI18n)
    {
        return toAjax(forumTagI18nService.insertForumTagI18n(forumTagI18n));
    }

    /**
     * 修改标签多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:edit')")
    @Log(title = "标签多语言内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumTagI18n forumTagI18n)
    {
        return toAjax(forumTagI18nService.updateForumTagI18n(forumTagI18n));
    }

    /**
     * 删除标签多语言内容
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTagi18n:remove')")
    @Log(title = "标签多语言内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{i18nIds}")
    public AjaxResult remove(@PathVariable Long[] i18nIds)
    {
        return toAjax(forumTagI18nService.deleteForumTagI18nByI18nIds(i18nIds));
    }
}
