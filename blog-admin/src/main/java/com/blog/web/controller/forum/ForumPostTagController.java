package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumPostTag;
import com.blog.forum.service.IForumPostTagService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 帖子标签关联Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/forum/forumPostTag")
public class ForumPostTagController extends BaseController
{
    @Autowired
    private IForumPostTagService forumPostTagService;

    /**
     * 查询帖子标签关联列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumPostTag forumPostTag)
    {
        startPage();
        List<ForumPostTag> list = forumPostTagService.selectForumPostTagList(forumPostTag);
        return getDataTable(list);
    }

    /**
     * 导出帖子标签关联列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:export')")
    @Log(title = "帖子标签关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumPostTag forumPostTag)
    {
        List<ForumPostTag> list = forumPostTagService.selectForumPostTagList(forumPostTag);
        ExcelUtil<ForumPostTag> util = new ExcelUtil<ForumPostTag>(ForumPostTag.class);
        util.exportExcel(response, list, "帖子标签关联数据");
    }

    /**
     * 获取帖子标签关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(forumPostTagService.selectForumPostTagById(id));
    }

    /**
     * 新增帖子标签关联
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:add')")
    @Log(title = "帖子标签关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumPostTag forumPostTag)
    {
        return toAjax(forumPostTagService.insertForumPostTag(forumPostTag));
    }

    /**
     * 修改帖子标签关联
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:edit')")
    @Log(title = "帖子标签关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumPostTag forumPostTag)
    {
        return toAjax(forumPostTagService.updateForumPostTag(forumPostTag));
    }

    /**
     * 删除帖子标签关联
     */
    @PreAuthorize("@ss.hasPermi('forum:forumPostTag:remove')")
    @Log(title = "帖子标签关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(forumPostTagService.deleteForumPostTagByIds(ids));
    }
}
