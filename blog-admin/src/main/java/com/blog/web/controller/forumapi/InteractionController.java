package com.blog.web.controller.forumapi;

import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.dto.CollectDTO;
import com.blog.forum.domain.dto.LikeDTO;
import com.blog.forum.domain.dto.ShareDTO;
import com.blog.forum.service.IForumInteractionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
@Api(tags = "互动管理")
@RestController
@RequestMapping("/api/interaction")
public class InteractionController extends BaseController{

    @Autowired
    private IForumInteractionService interactionService;
    @ApiOperation("点赞/取消点赞")
    @PostMapping("/like")
    public AjaxResult like(@RequestBody LikeDTO likeDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return error("请先登录");
        }
        return interactionService.like(likeDTO, loginUser.getUser().getUserId());
    }
    @ApiOperation("分享")
    @PostMapping("/share")
    public AjaxResult share(@RequestBody ShareDTO shareDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return error("请先登录");
        }
        return interactionService.share(shareDTO, loginUser.getUser().getUserId());
    }
    @ApiOperation("收藏/取消收藏")
    @PostMapping("/collect")
    public AjaxResult collect(@RequestBody CollectDTO collectDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return error("请先登录");
        }
        return interactionService.collect(collectDTO, loginUser.getUser().getUserId());
    }

    @GetMapping("/user/collect")
    @ApiOperation("获取我的收藏列表(分页)")
    public TableDataInfo getMyCollectList() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }
        return getDataTable(interactionService.getMyCollectList(userId));
    }
    @GetMapping("/user/like")
    @ApiOperation("获取我的点赞列表(分页,需要登录)")
    public TableDataInfo getMyLikeList() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }
        return getDataTable(interactionService.getMyLikeList(userId));
    }
    @GetMapping("/user/comment")
    @ApiOperation("获取我的评论列表(分页)")
    public TableDataInfo getMyCommentList() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }
        return getDataTable(interactionService.getMyCommentList(userId));
    }
    @GetMapping("/user/stats")
    @ApiOperation("获取用户互动统计(收藏/点赞/评论数)")
    public AjaxResult getUserStats() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }
        return AjaxResult.success(interactionService.getUserStats(userId));
    }
}
