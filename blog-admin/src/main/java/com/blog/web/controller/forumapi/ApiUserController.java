package com.blog.web.controller.forumapi;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.common.core.domain.entity.SysUser;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.core.redis.RedisCache;
import com.blog.common.utils.AdvancedEmailValidator;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.dto.UserInfoUpdateDTO;
import com.blog.forum.service.IForumUserExtendService;
import com.blog.framework.web.service.EmailService;
import com.blog.framework.web.service.SysRegisterService;
import com.blog.framework.web.service.TokenService;
import com.blog.system.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@RestController
@RequestMapping(value ="/api/auth",produces = "application/json;charset=UTF-8")
public class ApiUserController extends BaseController {
    @Autowired
    private TokenService tokenService;

    @Resource
    private EmailService emailService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private RedisCache redisCache;

    @Autowired
    private SysRegisterService sysRegisterService;

    @Autowired
    private IForumUserExtendService forumUserExtendService;


    /**
     * 验证邮箱是否有效
     */
    @PostMapping("/email/validate")
    public AjaxResult validateEmail(@RequestParam(required = true) String email) {
        // 验证邮箱格式
        if (!Validator.isEmail(email)) {
            return AjaxResult.error("邮箱格式不正确", false);
        }

        // 检查邮箱是否已注册
        if (userService.checkEmailExists(email)) {
            return AjaxResult.error("该邮箱已注册", false);
        }

        // 验证邮箱是否有效
        boolean isValid = AdvancedEmailValidator.isValidEmail(email);
        if (isValid) {
            return AjaxResult.success("邮箱有效", true);
        } else {
            return AjaxResult.error("邮箱无效或不存在", false);
        }
    }

    /**
     * 邮箱注册
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestParam(required = true) String email,
                               @RequestParam(required = true) String password) {
        // 验证邮箱格式
        if (!Validator.isEmail(email)) {
            return AjaxResult.error("邮箱格式不正确");
        }

        // 检查邮箱是否已注册
        if (userService.checkEmailExists(email)) {
            return AjaxResult.error("该邮箱已注册");
        }

        // 验证邮箱是否有效
        if (!AdvancedEmailValidator.isValidEmail(email)) {
            return AjaxResult.error("邮箱无效或不存在");
        }

        return sysRegisterService.registerForBlog(email, password);
    }

    /**
     * 邮箱登录
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestParam(required = true) String email,
                            @RequestParam(required = true) String password) {
        // 验证邮箱格式
        if (!Validator.isEmail(email)) {
            return AjaxResult.error("邮箱格式不正确");
        }

        // 查询用户
        SysUser user = userService.selectUserByEmail(email);

        // 验证用户是否存在
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        // 验证状态
        if ("1".equals(user.getStatus())) {
            return AjaxResult.error("用户已被禁用");
        }

        // 验证密码 - 使用BCrypt验证
        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            return AjaxResult.error("密码错误");
        }

        // 更新用户最后登录时间
        userService.updateUserLoginTime(user.getUserId());

        // 创建token
        LoginUser loginUser = new LoginUser(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", tokenService.createToken(loginUser));
        return ajax;
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout(HttpServletRequest request) {
        tokenService.delLoginUser(tokenService.getLoginUser(request).getToken());
        return AjaxResult.success();
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public AjaxResult info(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        SysUser user = loginUser.getUser();
        ForumUserExtend userExtend = forumUserExtendService.selectForumUserExtendByUserId(user.getUserId());
        // 返回精简的用户信息
        Map<String, Object> data = new HashMap<>();
        data.put("userId", user.getUserId());
        data.put("username", user.getUserName());
        data.put("nickname", userExtend.getNickname());
        data.put("email", userExtend.getEmail());
        data.put("avatar", userExtend.getAvatar());
        data.put("bio",userExtend.getBio());
        return AjaxResult.success(data);
    }


    /**
     * 重置密码（通过验证码）
     */
    @PostMapping("/password/reset")
    public AjaxResult resetPassword(@RequestParam String email,
                                    @RequestParam String code,
                                    @RequestParam String newPassword) {
        // 验证邮箱格式
        if (!Validator.isEmail(email)) {
            return AjaxResult.error("邮箱格式不正确");
        }

        // 检查邮箱是否已注册
        SysUser user = userService.selectUserByEmail(email);
        if (user == null) {
            return AjaxResult.error("该邮箱未注册");
        }

        // 检查用户状态
        if ("1".equals(user.getStatus())) {
            return AjaxResult.error("用户已被禁用");
        }

        // 验证验证码
        String cacheKey = "reset_code:" + email;
        String cachedCode = redisCache.getCacheObject(cacheKey);
        if (cachedCode == null) {
            return AjaxResult.error("验证码已过期");
        }
        if (!StrUtil.equals(code, cachedCode)) {
            return AjaxResult.error("验证码错误");
        }

        // 更新密码
        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        if (userService.updateUser(user) > 0) {
            // 删除验证码
            redisCache.deleteObject(cacheKey);
            return AjaxResult.success("密码重置成功");
        }

        return AjaxResult.error("密码重置失败");
    }

    /**
     * 修改密码（需要原密码）
     */
    @PostMapping("/password/change")
    public AjaxResult changePassword(@RequestParam String email,
                                     @RequestParam String oldPassword,
                                     @RequestParam String newPassword) {
        // 验证邮箱格式
        if (!Validator.isEmail(email)) {
            return AjaxResult.error("邮箱格式不正确");
        }

        // 检查邮箱是否已注册
        SysUser user = userService.selectUserByEmail(email);
        if (user == null) {
            return AjaxResult.error("该邮箱未注册");
        }

        // 检查用户状态
        if ("1".equals(user.getStatus())) {
            return AjaxResult.error("用户已被禁用");
        }

        // 验证原密码 - 使用BCrypt验证
        if (!SecurityUtils.matchesPassword(oldPassword, user.getPassword())) {
            return AjaxResult.error("原密码错误");
        }

        // 检查新密码是否与原密码相同
        if (StrUtil.equals(oldPassword, newPassword)) {
            return AjaxResult.error("新密码不能与原密码相同");
        }

        // 更新密码
        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        if (userService.updateUser(user) > 0) {
            return AjaxResult.success("密码修改成功");
        }

        return AjaxResult.error("密码修改失败");
    }

    /**
     * 发送重置密码验证码
     */
//    @PostMapping("/password/send-code")
//    public AjaxResult sendResetCode(@RequestParam String email) {
//        // 验证邮箱格式
//        if (!Validator.isEmail(email)) {
//            return AjaxResult.error("邮箱格式不正确");
//        }
//
//        // 检查邮箱是否已注册
//        SysUser user = userService.selectUserByEmail(email);
//        if (user == null) {
//            return AjaxResult.error("该邮箱未注册");
//        }
//
//        // 检查用户状态
//        if ("1".equals(user.getStatus())) {
//            return AjaxResult.error("用户已被禁用");
//        }
//
//        // 生成6位验证码
//        String code = String.valueOf((int)((Math.random() * 9 + 1) * 100000));
//
//        // 存储验证码到Redis，有效期10分钟
//        String cacheKey = "reset_code:" + email;
//        redisCache.setCacheObject(cacheKey, code, 10, java.util.concurrent.TimeUnit.MINUTES);
//
//        // 发送邮件
//        try {
//            emailService.sendResetPasswordCode(email, code);
//            return AjaxResult.success("验证码发送成功");
//        } catch (Exception e) {
//            return AjaxResult.error("验证码发送失败");
//        }
//    }

    @PostMapping("/nickname")
    public AjaxResult updateNickName(@RequestParam String nickname,HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        SysUser user = loginUser.getUser();
        try {
            user.setNickName(nickname);
            userService.updateUser(user);
            ForumUserExtend forumUserExtend = forumUserExtendService.selectForumUserExtendByUserId(user.getUserId());
            forumUserExtend.setNickname(nickname);
            forumUserExtendService.updateForumUserExtend(forumUserExtend);
        }catch (Exception e){
            return AjaxResult.error("修改昵称失败");
        }

        return AjaxResult.success("修改昵称成功");

    }

    @PostMapping(value = "/profile", produces = "application/json;charset=UTF-8")
    @ApiOperation("更新用户信息")
    public AjaxResult updateProfile(@RequestBody UserInfoUpdateDTO updateDTO) {
        return forumUserExtendService.updateUserInfo(updateDTO);
    }
}
