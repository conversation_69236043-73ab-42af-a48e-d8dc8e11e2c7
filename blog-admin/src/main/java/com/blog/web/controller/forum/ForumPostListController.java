package com.blog.web.controller.forum;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.ForumPost;
import com.blog.forum.domain.ForumTag;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.domain.ForumPostI18nContent;
import com.blog.forum.domain.ForumPostTag;
import com.blog.forum.domain.dto.PostAuditDTO;
import com.blog.forum.domain.dto.PostListQueryDTO;
import com.blog.forum.mapper.ForumTagMapper;
import com.blog.forum.mapper.ForumLanguageMapper;
import com.blog.forum.mapper.ForumPostI18nContentMapper;
import com.blog.forum.mapper.ForumPostTagMapper;
import com.blog.forum.util.LangUtils;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.service.IForumPostService;
import com.blog.forum.service.IForumTagService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 帖子列表Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forum/postList")
public class ForumPostListController extends BaseController
{
    @Autowired
    private IForumPostService forumPostService;

    @Autowired
    private IForumTagService forumTagService;

    @Autowired
    private ForumTagMapper forumTagMapper;

    @Autowired
    private ForumLanguageMapper forumLanguageMapper;

    @Autowired
    private ForumPostI18nContentMapper forumPostI18nContentMapper;

    @Autowired
    private ForumPostTagMapper forumPostTagMapper;

    /**
     * 查询帖子列表
     */
    @PreAuthorize("@ss.hasPermi('forum:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(PostListQueryDTO query)
    {
        startPage();
        List<PostDetailVO> list = forumPostService.selectPostListAdvanced(query);
        return getDataTable(list);
    }

    /**
     * 导出帖子列表
     */
    @PreAuthorize("@ss.hasPermi('forum:post:export')")
    @Log(title = "帖子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PostListQueryDTO query)
    {
        List<PostDetailVO> list = forumPostService.selectPostListAdvanced(query);
        ExcelUtil<PostDetailVO> util = new ExcelUtil<>(PostDetailVO.class);
        util.exportExcel(response, list, "帖子数据");
    }

    /**
     * 获取帖子详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:post:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Long postId)
    {
        return success(forumPostService.selectForumPostByPostId(postId));
    }

    /**
     * 新增帖子
     */
    @PreAuthorize("@ss.hasPermi('forum:post:add')")
    @Log(title = "帖子", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody ForumPost forumPost)
    {
        // 设置当前用户ID和语言ID
        Long userId = SecurityUtils.getUserId();
        forumPost.setSysUserId(userId);
        forumPost.setLangId(LangUtils.getCurrentLangId());
        forumPost.setIsUserPost("0"); // 管理员发布的帖子

        // 插入帖子基本信息
        int result = forumPostService.insertForumPost(forumPost);

        if (result > 0) {
            // 处理标签关联
            if (forumPost.getTagIds() != null && !forumPost.getTagIds().isEmpty()) {
                for (Long tagId : forumPost.getTagIds()) {
                    ForumPostTag postTag = new ForumPostTag();
                    postTag.setPostId(forumPost.getPostId());
                    postTag.setTagId(tagId);
                    postTag.setCreateTime(new Date());
                    forumPostTagMapper.insertForumPostTag(postTag);
                }
            }

            // 处理国际化内容
            saveI18nContent(forumPost.getPostId(), forumPost);
        }

        return toAjax(result);
    }

    /**
     * 修改帖子
     */
    @PreAuthorize("@ss.hasPermi('forum:post:edit')")
    @Log(title = "帖子", businessType = BusinessType.UPDATE)
    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult edit(@RequestBody ForumPost forumPost)
    {
        // 更新帖子基本信息
        int result = forumPostService.updateForumPost(forumPost);

        if (result > 0) {
            // 先删除原有标签关联
            forumPostTagMapper.deleteByPostId(forumPost.getPostId());

            // 处理标签关联
            if (forumPost.getTagIds() != null && !forumPost.getTagIds().isEmpty()) {
                for (Long tagId : forumPost.getTagIds()) {
                    ForumPostTag postTag = new ForumPostTag();
                    postTag.setPostId(forumPost.getPostId());
                    postTag.setTagId(tagId);
                    postTag.setCreateTime(new Date());
                    forumPostTagMapper.insertForumPostTag(postTag);
                }
            }

            // 更新国际化内容
            updateI18nContent(forumPost.getPostId(), forumPost);
        }

        return toAjax(result);
    }

    /**
     * 删除帖子
     */
    @PreAuthorize("@ss.hasPermi('forum:post:remove')")
    @Log(title = "帖子", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        return toAjax(forumPostService.deleteForumPostByPostIds(postIds));
    }

    /**
     * 获取分类列表
     */
    @PreAuthorize("@ss.hasPermi('forum:post:categories')")
    @GetMapping("/categories")
    public AjaxResult getCategoryList()
    {
        List<CategoryVO> categories = forumPostService.selectCategoryList();
        return success(categories);
    }

    /**
     * 获取标签列表（支持国际化）
     */
    @GetMapping("/tags")
    @PreAuthorize("@ss.hasPermi('forum:post:tag')")
    public AjaxResult getTagList()
    {
        // 获取当前语言ID
        Long langId = LangUtils.getCurrentLangId();
        // 使用支持国际化的查询方法
        List<ForumTag> tags = forumTagMapper.selectTagList(new ForumTag(), langId);
        return success(tags);
    }

    /**
     * 审核帖子
     */
    @PreAuthorize("@ss.hasPermi('forum:post:edit')")
    @Log(title = "帖子审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody PostAuditDTO auditDTO)
    {
        if (auditDTO.getPostId() == null) {
            return error("帖子ID不能为空");
        }
        if (auditDTO.getPostStatus() == null) {
            return error("审核结果不能为空");
        }

        // 构建帖子对象
        ForumPost forumPost = new ForumPost();
        forumPost.setPostId(auditDTO.getPostId());
        forumPost.setPostStatus(auditDTO.getPostStatus());
        forumPost.setRemark(auditDTO.getRemark());

        return toAjax(forumPostService.updateForumPost(forumPost));
    }

    /**
     * 设置帖子置顶状态
     */
    @PreAuthorize("@ss.hasPermi('forum:post:edit')")
    @Log(title = "帖子置顶", businessType = BusinessType.UPDATE)
    @PutMapping("/setTop")
    public AjaxResult setTop(@RequestBody ForumPost post)
    {
        if (post.getPostId() == null || post.getIsTop() == null) {
            return error("参数不完整");
        }
        // 使用专门的置顶方法，避免影响其他字段
        return toAjax(forumPostService.updatePostTopStatus(post.getPostId(), post.getIsTop()));
    }

    /**
     * 保存帖子国际化内容
     *
     * @param postId 帖子ID
     * @param forumPost 帖子对象
     */
    private void saveI18nContent(Long postId, ForumPost forumPost) {
        // 获取当前支持的语言
        List<ForumLanguage> languages = forumLanguageMapper.selectEnabledLanguageList();

        for (ForumLanguage lang : languages) {
            ForumPostI18nContent content = new ForumPostI18nContent();
            content.setPostId(postId);
            content.setLangId(lang.getLangId());
            content.setTitle(forumPost.getTitle());
            content.setSummary(forumPost.getSummary());
            content.setContent(forumPost.getContent());
            content.setCreateTime(new Date());

            forumPostI18nContentMapper.insertForumPostI18nContent(content);
        }
    }

    /**
     * 更新帖子国际化内容
     *
     * @param postId 帖子ID
     * @param forumPost 帖子对象
     */
    private void updateI18nContent(Long postId, ForumPost forumPost) {
        // 获取当前支持的语言
        List<ForumLanguage> languages = forumLanguageMapper.selectEnabledLanguageList();

        for (ForumLanguage lang : languages) {
            // 检查是否已存在该语言的内容
            Long existingId = forumPostI18nContentMapper.exists(postId, lang.getLangId());

            ForumPostI18nContent content = new ForumPostI18nContent();
            content.setPostId(postId);
            content.setLangId(lang.getLangId());
            content.setTitle(forumPost.getTitle());
            content.setSummary(forumPost.getSummary());
            content.setContent(forumPost.getContent());
            content.setUpdateTime(new Date());

            if (existingId != null && existingId > 0) {
                // 更新现有内容
                content.setContentId(existingId);
                forumPostI18nContentMapper.updateForumPostI18nContent(content);
            } else {
                // 插入新内容
                content.setCreateTime(new Date());
                forumPostI18nContentMapper.insertForumPostI18nContent(content);
            }
        }
    }
}
