package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumCarousel;
import com.blog.forum.service.IForumCarouselService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 轮播图Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/carousel")
public class ForumCarouselController extends BaseController
{
    @Autowired
    private IForumCarouselService forumCarouselService;

    /**
     * 查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumCarousel forumCarousel)
    {
        startPage();
        List<ForumCarousel> list = forumCarouselService.selectForumCarouselList(forumCarousel);
        return getDataTable(list);
    }

    /**
     * 导出轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:export')")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumCarousel forumCarousel)
    {
        List<ForumCarousel> list = forumCarouselService.selectForumCarouselList(forumCarousel);
        ExcelUtil<ForumCarousel> util = new ExcelUtil<ForumCarousel>(ForumCarousel.class);
        util.exportExcel(response, list, "轮播图数据");
    }

    /**
     * 获取轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:query')")
    @GetMapping(value = "/{carouselId}")
    public AjaxResult getInfo(@PathVariable("carouselId") Long carouselId)
    {
        return success(forumCarouselService.selectForumCarouselByCarouselId(carouselId));
    }

    /**
     * 新增轮播图
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:add')")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumCarousel forumCarousel)
    {
        return toAjax(forumCarouselService.insertForumCarousel(forumCarousel));
    }

    /**
     * 修改轮播图
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:edit')")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumCarousel forumCarousel)
    {
        return toAjax(forumCarouselService.updateForumCarousel(forumCarousel));
    }

    /**
     * 删除轮播图
     */
    @PreAuthorize("@ss.hasPermi('forum:carousel:remove')")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{carouselIds}")
    public AjaxResult remove(@PathVariable Long[] carouselIds)
    {
        return toAjax(forumCarouselService.deleteForumCarouselByCarouselIds(carouselIds));
    }
}
