package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumPostAttachment;
import com.blog.forum.service.IForumPostAttachmentService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 帖子附件Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/postAttachment")
public class ForumPostAttachmentController extends BaseController
{
    @Autowired
    private IForumPostAttachmentService forumPostAttachmentService;

    /**
     * 查询帖子附件列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumPostAttachment forumPostAttachment)
    {
        startPage();
        List<ForumPostAttachment> list = forumPostAttachmentService.selectForumPostAttachmentList(forumPostAttachment);
        return getDataTable(list);
    }

    /**
     * 导出帖子附件列表
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:export')")
    @Log(title = "帖子附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumPostAttachment forumPostAttachment)
    {
        List<ForumPostAttachment> list = forumPostAttachmentService.selectForumPostAttachmentList(forumPostAttachment);
        ExcelUtil<ForumPostAttachment> util = new ExcelUtil<ForumPostAttachment>(ForumPostAttachment.class);
        util.exportExcel(response, list, "帖子附件数据");
    }

    /**
     * 获取帖子附件详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:query')")
    @GetMapping(value = "/{attachmentId}")
    public AjaxResult getInfo(@PathVariable("attachmentId") Long attachmentId)
    {
        return success(forumPostAttachmentService.selectForumPostAttachmentByAttachmentId(attachmentId));
    }

    /**
     * 新增帖子附件
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:add')")
    @Log(title = "帖子附件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumPostAttachment forumPostAttachment)
    {
        return toAjax(forumPostAttachmentService.insertForumPostAttachment(forumPostAttachment));
    }

    /**
     * 修改帖子附件
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:edit')")
    @Log(title = "帖子附件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumPostAttachment forumPostAttachment)
    {
        return toAjax(forumPostAttachmentService.updateForumPostAttachment(forumPostAttachment));
    }

    /**
     * 删除帖子附件
     */
    @PreAuthorize("@ss.hasPermi('forum:postAttachment:remove')")
    @Log(title = "帖子附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attachmentIds}")
    public AjaxResult remove(@PathVariable Long[] attachmentIds)
    {
        return toAjax(forumPostAttachmentService.deleteForumPostAttachmentByAttachmentIds(attachmentIds));
    }
}
