package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumCollect;
import com.blog.forum.service.IForumCollectService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 收藏Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/collect")
public class ForumCollectController extends BaseController
{
    @Autowired
    private IForumCollectService forumCollectService;

    /**
     * 查询收藏列表
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumCollect forumCollect)
    {
        startPage();
        List<ForumCollect> list = forumCollectService.selectForumCollectList(forumCollect);
        return getDataTable(list);
    }

    /**
     * 导出收藏列表
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:export')")
    @Log(title = "收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumCollect forumCollect)
    {
        List<ForumCollect> list = forumCollectService.selectForumCollectList(forumCollect);
        ExcelUtil<ForumCollect> util = new ExcelUtil<ForumCollect>(ForumCollect.class);
        util.exportExcel(response, list, "收藏数据");
    }

    /**
     * 获取收藏详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:query')")
    @GetMapping(value = "/{collectId}")
    public AjaxResult getInfo(@PathVariable("collectId") Long collectId)
    {
        return success(forumCollectService.selectForumCollectByCollectId(collectId));
    }

    /**
     * 新增收藏
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:add')")
    @Log(title = "收藏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumCollect forumCollect)
    {
        return toAjax(forumCollectService.insertForumCollect(forumCollect));
    }

    /**
     * 修改收藏
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:edit')")
    @Log(title = "收藏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumCollect forumCollect)
    {
        return toAjax(forumCollectService.updateForumCollect(forumCollect));
    }

    /**
     * 删除收藏
     */
    @PreAuthorize("@ss.hasPermi('forum:collect:remove')")
    @Log(title = "收藏", businessType = BusinessType.DELETE)
	@DeleteMapping("/{collectIds}")
    public AjaxResult remove(@PathVariable Long[] collectIds)
    {
        return toAjax(forumCollectService.deleteForumCollectByCollectIds(collectIds));
    }
}
