package com.blog.web.controller.forumapi;


import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.dto.CommentDTO;
import com.blog.forum.domain.dto.CommentQueryDto;
import com.blog.forum.domain.vo.CommentDetailVO;
import com.blog.forum.service.IForumCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "评论管理")
@RestController
@RequestMapping("/api/comment")
public class ApiCommentController extends BaseController {
    @Autowired
    private IForumCommentService commentService;
    @ApiOperation("获取评论列表")
    @GetMapping("/list")
    public TableDataInfo list(CommentQueryDto commentQueryDto) {
        Long userId = null;
        try {
            LoginUser user = SecurityUtils.getLoginUser();
            if (user != null){
                userId = user.getUser().getUserId();
            }
        }catch (Exception e){
            e.printStackTrace();
        }


        startPage();
        List<CommentDetailVO> list = commentService.getCommentList(commentQueryDto.getContentType(), commentQueryDto.getContentId(), userId);
        return getDataTable(list);
    }

    @ApiOperation("发表评论")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "contentType", value = "内容类型（0帖子 1用户发帖）", required = true),
        @ApiImplicitParam(name = "contentId", value = "内容ID", required = true),
        @ApiImplicitParam(name = "parentId", value = "父评论ID（0表示一级评论）", required = true),
        @ApiImplicitParam(name = "content", value = "评论内容", required = true),
        @ApiImplicitParam(name = "attachments", value = "附件列表", required = false, dataType = "List")
    })
    @PostMapping
    public AjaxResult add(@RequestBody CommentDTO commentDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            return error("请先登录");
        }
        return commentService.addComment(commentDTO, loginUser.getUser().getUserId());
    }
    @ApiOperation("删除评论")
    @DeleteMapping("/{commentId}")
    public AjaxResult delete(@PathVariable Long commentId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            return error("请先登录");
        }
        return commentService.deleteComment(commentId, loginUser.getUser().getUserId());
    }
}
