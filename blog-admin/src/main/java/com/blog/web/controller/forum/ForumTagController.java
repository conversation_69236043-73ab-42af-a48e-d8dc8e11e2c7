package com.blog.web.controller.forum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumTag;
import com.blog.forum.domain.ForumTagI18n;
import com.blog.forum.mapper.ForumTagI18nMapper;
import com.blog.forum.service.IForumTagService;
import com.blog.forum.service.IForumTagI18nService;
import com.blog.forum.util.LangUtils;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 帖子标签Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/forum/forumTag")
public class ForumTagController extends BaseController
{
    @Autowired
    private IForumTagService forumTagService;

    @Autowired
    private IForumTagI18nService forumTagI18nService;

    @Autowired
    private ForumTagI18nMapper forumTagI18nMapper;

    /**
     * 查询帖子标签列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumTag forumTag)
    {
        startPage();
        List<ForumTag> list = forumTagService.selectForumTagList(forumTag);
        return getDataTable(list);
    }

    /**
     * 导出帖子标签列表
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:export')")
    @Log(title = "帖子标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumTag forumTag)
    {
        List<ForumTag> list = forumTagService.selectForumTagList(forumTag);
        ExcelUtil<ForumTag> util = new ExcelUtil<ForumTag>(ForumTag.class);
        util.exportExcel(response, list, "帖子标签数据");
    }

    /**
     * 获取帖子标签详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:query')")
    @GetMapping(value = "/{tagId}")
    public AjaxResult getInfo(@PathVariable("tagId") Long tagId)
    {
        // 获取标签基本信息
        ForumTag tag = forumTagService.selectForumTagByTagId(tagId);

        // 获取标签的多语言信息
        ForumTagI18n queryParam = new ForumTagI18n();
        queryParam.setTagId(tagId);
        List<ForumTagI18n> i18nList = forumTagI18nService.selectForumTagI18nList(queryParam);

        // 将多语言信息添加到返回结果中
        Map<String, Object> result = new HashMap<>();
        result.put("data", tag);

        // 将多语言数据转换为以语言代码为键的Map
        Map<String, Map<String, Object>> i18nMap = new HashMap<>();
        for (ForumTagI18n i18n : i18nList) {
            // 获取语言代码
            String langCode = getLangCodeById(i18n.getLangId());
            if (langCode != null) {
                Map<String, Object> langData = new HashMap<>();
                langData.put("langId", i18n.getLangId());
                langData.put("tagName", i18n.getTagName());
                i18nMap.put(langCode, langData);
            }
        }

        // 将多语言数据添加到返回结果中
        result.put("i18n", i18nMap);

        return AjaxResult.success(result);
    }

    /**
     * 新增帖子标签
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:add')")
    @Log(title = "帖子标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> requestData)
    {
        // 从请求中提取基本标签信息
        ForumTag forumTag = new ForumTag();

        // 设置基本属性
        if (requestData.containsKey("tagName")) {
            forumTag.setTagName((String) requestData.get("tagName"));
        }
        if (requestData.containsKey("color")) {
            forumTag.setColor((String) requestData.get("color"));
        } else {
            // 如果没有提供颜色，则生成一个随机颜色
            forumTag.setColor(generateRandomColor());
        }
        if (requestData.containsKey("sort")) {
            forumTag.setSort(Long.valueOf(requestData.get("sort").toString()));
        }
        if (requestData.containsKey("status")) {
            forumTag.setStatus((String) requestData.get("status"));
        }
        if (requestData.containsKey("remark")) {
            forumTag.setRemark((String) requestData.get("remark"));
        }

        // 插入基本标签信息
        int rows = forumTagService.insertForumTag(forumTag);

        Long currentLangId = LangUtils.getCurrentLangId();

        // 如果插入成功且包含多语言数据，则处理多语言信息
        if (rows > 0 && requestData.containsKey("i18n")) {
            Map<String, Map<String, Object>> i18nData = (Map<String, Map<String, Object>>) requestData.get("i18n");
            for (Map.Entry<String, Map<String, Object>> entry : i18nData.entrySet()) {
                String langCode = entry.getKey();
                Map<String, Object> langData = entry.getValue();

                // 创建多语言对象
                ForumTagI18n i18n = new ForumTagI18n();
                i18n.setTagId(forumTag.getTagId());

                Long langId = null;
                // 设置语言ID
                if (langData.containsKey("langId")) {
                    langId = Long.valueOf(langData.get("langId").toString());
                    i18n.setLangId(langId);
                } else {
                    // 如果没有提供语言ID，则根据语言代码获取
                    langId = getLangIdByCode(langCode);
                    if (langId != null) {
                        i18n.setLangId(langId);
                    } else {
                        continue; // 跳过无效的语言代码
                    }
                }

                String tagName = "";
                // 设置标签名称
                if (langData.containsKey("tagName")) {
                    tagName = (String) langData.get("tagName");
                    i18n.setTagName(tagName);
                }

                // 如果是默认语言，将标签名称更新到主表
                if (langId != null && langId.equals(currentLangId) && StrUtil.isNotEmpty(tagName)) {
                    forumTag.setTagName(tagName);
                    forumTagService.updateForumTag(forumTag);
                }

                // 插入多语言数据
                forumTagI18nService.insertForumTagI18n(i18n);
            }
        }

        return toAjax(rows);
    }

    /**
     * 修改帖子标签
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:edit')")
    @Log(title = "帖子标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Map<String, Object> requestData)
    {
        // 从请求中提取基本标签信息
        ForumTag forumTag = new ForumTag();

        // 设置标签ID
        if (requestData.containsKey("tagId")) {
            forumTag.setTagId(Long.valueOf(requestData.get("tagId").toString()));
        } else {
            return AjaxResult.error("缺少标签ID");
        }

        // 设置基本属性
        if (requestData.containsKey("tagName")) {
            forumTag.setTagName((String) requestData.get("tagName"));
        }
        if (requestData.containsKey("color")) {
            forumTag.setColor((String) requestData.get("color"));
        }
        if (requestData.containsKey("sort")) {
            forumTag.setSort(Long.valueOf(requestData.get("sort").toString()));
        }
        if (requestData.containsKey("status")) {
            forumTag.setStatus((String) requestData.get("status"));
        }
        if (requestData.containsKey("remark")) {
            forumTag.setRemark((String) requestData.get("remark"));
        }

        // 更新基本标签信息
        int rows = forumTagService.updateForumTag(forumTag);

        Long currentLangId = LangUtils.getCurrentLangId();

        // 如果更新成功且包含多语言数据，则处理多语言信息
        if (rows > 0 && requestData.containsKey("i18n")) {
            Map<String, Map<String, Object>> i18nData = (Map<String, Map<String, Object>>) requestData.get("i18n");
            for (Map.Entry<String, Map<String, Object>> entry : i18nData.entrySet()) {
                String langCode = entry.getKey();
                Map<String, Object> langData = entry.getValue();

                // 获取语言ID
                Long langId = null;
                if (langData.containsKey("langId")) {
                    langId = Long.valueOf(langData.get("langId").toString());
                } else {
                    // 如果没有提供语言ID，则根据语言代码获取
                    langId = getLangIdByCode(langCode);
                    if (langId == null) {
                        continue; // 跳过无效的语言代码
                    }
                }

                // 查询是否已存在该语言的标签翻译
                ForumTagI18n queryParam = new ForumTagI18n();
                queryParam.setTagId(forumTag.getTagId());
                queryParam.setLangId(langId);
                List<ForumTagI18n> existingI18n = forumTagI18nService.selectForumTagI18nList(queryParam);

                // 创建或更新多语言对象
                ForumTagI18n i18n = new ForumTagI18n();
                i18n.setTagId(forumTag.getTagId());
                i18n.setLangId(langId);

                String tagName = "";
                // 设置标签名称
                if (langData.containsKey("tagName")) {
                    tagName = (String) langData.get("tagName");
                    i18n.setTagName(tagName);
                }

                // 如果是默认语言，将标签名称更新到主表
                if (langId != null && langId.equals(currentLangId) && StrUtil.isNotEmpty(tagName)) {
                    forumTag.setTagName(tagName);
                    forumTagService.updateForumTag(forumTag);
                }

                if (existingI18n != null && !existingI18n.isEmpty()) {
                    // 更新现有的多语言数据
                    i18n.setI18nId(existingI18n.get(0).getI18nId());
                    forumTagI18nService.updateForumTagI18n(i18n);
                } else {
                    // 插入新的多语言数据
                    forumTagI18nService.insertForumTagI18n(i18n);
                }
            }
        }

        return toAjax(rows);
    }

    /**
     * 删除帖子标签
     */
    @PreAuthorize("@ss.hasPermi('forum:forumTag:remove')")
    @Log(title = "帖子标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tagIds}")
    public AjaxResult remove(@PathVariable Long[] tagIds)
    {
        // 删除标签的多语言数据
        for (Long tagId : tagIds) {
            ForumTagI18n queryParam = new ForumTagI18n();
            queryParam.setTagId(tagId);
            List<ForumTagI18n> i18nList = forumTagI18nService.selectForumTagI18nList(queryParam);
            for (ForumTagI18n i18n : i18nList) {
                forumTagI18nService.deleteForumTagI18nByI18nId(i18n.getI18nId());
            }
        }

        // 删除标签基本信息
        return toAjax(forumTagService.deleteForumTagByTagIds(tagIds));
    }

    /**
     * 根据语言ID获取语言代码
     *
     * @param langId 语言ID
     * @return 语言代码
     */
    private String getLangCodeById(Long langId) {
        // 这里需要实现根据语言ID获取语言代码的逻辑
        // 可以调用相关服务或直接查询数据库
        if (langId == 1L) {
            return "zh-CN";
        } else if (langId == 2L) {
            return "en-US";
        }
        return null;
    }

    /**
     * 根据语言代码获取语言ID
     *
     * @param langCode 语言代码
     * @return 语言ID
     */
    private Long getLangIdByCode(String langCode) {
        // 这里需要实现根据语言代码获取语言ID的逻辑
        // 可以调用相关服务或直接查询数据库
        if ("zh-CN".equals(langCode)) {
            return 1L;
        } else if ("en-US".equals(langCode)) {
            return 2L;
        }
        return null;
    }

    /**
     * 生成随机颜色
     *
     * @return 十六进制颜色值，如 #409EFF
     */
    private String generateRandomColor() {
        // 预定义的一些好看的颜色
        String[] colors = {
            "#409EFF", // 蓝色
            "#67C23A", // 绿色
            "#E6A23C", // 黄色
            "#F56C6C", // 红色
            "#909399", // 灰色
            "#8E44AD", // 紫色
            "#16A085", // 青色
            "#D35400", // 橙色
            "#2980B9", // 深蓝色
            "#27AE60"  // 深绿色
        };

        // 随机选择一个颜色
        int index = (int) (Math.random() * colors.length);
        return colors[index];
    }
}
