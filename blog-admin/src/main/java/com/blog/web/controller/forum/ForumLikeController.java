package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.enums.BusinessType;
import com.blog.forum.domain.ForumLike;
import com.blog.forum.service.IForumLikeService;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.common.core.page.TableDataInfo;

/**
 * 点赞Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/forum/like")
public class ForumLikeController extends BaseController
{
    @Autowired
    private IForumLikeService forumLikeService;

    /**
     * 查询点赞列表
     */
    @PreAuthorize("@ss.hasPermi('forum:like:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForumLike forumLike)
    {
        startPage();
        List<ForumLike> list = forumLikeService.selectForumLikeList(forumLike);
        return getDataTable(list);
    }

    /**
     * 导出点赞列表
     */
    @PreAuthorize("@ss.hasPermi('forum:like:export')")
    @Log(title = "点赞", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForumLike forumLike)
    {
        List<ForumLike> list = forumLikeService.selectForumLikeList(forumLike);
        ExcelUtil<ForumLike> util = new ExcelUtil<ForumLike>(ForumLike.class);
        util.exportExcel(response, list, "点赞数据");
    }

    /**
     * 获取点赞详细信息
     */
    @PreAuthorize("@ss.hasPermi('forum:like:query')")
    @GetMapping(value = "/{likeId}")
    public AjaxResult getInfo(@PathVariable("likeId") Long likeId)
    {
        return success(forumLikeService.selectForumLikeByLikeId(likeId));
    }

    /**
     * 新增点赞
     */
    @PreAuthorize("@ss.hasPermi('forum:like:add')")
    @Log(title = "点赞", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForumLike forumLike)
    {
        return toAjax(forumLikeService.insertForumLike(forumLike));
    }

    /**
     * 修改点赞
     */
    @PreAuthorize("@ss.hasPermi('forum:like:edit')")
    @Log(title = "点赞", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForumLike forumLike)
    {
        return toAjax(forumLikeService.updateForumLike(forumLike));
    }

    /**
     * 删除点赞
     */
    @PreAuthorize("@ss.hasPermi('forum:like:remove')")
    @Log(title = "点赞", businessType = BusinessType.DELETE)
	@DeleteMapping("/{likeIds}")
    public AjaxResult remove(@PathVariable Long[] likeIds)
    {
        return toAjax(forumLikeService.deleteForumLikeByLikeIds(likeIds));
    }
}
