package com.blog.web.controller.forum;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.blog.common.annotation.Log;
import com.blog.common.core.controller.BaseController;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.common.enums.BusinessType;
import com.blog.common.utils.poi.ExcelUtil;
import com.blog.system.domain.dto.AdminUserQueryDTO;
import com.blog.system.domain.vo.AdminUserVO;
import com.blog.forum.service.IForumUserExtendService;
import com.blog.framework.web.service.TokenService;
import com.blog.common.core.redis.RedisCache;
import com.blog.common.core.domain.model.LoginUser;

/**
 * 用户管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forum/user")
public class ForumUserController extends BaseController
{
    @Autowired
    private IForumUserExtendService userExtendService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询用户列表
     */
    @PreAuthorize("@ss.hasPermi('forum:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminUserQueryDTO queryDTO)
    {
        startPage();
        List<AdminUserVO> list = userExtendService.selectAdminUserList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出用户列表
     */
    @PreAuthorize("@ss.hasPermi('forum:user:export')")
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminUserQueryDTO queryDTO)
    {
        List<AdminUserVO> list = userExtendService.selectAdminUserList(queryDTO);
        ExcelUtil<AdminUserVO> util = new ExcelUtil<AdminUserVO>(AdminUserVO.class);
        util.exportExcel(response, list, "用户数据");
    }

    /**
     * 修改用户禁止发帖状态
     */
    @PreAuthorize("@ss.hasPermi('forum:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeBannedPost/{userId}/{isBannedPost}")
    public AjaxResult changeBannedPost(@PathVariable Long userId, @PathVariable String isBannedPost)
    {
        return userExtendService.updateUserBannedPost(userId, isBannedPost);
    }

    /**
     * 修改用户状态
     */
    @PreAuthorize("@ss.hasPermi('forum:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus/{sysUserId}/{status}")
    public AjaxResult changeStatus(@PathVariable Long sysUserId, @PathVariable String status)
    {
        AjaxResult result = userExtendService.updateUserStatus(sysUserId, status);

        // 如果是禁用用户且操作成功，清除该用户的所有token
        if (result.isSuccess() && "1".equals(status)) {
            clearUserTokens(sysUserId);
        }

        return result;
    }

    /**
     * 重置用户密码
     */
    @PreAuthorize("@ss.hasPermi('forum:user:resetPassword')")
    @Log(title = "重置用户密码", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestParam Long userId, @RequestParam String newPassword)
    {
        return userExtendService.resetUserPassword(userId, newPassword);
    }

    /**
     * 清除用户的所有token
     *
     * @param sysUserId 系统用户ID
     */
    private void clearUserTokens(Long sysUserId) {
        try {
            // 获取Redis中所有的登录token键
            String pattern = "login_tokens:*";
            java.util.Set<String> keys = (java.util.Set<String>) redisCache.keys(pattern);

            for (String key : keys) {
                Object loginUser = redisCache.getCacheObject(key);
                if (loginUser != null && loginUser instanceof LoginUser) {
                    LoginUser user = (LoginUser) loginUser;
                    if (user.getUser() != null && sysUserId.equals(user.getUser().getUserId())) {
                        // 删除该用户的token
                        redisCache.deleteObject(key);
                    }
                }
            }
        } catch (Exception e) {
            // 记录日志，但不影响主流程
            logger.error("清除用户token失败: " + e.getMessage(), e);
        }
    }
}
