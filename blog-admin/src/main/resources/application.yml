# 项目相关配置
blog:
  # 名称
  name: blog
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/blog/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/blog/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    encoding:
      # 强制使用配置的编码格式
      force: true
      # 编码格式
      charset: UTF-8
      # 强制响应编码
      force-response: true
    # 文件上传配置
    multipart:
      # 单个文件大小限制
      max-file-size: 100MB
      # 请求总大小限制
      max-request-size: 100MB
      # 启用文件上传功能
      enabled: true
      # 临时文件存储位置
      location: ${java.io.tmpdir}/blog-upload-temp

# 日志配置
logging:
  level:
    com.blog: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  mail:
    enabled: true
    host: smtp.qq.com  # 你的SMTP服务器地址
    protocol: smtp
    port: 465               # SMTP端口(SSL一般是465)
    username: <EMAIL>  # 发件人邮箱
    password: huhsrmpdfcuqfhah # 邮箱密码或授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000
    from: <EMAIL>  # 发件人地址
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  # 文件上传配置
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（7天 = 7 * 24 * 60 = 10080分钟）
  expireTime: 10080

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.blog.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
