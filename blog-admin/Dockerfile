# 使用 JDK 1.8 作为基础镜像
FROM openjdk:8-jdk-alpine

# 设置工作目录
WORKDIR /app

# 添加作者信息
LABEL maintainer="blog-admin"

# 设置时区为亚洲/上海并安装中文支持
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata && \
    # 安装中文字体和语言包
    apk add --no-cache font-noto-cjk && \
    apk add --no-cache icu-libs && \
    # 创建中文目录
    mkdir -p /usr/share/fonts/chinese

# 设置环境变量
ENV JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.language=zh -Duser.region=CN"
ENV TZ=Asia/Shanghai
ENV LANG=zh_CN.UTF-8
ENV LC_ALL=zh_CN.UTF-8

# 创建日志目录并设置权限
RUN mkdir -p /app/logs && \
    chmod -R 777 /app/logs

# 将 JAR 文件复制到容器中
COPY target/blog-admin.jar /app/blog-admin.jar

# 暴露端口
EXPOSE 8080

# 设置容器启动命令
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} -jar /app/blog-admin.jar --spring.profiles.active=druid"]
