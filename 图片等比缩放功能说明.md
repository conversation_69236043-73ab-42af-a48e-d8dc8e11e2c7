# 图片等比缩放功能实现说明

## 功能概述

实现了富文本编辑器中图片的智能等比缩放功能：
1. 上传图片后自动按比例缩放适应容器大小
2. 点击图片弹出原图预览
3. 窗口大小变化时自动重新调整图片尺寸
4. 保持图片原始比例，不变形

## 核心功能

### 1. 图片自动缩放

**缩放逻辑**：
- 获取富文本编辑器容器的实际宽度（减去padding）
- 设置最大高度为屏幕高度的40%，最大不超过400px
- 计算宽度和高度的缩放比例
- 选择较小的缩放比例，确保图片完全适应容器
- 只缩小不放大（scale ≤ 1）

```javascript
// 计算缩放比例
const containerWidth = editorContent.clientWidth - 40 // 减去padding
const containerHeight = Math.min(400, window.innerHeight * 0.4) // 最大高度

const widthRatio = containerWidth / originalWidth
const heightRatio = containerHeight / originalHeight
const scale = Math.min(widthRatio, heightRatio, 1) // 不放大，只缩小

// 计算最终尺寸
const finalWidth = Math.floor(originalWidth * scale)
const finalHeight = Math.floor(originalHeight * scale)
```

### 2. 图片样式设置

**显示样式**：
- `display: block` - 块级显示，独占一行
- `margin: 10px auto` - 居中显示，上下间距
- `cursor: pointer` - 鼠标悬停显示手型
- `border: 1px solid #ddd` - 淡灰色边框
- `border-radius: 4px` - 圆角边框
- `box-shadow` - 阴影效果
- `transition: all 0.2s ease` - 平滑过渡动画

**悬停效果**：
- 边框颜色变为蓝色 `#409EFF`
- 阴影增强并带蓝色调
- 轻微上移效果 `translateY(-2px)`

### 3. 响应式调整

**窗口大小变化监听**：
```javascript
// 添加窗口大小变化监听
window.addEventListener('resize', this.handleWindowResize)

// 防抖处理，避免频繁调整
handleWindowResize() {
  clearTimeout(this.resizeTimer)
  this.resizeTimer = setTimeout(() => {
    this.resizeAllImages()
  }, 300)
}
```

**批量调整**：
```javascript
resizeAllImages() {
  const images = this.$refs.editorContent.querySelectorAll('img')
  images.forEach(img => {
    this.adjustImageSize(img)
  })
}
```

### 4. 图片预览功能

**点击预览**：
- 点击图片触发预览对话框
- 显示原始尺寸的图片
- 使用Element UI的el-dialog组件
- 预览图片最大宽度100%，最大高度70vh
- 居中显示，带阴影效果

```javascript
// 图片点击事件
img.onclick = function(e) {
  e.preventDefault()
  this.showImagePreview(url)
}.bind(this)

// 显示预览
showImagePreview(url) {
  this.imagePreview.url = url
  this.imagePreview.visible = true
}
```

## 技术实现

### 1. 图片上传处理

```javascript
// 上传成功后的处理
if (url) {
  const img = document.createElement('img')
  img.src = url
  
  // 设置图片样式 - 等比缩放适应容器
  this.setImageDisplayStyle(img)
  
  // 添加点击事件，点击放大图片
  img.onclick = function(e) {
    e.preventDefault()
    this.showImagePreview(url)
  }.bind(this)
  
  // 图片加载完成后重新计算尺寸
  img.onload = function() {
    this.adjustImageSize(img)
  }.bind(this)
  
  // 插入到编辑器
  range.insertNode(img)
}
```

### 2. 已有图片处理

**初始化时处理**：
```javascript
initEditor() {
  if (this.value && this.$refs.editorContent) {
    this.$refs.editorContent.innerHTML = this.value
    
    // 调整已有图片的大小
    this.$nextTick(() => {
      this.resizeAllImages()
      this.setupImageClickEvents()
    })
  }
}
```

**内容变化时处理**：
```javascript
watch: {
  value: {
    handler(newVal) {
      if (this.$refs.editorContent && newVal !== this.getContent()) {
        this.$refs.editorContent.innerHTML = newVal
        // 处理新内容中的图片
        this.$nextTick(() => {
          this.resizeAllImages()
          this.setupImageClickEvents()
        })
      }
    }
  }
}
```

### 3. 事件绑定管理

**为已有图片设置事件**：
```javascript
setupImageClickEvents() {
  const images = this.$refs.editorContent.querySelectorAll('img')
  images.forEach(img => {
    // 移除已有的点击事件（避免重复绑定）
    img.onclick = null
    
    // 添加点击预览事件
    img.onclick = (e) => {
      e.preventDefault()
      this.showImagePreview(img.src)
    }
    
    // 设置样式
    this.setImageDisplayStyle(img)
  })
}
```

## CSS样式优化

### 1. 图片基础样式

```scss
img {
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: #409EFF;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transform: translateY(-2px);
  }

  // 确保图片在容器中居中显示
  &[style*="display: block"] {
    margin: 10px auto;
  }
}
```

### 2. 预览对话框样式

```scss
.image-preview-container {
  text-align: center;
  padding: 20px;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
```

## 使用效果

### ✅ 图片显示优化
- 图片自动适应容器大小，不会超出编辑器范围
- 保持原始宽高比，不会变形
- 居中显示，视觉效果更好
- 有清晰的边框和阴影，层次分明

### ✅ 交互体验提升
- 鼠标悬停有明显的视觉反馈
- 点击图片可以查看原图
- 预览对话框支持大图查看
- 响应式设计，适应不同屏幕尺寸

### ✅ 性能优化
- 使用防抖处理窗口大小变化
- 避免重复绑定事件
- 精确计算图片尺寸，避免布局抖动

## 测试建议

### 1. 基础功能测试
- 上传不同尺寸的图片，验证自动缩放效果
- 点击图片，验证预览功能
- 调整浏览器窗口大小，验证响应式调整

### 2. 边界情况测试
- 上传超大图片（如4K图片）
- 上传超小图片（如图标）
- 在移动设备上测试响应式效果

### 3. 兼容性测试
- 测试不同浏览器的兼容性
- 测试图片格式兼容性（JPG、PNG、GIF、WebP等）
- 测试编辑器内容的保存和加载

## 注意事项

1. **图片加载时机**：使用`img.onload`确保图片完全加载后再调整尺寸
2. **事件管理**：正确添加和移除事件监听器，避免内存泄漏
3. **防抖处理**：窗口大小变化使用防抖，避免频繁计算
4. **样式优先级**：使用内联样式确保样式生效
5. **响应式设计**：考虑不同屏幕尺寸的适配

## 扩展功能建议

1. **图片编辑**：可以添加图片旋转、裁剪等功能
2. **懒加载**：对于大量图片可以实现懒加载
3. **图片压缩**：上传前可以进行图片压缩
4. **拖拽调整**：支持拖拽调整图片大小
5. **图片标注**：支持在图片上添加文字标注
