# 修改密码页面更新说明

## 需求分析

用户反馈当前的重置密码页面逻辑有问题：
- 现有逻辑：通过验证码重置密码（适用于忘记密码的场景）
- 用户需求：通过原密码修改密码（适用于已知密码但想更换的场景）

## 修改内容

### 1. 页面结构调整

**修改前**：
- 邮箱
- 验证码（需要发送验证码）
- 新密码
- 确认密码

**修改后**：
- 邮箱
- 原密码
- 新密码
- 确认密码

### 2. 国际化支持

#### 中文翻译新增
```javascript
changePassword:'修改密码',
changePasswordSuccess:'密码修改成功',
changePasswordFailed:'密码修改失败，请重试',
```

#### 英文翻译新增
```javascript
changePassword:'Change Password',
changePasswordSuccess:'Password changed successfully',
changePasswordFailed:'Password change failed, please try again',
```

### 3. 表单验证更新

#### 新增原密码验证
```javascript
const validateOldPassword = (rule: any, value: string, callback: Function) => {
    if (value === '') {
        callback(new Error(t('oldPasswordRequired')))
    } else {
        callback()
    }
}
```

#### 更新新密码验证
```javascript
const validateNewPassword = (rule: any, value: string, callback: Function) => {
    if (value === '') {
        callback(new Error(t('newPasswordRequired')))
    } else {
        if (form.value.confirmPassword !== '') {
            if (formRef.value) {
                formRef.value.validateField('confirmPassword')
            }
        }
        callback()
    }
}
```

#### 更新确认密码验证
```javascript
const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
    if (value === '') {
        callback(new Error(t('confirmPasswordRequired')))
    } else if (value !== form.value.newPassword) {
        callback(new Error(t('passwordMismatch')))
    } else {
        callback()
    }
}
```

### 4. 验证规则更新

```javascript
const rules = {
    email: [
        { required: true, message: t('msg4'), trigger: 'blur' },
        { type: 'email', message: t('msg5'), trigger: 'blur' }
    ],
    oldPassword: [
        { validator: validateOldPassword, trigger: 'blur' },
        { min: 6, message: t('msg6'), trigger: 'blur' }
    ],
    newPassword: [
        { validator: validateNewPassword, trigger: 'blur' },
        { min: 6, message: t('msg6'), trigger: 'blur' }
    ],
    confirmPassword: [
        { validator: validateConfirmPassword, trigger: 'blur' }
    ]
}
```

### 5. API调用更新

**修改前**：
```javascript
let datas = await axios.post('/api/auth/password/change', {
    email: form.value.email,
    code: form.value.code,
    newPassword: form.value.newPassword
});
```

**修改后**：
```javascript
let datas = await axios.post('/api/auth/password/change', {
    email: form.value.email,
    oldPassword: form.value.oldPassword,
    newPassword: form.value.newPassword
});
```

### 6. 用户体验优化

#### 成功提示国际化
```javascript
if (datas.code == 200) {
    ElMessage.success(t('changePasswordSuccess'))
    router.push('/auth/login')
}
```

#### 错误提示国际化
```javascript
} catch (error) {
    console.error('Password change failed:', error)
    ElMessage.error(t('changePasswordFailed'))
}
```

### 7. 代码清理

- 移除了不需要的验证码相关功能
- 删除了发送验证码的函数和倒计时逻辑
- 清理了不使用的导入（Key图标）
- 移除了sendingCode和countdown变量

## 页面功能说明

### 表单字段

1. **邮箱**：
   - 必填项
   - 邮箱格式验证
   - 用于标识用户身份

2. **原密码**：
   - 必填项
   - 最少6位字符
   - 用于验证用户身份

3. **新密码**：
   - 必填项
   - 最少6位字符
   - 不能与原密码相同（后端验证）

4. **确认密码**：
   - 必填项
   - 必须与新密码一致

### 验证流程

1. **前端验证**：
   - 邮箱格式验证
   - 密码长度验证
   - 确认密码一致性验证

2. **后端验证**：
   - 用户存在性验证
   - 原密码正确性验证
   - 新密码与原密码不同验证

### 国际化支持

页面完全支持中英文切换：
- 所有标签文本国际化
- 所有提示信息国际化
- 所有错误信息国际化

## 使用场景

### 适用场景
- 用户记得当前密码，想要更换新密码
- 定期更换密码的安全需求
- 密码泄露后的主动更换

### 不适用场景
- 用户忘记密码（应使用忘记密码功能）
- 管理员重置用户密码（应使用管理后台功能）

## 安全特性

1. **身份验证**：通过原密码验证用户身份
2. **密码强度**：要求密码至少6位字符
3. **防重复**：后端验证新密码不能与原密码相同
4. **加密传输**：密码通过HTTPS加密传输
5. **加密存储**：新密码使用BCrypt加密存储

## 测试验证

### 测试用例

1. **正常修改密码**：
   - 输入正确的邮箱和原密码
   - 输入符合要求的新密码
   - 确认密码与新密码一致
   - 提交成功，跳转到登录页面

2. **原密码错误**：
   - 输入错误的原密码
   - 后端返回"原密码错误"

3. **新密码与原密码相同**：
   - 输入与原密码相同的新密码
   - 后端返回"新密码不能与原密码相同"

4. **确认密码不一致**：
   - 确认密码与新密码不一致
   - 前端验证失败，显示错误提示

5. **国际化测试**：
   - 切换语言，验证所有文本正确显示

## 注意事项

1. **API接口**：确保后端 `/api/auth/password/change` 接口正常工作
2. **密码验证**：后端使用BCrypt验证原密码
3. **用户体验**：修改成功后自动跳转到登录页面
4. **错误处理**：提供清晰的错误提示信息

现在修改密码页面已经完全更新，支持通过原密码验证身份来修改密码，并且完全支持国际化。
