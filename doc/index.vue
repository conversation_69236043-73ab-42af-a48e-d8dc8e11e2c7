<template>
  <div class="create-post">
    <div class="container">
      <h1>{{$t('posts.postMessage')}}</h1>
      <!-- <h4 style="font-family:Miskan;color:#000;font-size:24px">sky</h4> -->

      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="handleSubmit">
        <el-form-item :label="$t('posts.title')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('posts.title')" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('posts.description')" prop="summary">
          <el-input v-model="form.summary" :placeholder="$t('posts.description')" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('posts.catogory')" prop="category">
          <el-select v-model="form.categoryId" :placeholder="$t('posts.catogory')">
            <el-option v-for="item in categories" :key="item.categoryId" :label="item.categoryName"
              :value="item.categoryId" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('posts.label')" prop="tags">
          <el-select v-model="form.tagIds" multiple filterable default-first-option :placeholder="$t('posts.label')"
            :max-collapse-tags="2">
            <el-option v-for="tag in tabs" :key="tag" :label="tag.tagName" :value="tag.tagId" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('posts.content')" prop="content">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('bold') }"
                  @click="editor?.chain().focus().toggleBold().run()">
                  B
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('italic') }"
                  @click="editor?.chain().focus().toggleItalic().run()">
                  <i>I</i>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('strike') }"
                  @click="editor?.chain().focus().toggleStrike().run()">
                  <el-icon>
                    <DeleteFilled />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('code') }"
                  @click="editor?.chain().focus().toggleCode().run()">
                  <el-icon>
                    <CollectionTag />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()">
                  H1
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()">
                  H2
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }"
                  @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()">
                  H3
                </el-button>
                <el-button>
                  <el-popover class="box-item" title="" content="Bottom Center prompts info" placement="bottom">
                    <template #reference>
                      <img style="width: 20px;" src="../../static/smil.png" />
                    </template>
                    <div style="max-height: 300px;overflow: auto;">
                      <span @click="onSelectEmoji(item)" style="padding: 5px;cursor: pointer;" v-for="item,i in emoji"
                        :key="i">{{item}}</span>
                    </div>
                  </el-popover>
                </el-button>

               
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('bulletList') }"
                  @click="editor?.chain().focus().toggleBulletList().run()">
                  <el-icon>
                    <List />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('orderedList') }"
                  @click="editor?.chain().focus().toggleOrderedList().run()">
                  <el-icon>
                    <Sort />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('blockquote') }"
                  @click="editor?.chain().focus().toggleBlockquote().run()">
                  <el-icon>
                    <ChatSquare />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('link') }" @click="addLink">
                  <el-icon>
                    <LinkIcon />
                  </el-icon>
                </el-button>
                <el-button @click="uploadImage">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </el-button>
                <el-button @click="uploadFile">
                  <el-icon>
                    <Files />
                  </el-icon>
                </el-button>

              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button :class="{ 'is-active': editor?.isActive('taskList') }"
                  @click="editor?.chain().focus().toggleTaskList().run()">
                  <el-icon>
                    <Check />
                  </el-icon>
                </el-button>
                <el-button :class="{ 'is-active': editor?.isActive('horizontalRule') }"
                  @click="editor?.chain().focus().setHorizontalRule().run()">
                  <el-icon>
                    <Minus />
                  </el-icon>
                </el-button>
              </el-button-group>

              <el-divider direction="vertical" />

              <el-button-group>
                <el-button @click="editor?.chain().focus().undo().run()">
                  <el-icon>
                    <Back />
                  </el-icon>
                </el-button>
                <el-button @click="editor?.chain().focus().redo().run()">
                  <el-icon>
                    <Right />
                  </el-icon>
                </el-button>
              </el-button-group>
                  <el-select v-model="fontFamilyValue" @change="selectFont" placeholder="fontfamily" style="width: 120px;" size="mini" clearable>
                    <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
            </div>

            <div class="editor-content">
              <div style="max-height: 500px;overflow: auto;">
                <editor-content :editor="editor" />
              </div>
            </div>
          </div>
        </el-form-item>

        <div class="form-footer">
          <el-button @click="handleCancel">{{$t('cancel')}}</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{$t('confirm')}}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">


  import { ref, computed, onBeforeUnmount, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { Editor, EditorContent } from '@tiptap/vue-3'
  import StarterKit from '@tiptap/starter-kit'
  import Image from '@tiptap/extension-image'
  import Link from '@tiptap/extension-link'
  import TaskList from '@tiptap/extension-task-list'
  import TaskItem from '@tiptap/extension-task-item'

  import TextStyle from '@tiptap/extension-text-style'
  import FontFamily from '@tiptap/extension-font-family'

  import { ElMessage } from 'element-plus'
  import * as ElementPlusIconsVue from '@element-plus/icons-vue'
  import type { FormInstance } from 'element-plus'
  import axios from '@/utils/request'
  import emoji from '@/utils/emoji'
  import fontFamily from '@/utils/font-family'

  const router = useRouter()
  const formRef = ref < FormInstance > ()
  const loading = ref(false)
  const fontFamilyValue = ref('')
  import { useI18n } from 'vue-i18n'
  const {t}=useI18n()
  const editor = ref < Editor | null > (null)
  const {
    Bold,
    Italic,
    DeleteFilled,
    Terminal,
    List,
    Sort,
    ChatSquare,
    Back,
    Right,
    Picture,
    Files,
    Link: LinkIcon,
    Check,
    Minus
  } = ElementPlusIconsVue

  // 在组件挂载后初始化编辑器
  onMounted(() => {
    getCates();
    getTab();
    editor.value = new Editor({

      extensions: [
        StarterKit,
        TextStyle,  // 必须放在 FontFamily 之前
        FontFamily.configure({
          types: ['textStyle'],  // 指定应用到的节点类型
        }),
        Image.configure({
          HTMLAttributes: {
            class: 'editor-image'
          }
        }),
        Link.configure({
          HTMLAttributes: {
            class: 'editor-link'
          }
        }),
        TaskList,
        TaskItem
      ],
      content: form.value.content,
      onUpdate: ({ editor }) => {
        form.value.content = editor.getHTML()
      }
    })
  })

  // 在组件卸载前销毁编辑器
  onBeforeUnmount(() => {
    if (editor.value) {
      editor.value.destroy()
    }
  })
  const getTab = async () => {

    try {
      const tab = await axios.get('/api/post/tags', {});
      if (tab.code == 200) {
        tabs.value = tab.data
      }
    } catch {

    }
  }
  const form = ref({
    title: '',
    categoryId: '',
    tagIds: [],
    content: '',
    coverImage: '',
    attachments: [],
    summary: '',
  })

  const rules = {
    title: [
      { required: true, message: t('pmsg1'), trigger: 'blur' },
      { min: 2, max: 100, message: t('pmsg2'), trigger: 'blur' }
    ],
    categoryId: [
      { required: true, message: t('pmsg3'), trigger: 'change' }
    ],
    tagIds: [
      { required: true, message: '请至少选择一个标签', trigger: 'change' },
      {
        validator: (_: any, value: string[]) => {
          if (value.length > 3) {
            return Promise.reject('最多只能选择3个标签')
          }
          return Promise.resolve()
        },
        trigger: 'change'
      }
    ],
    content: [
      {
        validator: (_: any, value: string) => {
          if (!editor.value?.getText().trim()) {
            return Promise.reject('请输入内容')
          }
          return Promise.resolve()
        },
        trigger: 'blur'
      }
    ]
  }

  const categories = ref([])
  const tabs = ref([])



  const handleSubmit = async () => {
    if (!formRef.value || !editor.value) return
    const postData = {
      ...form.value,
      content: editor.value.getHTML()
    }
    console.log(postData)
    try {
      loading.value = true
      // await formRef.value.validate()

      const postData = {
        ...form.value,
        content: editor.value.getHTML()
      }
      const ret = await axios.postBody('/api/user_post/submit', postData)
      console.log("ret----", ret)
      if (ret.code == 200) {
        ElMessage.success(ret.msg)
      } else {
        ElMessage.Error(ret.msg)

      }

    } catch (error) {
      console.error('Failed to create post:', error)
      ElMessage.error(error instanceof Error ? error.message : '发布失败，请重试')
    } finally {
      loading.value = false
    }
  }

  const handleCancel = () => {
    router.back()
  }
  const getCates = async () => {
    const cates = await axios.get('/api/post/category/list', {})

    if (cates.code == 200) {
      categories.value = cates.data
    }
  }
  // 添加链接
  const addLink = () => {
    const url = window.prompt('请输入链接地址')
    if (url) {
      editor.value?.chain().focus().setLink({ href: url }).run()
    }
  }

  // 上传图片
  const uploadImage = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)
        const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
        if (data.code == 200 && data.url) {
          editor.value?.chain().focus().setImage({ src: data.url }).run()
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('图片上传失败')
      }
    }
    input.click()
  }
  // 选择表情
  const onSelectEmoji = (emoji) => {
    editor.value?.chain().focus().insertContent(`${emoji}`).run()
  }
  // 设置字体
  const selectFont = () => {
    // console.log(fontFamilyValue.value)
    editor.value?.chain().focus().setFontFamily(fontFamilyValue.value).run()

  }
  // 上传文件
  const uploadFile = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'zip/zip'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      console.log("file===", file)
      if (!file) return

      try {
        const formData = new FormData()
        formData.append('file', file)
        const data = await axios.postBody("/api/oss/upload?bizType=forum", formData)
        if (data.code == 200 && data.url) {
          editor.value?.chain().focus().insertContent(`<a download='${file.name}' target='_blank' href="${data.url}">${file.name}</a>`).run()
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('图片上传失败')
      }
    }
    input.click()
  }
</script>

<style lang="scss" scoped>
  .create-post {
    padding: 24px 0;

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 16px;
    }

    h1 {
      margin-bottom: 24px;
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
    }
  }

  .editor-container {
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 8px;
    border-bottom: 1px solid $border-color-base;
    background-color: $background-color-light;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      padding: 8px;

      &.is-active {
        color: $primary-color;
        background-color: rgba($primary-color, 0.1);
      }
    }
  }

  .editor-content {
    padding: 16px;
    min-height: 300px;
    background-color: $background-color-white;

    :deep(.ProseMirror) {
      min-height: 300px;
      outline: none;

      >*+* {
        margin-top: 0.75em;
      }

      ul,
      ol {
        padding: 0 1rem;
      }

      h1 {
        font-size: 2em;
      }

      h2 {
        font-size: 1.5em;
      }

      h3 {
        font-size: 1.25em;
      }

      blockquote {
        padding-left: 1rem;
        border-left: 2px solid $border-color-base;
        color: $text-secondary;
      }

      code {
        background-color: $background-color-light;
        color: $text-regular;
        padding: 0.2em 0.4em;
        border-radius: $border-radius-small;
      }

      pre {
        background: $background-color-base;
        color: $text-regular;
        padding: 0.75em 1em;
        border-radius: $border-radius-base;

        code {
          background: none;
          color: inherit;
          padding: 0;
        }
      }

      img {
        max-width: 100%;
        height: auto;
      }

      a {
        color: $primary-color;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      ul[data-type="taskList"] {
        list-style: none;
        padding: 0;

        li {
          display: flex;
          align-items: center;

          >label {
            flex: 0 0 auto;
            margin-right: 0.5rem;
            user-select: none;
          }

          >div {
            flex: 1 1 auto;
          }
        }
      }
    }
  }

  .form-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  @media (max-width: $breakpoint-sm) {
    .create-post {
      padding: 16px;
    }

    .editor-toolbar {
      .el-button {
        padding: 6px;
      }
    }
  }

  .editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem 0;
  }

  .editor-link {
    color: $primary-color;
    text-decoration: underline;
  }
</style>