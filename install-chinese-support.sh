#!/bin/bash

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要root权限运行" 
   exit 1
fi

echo "=== 安装中文支持 ==="

# 安装中文语言包
echo "正在安装中文语言包..."
yum -y install langpacks-zh_CN

# 安装中文字体
echo "正在安装中文字体..."
yum -y install wqy-microhei-fonts wqy-zenhei-fonts

# 设置系统默认语言为中文
echo "正在设置系统默认语言..."
localectl set-locale LANG=zh_CN.UTF-8

# 安装必要的工具
echo "正在安装必要的工具..."
yum -y install vim wget curl net-tools

# 设置环境变量
echo "正在设置环境变量..."
cat > /etc/profile.d/locale.sh << EOF
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
EOF

# 使环境变量生效
source /etc/profile.d/locale.sh

# 输出完成信息
echo ""
echo "中文支持安装完成！"
echo "当前系统语言设置："
echo "LANG=$LANG"
echo "LC_ALL=$LC_ALL"
echo ""
echo "请重新登录系统或执行 'source /etc/profile' 使设置生效。"
