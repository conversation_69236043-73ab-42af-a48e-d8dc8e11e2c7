# 解决模块循环依赖问题

## 问题描述

在为用户禁用功能添加清除token逻辑时，遇到了模块循环依赖问题：

```
java: Annotation processing is not supported for module cycles. 
Please ensure that all modules from cycle [blog-forum,blog-framework] are excluded from annotation processing
```

## 问题原因

### 循环依赖关系
- `blog-forum` 依赖 `blog-framework`（为了使用TokenService）
- `blog-framework` 依赖 `blog-forum`（已存在的依赖）

这形成了循环依赖，导致Maven编译时注解处理失败。

## 解决方案

### 方案选择：将清除token逻辑移到blog-admin模块

选择这个方案的原因：
1. **职责分离**：用户管理是管理功能，应该在admin模块中处理
2. **避免循环依赖**：admin模块可以依赖framework模块，不会产生循环依赖
3. **架构清晰**：业务逻辑和框架逻辑分离

## 具体修改

### 1. 移除blog-forum中的TokenService依赖

#### 移除导入
```java
// 移除这些导入
import com.blog.framework.web.service.TokenService;
import com.blog.common.core.redis.RedisCache;
```

#### 移除注入
```java
// 移除这些注入
@Autowired
private TokenService tokenService;

@Autowired
private RedisCache redisCache;
```

#### 简化updateUserStatus方法
```java
// 移除清除token的逻辑，只保留基本的状态更新
int rows = sysUserMapper.updateUser(sysUser);
if (rows > 0) {
    return AjaxResult.success(StringUtils.equals(status, "0") ? "启用成功" : "停用成功");
}
return AjaxResult.error("操作失败");
```

#### 移除clearUserTokens方法
完全移除了`clearUserTokens`方法。

### 2. 在blog-admin中添加清除token逻辑

#### 添加依赖注入
```java
import com.blog.framework.web.service.TokenService;
import com.blog.common.core.redis.RedisCache;
import com.blog.common.core.domain.model.LoginUser;

@Autowired
private TokenService tokenService;

@Autowired
private RedisCache redisCache;
```

#### 修改changeStatus方法
```java
@PutMapping("/changeStatus/{sysUserId}/{status}")
public AjaxResult changeStatus(@PathVariable Long sysUserId, @PathVariable String status)
{
    AjaxResult result = userExtendService.updateUserStatus(sysUserId, status);
    
    // 如果是禁用用户且操作成功，清除该用户的所有token
    if (result.isSuccess() && "1".equals(status)) {
        clearUserTokens(sysUserId);
    }
    
    return result;
}
```

#### 添加clearUserTokens方法
```java
/**
 * 清除用户的所有token
 *
 * @param sysUserId 系统用户ID
 */
private void clearUserTokens(Long sysUserId) {
    try {
        // 获取Redis中所有的登录token键
        String pattern = "login_tokens:*";
        java.util.Set<String> keys = redisCache.keys(pattern);
        
        for (String key : keys) {
            Object loginUser = redisCache.getCacheObject(key);
            if (loginUser != null && loginUser instanceof LoginUser) {
                LoginUser user = (LoginUser) loginUser;
                if (user.getUser() != null && sysUserId.equals(user.getUser().getUserId())) {
                    // 删除该用户的token
                    redisCache.deleteObject(key);
                }
            }
        }
    } catch (Exception e) {
        // 记录日志，但不影响主流程
        logger.error("清除用户token失败: " + e.getMessage(), e);
    }
}
```

### 3. 移除循环依赖

#### 修改blog-forum/pom.xml
```xml
<!-- 移除这个依赖 -->
<dependency>
    <groupId>com.blog</groupId>
    <artifactId>blog-framework</artifactId>
</dependency>
```

## 修改后的模块依赖关系

### 修改前（循环依赖）
```
blog-forum ←→ blog-framework
```

### 修改后（正常依赖）
```
blog-admin → blog-framework
blog-admin → blog-forum
blog-framework → blog-forum (保留，用于其他功能)
```

## 功能验证

### 测试步骤
1. **编译测试**：确认没有循环依赖错误
2. **功能测试**：
   - 禁用用户时，验证用户token被清除
   - 禁用用户后，验证用户无法登录
   - 启用用户时，验证功能正常

### 预期结果
- ✅ 编译成功，无循环依赖错误
- ✅ 禁用用户时自动清除token
- ✅ 用户状态管理功能正常
- ✅ 系统架构更加清晰

## 架构优势

### 1. 职责分离
- **blog-forum**：专注于论坛业务逻辑
- **blog-admin**：专注于管理功能
- **blog-framework**：专注于框架功能

### 2. 依赖关系清晰
- 避免了循环依赖
- 模块间依赖关系更加合理
- 便于后续维护和扩展

### 3. 代码组织合理
- 管理功能集中在admin模块
- 业务逻辑和框架逻辑分离
- 便于团队协作开发

## 注意事项

### 1. 模块设计原则
- 避免循环依赖
- 保持单一职责
- 依赖方向应该是单向的

### 2. 后续开发建议
- 在添加新功能时，注意模块间的依赖关系
- 管理功能应该放在admin模块
- 框架功能应该放在framework模块
- 业务功能应该放在对应的业务模块

### 3. 依赖管理
- 定期检查模块间的依赖关系
- 避免不必要的依赖
- 保持依赖关系的简洁性

现在循环依赖问题已经解决，系统可以正常编译和运行，用户禁用功能也能正常工作。
