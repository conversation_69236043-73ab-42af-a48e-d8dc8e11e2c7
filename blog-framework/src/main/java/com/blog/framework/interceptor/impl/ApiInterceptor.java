package com.blog.framework.interceptor.impl;

import com.alibaba.fastjson2.JSON;
import com.blog.common.constant.HttpStatus;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.utils.SecurityUtils;
import com.blog.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Component
public class ApiInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();

        // 1. 非API请求直接放行
        if (!requestURI.startsWith("/api/")) {
            return true;
        }

        // 2. 放行所有以 /api/post/ 开头的请求
        if (requestURI.startsWith("/api/post/")) {  // 新增规则
            return true;
        }
        if (requestURI.startsWith("/api/comment/")) {  // 新增规则
            return true;
        }

        // 3. 放行其他公开API（原有的认证相关路径）
        String[] openApis = {
                "/api/auth/email/code",
                "/api/auth/register",
                "/api/auth/login",
                "/api/auth/password/reset",
                "/api/auth/password/change"
        };
        if (Arrays.asList(openApis).contains(requestURI)) {
            return true;
        }

        // 4. 验证登录状态
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, "用户未登录")));
            return false;
        }

        // 5. 验证用户状态
        if ("1".equals(loginUser.getUser().getStatus())) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(JSON.toJSONString(AjaxResult.error(HttpStatus.FORBIDDEN, "用户已被禁用")));
            return false;
        }

        return true;
    }
}
