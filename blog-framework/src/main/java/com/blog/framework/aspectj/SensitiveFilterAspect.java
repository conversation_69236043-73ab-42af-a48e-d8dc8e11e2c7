package com.blog.framework.aspectj;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.blog.common.annotation.SensitiveFilter;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.system.service.ISensitiveWordService;

/**
 * 敏感词过滤处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class SensitiveFilterAspect
{
    private static final Logger log = LoggerFactory.getLogger(SensitiveFilterAspect.class);
    
    @Autowired
    private ISensitiveWordService sensitiveWordService;
    
    /**
     * 拦截所有返回值，对标记了@SensitiveFilter注解的字段进行敏感词过滤
     */
    @AfterReturning(pointcut = "execution(* com.blog.web.controller..*.*(..))", returning = "returnValue")
    public void doAfterReturning(JoinPoint joinPoint, Object returnValue) throws Throwable
    {
        if (returnValue == null)
        {
            return;
        }

        // 先检查返回值是否包含需要过滤的字段，如果没有则直接返回
        if (!containsSensitiveFilterAnnotation(returnValue))
        {
            return;
        }

        // 处理AjaxResult
        if (returnValue instanceof AjaxResult)
        {
            AjaxResult ajaxResult = (AjaxResult) returnValue;
            Object data = ajaxResult.get(AjaxResult.DATA_TAG);
            if (data != null)
            {
                processObject(data);
            }
        }
        // 处理TableDataInfo
        else if (returnValue instanceof TableDataInfo)
        {
            TableDataInfo tableDataInfo = (TableDataInfo) returnValue;
            List<?> rows = tableDataInfo.getRows();
            if (rows != null && !rows.isEmpty())
            {
                for (Object row : rows)
                {
                    processObject(row);
                }
            }
        }
        // 处理List
        else if (returnValue instanceof List)
        {
            List<?> list = (List<?>) returnValue;
            if (!list.isEmpty())
            {
                for (Object item : list)
                {
                    processObject(item);
                }
            }
        }
        // 处理Map
        else if (returnValue instanceof Map)
        {
            Map<?, ?> map = (Map<?, ?>) returnValue;
            if (!map.isEmpty())
            {
                for (Object value : map.values())
                {
                    processObject(value);
                }
            }
        }
        // 处理其他对象
        else
        {
            processObject(returnValue);
        }
    }
    
    /**
     * 处理对象，过滤敏感词
     * 
     * @param obj 对象
     * @throws IllegalAccessException 反射异常
     */
    private void processObject(Object obj) throws IllegalAccessException
    {
        if (obj == null)
        {
            return;
        }

        // 如果是基本类型、字符串、数组或日期类型，直接返回
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean ||
            obj instanceof Character || obj.getClass().isArray() ||
            obj instanceof java.util.Date || obj instanceof java.time.LocalDateTime ||
            obj instanceof java.time.LocalDate || obj instanceof java.time.LocalTime ||
            obj.getClass().isPrimitive())
        {
            return;
        }

        // 如果是Java内置类型，直接返回
        String className = obj.getClass().getName();
        if (className.startsWith("java.") || className.startsWith("javax.") ||
            className.startsWith("sun.") || className.startsWith("com.sun."))
        {
            return;
        }


        
        // 如果是集合，处理集合中的每个元素
        if (obj instanceof Collection)
        {
            Collection<?> collection = (Collection<?>) obj;
            for (Object item : collection)
            {
                processObject(item);
            }
            return;
        }
        
        // 如果是Map，处理Map中的每个值
        if (obj instanceof Map)
        {
            Map<?, ?> map = (Map<?, ?>) obj;
            for (Object value : map.values())
            {
                processObject(value);
            }
            return;
        }
        
        // 获取对象的所有字段
        Class<?> clazz = obj.getClass();
        Field[] fields = getAllFields(clazz);
        
        for (Field field : fields)
        {
            // 设置字段可访问
            field.setAccessible(true);
            
            // 检查字段是否标记了@SensitiveFilter注解
            SensitiveFilter annotation = field.getAnnotation(SensitiveFilter.class);
            if (annotation != null && annotation.enabled())
            {
                // 获取字段值
                Object fieldValue = field.get(obj);
                if (fieldValue instanceof String)
                {
                    String originalValue = (String) fieldValue;
                    // 过滤敏感词
                    String filteredValue = sensitiveWordService.filterSensitiveWord(originalValue);
                    // 设置过滤后的值
                    field.set(obj, filteredValue);
                }
                else if (fieldValue != null)
                {
                    // 如果字段值不是字符串，递归处理
                    processObject(fieldValue);
                }
            }
            else
            {
                // 如果字段没有标记注解，检查是否需要递归处理
                Object fieldValue = field.get(obj);
                if (fieldValue != null && shouldProcessRecursively(fieldValue))
                {
                    processObject(fieldValue);
                }
            }
        }
    }

    /**
     * 判断对象是否需要递归处理
     *
     * @param obj 对象
     * @return 是否需要递归处理
     */
    private boolean shouldProcessRecursively(Object obj)
    {
        if (obj == null)
        {
            return false;
        }

        // 基本类型和包装类型不需要递归处理
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean ||
            obj instanceof Character || obj.getClass().isArray() ||
            obj instanceof java.util.Date || obj instanceof java.time.LocalDateTime ||
            obj instanceof java.time.LocalDate || obj instanceof java.time.LocalTime ||
            obj.getClass().isPrimitive())
        {
            return false;
        }

        // Java内置类型不需要递归处理
        String className = obj.getClass().getName();
        if (className.startsWith("java.") || className.startsWith("javax.") ||
            className.startsWith("sun.") || className.startsWith("com.sun."))
        {
            return false;
        }

        // 集合和Map需要递归处理
        if (obj instanceof Collection || obj instanceof Map)
        {
            return true;
        }

        // 自定义对象需要递归处理
        return className.startsWith("com.blog.");
    }

    /**
     * 获取类的所有字段，包括父类的字段
     * 
     * @param clazz 类
     * @return 所有字段
     */
    private Field[] getAllFields(Class<?> clazz)
    {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null)
        {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields)
            {
                fieldList.add(field);
            }
            clazz = clazz.getSuperclass();
        }
        return fieldList.toArray(new Field[0]);
    }

    /**
     * 检查返回值是否包含需要过滤的字段
     *
     * @param returnValue 返回值
     * @return 是否包含需要过滤的字段
     */
    private boolean containsSensitiveFilterAnnotation(Object returnValue)
    {
        if (returnValue == null)
        {
            return false;
        }

        // 处理AjaxResult
        if (returnValue instanceof AjaxResult)
        {
            AjaxResult ajaxResult = (AjaxResult) returnValue;
            Object data = ajaxResult.get(AjaxResult.DATA_TAG);
            return data != null && containsSensitiveFilterAnnotation(data);
        }

        // 处理TableDataInfo
        if (returnValue instanceof TableDataInfo)
        {
            TableDataInfo tableDataInfo = (TableDataInfo) returnValue;
            List<?> rows = tableDataInfo.getRows();
            if (rows != null && !rows.isEmpty())
            {
                // 只检查第一个元素，如果第一个元素有注解，认为整个列表都需要过滤
                return containsSensitiveFilterAnnotation(rows.get(0));
            }
            return false;
        }

        // 处理List
        if (returnValue instanceof List)
        {
            List<?> list = (List<?>) returnValue;
            if (!list.isEmpty())
            {
                // 只检查第一个元素
                return containsSensitiveFilterAnnotation(list.get(0));
            }
            return false;
        }

        // 处理Map
        if (returnValue instanceof Map)
        {
            Map<?, ?> map = (Map<?, ?>) returnValue;
            if (!map.isEmpty())
            {
                // 检查第一个值
                Object firstValue = map.values().iterator().next();
                return containsSensitiveFilterAnnotation(firstValue);
            }
            return false;
        }

        // 检查对象本身是否有@SensitiveFilter注解的字段
        return hasAnnotatedFields(returnValue);
    }

    /**
     * 检查对象是否有标记了@SensitiveFilter注解的字段
     *
     * @param obj 对象
     * @return 是否有标记注解的字段
     */
    private boolean hasAnnotatedFields(Object obj)
    {
        if (obj == null)
        {
            return false;
        }

        // 基本类型不需要检查
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean ||
            obj instanceof Character || obj.getClass().isArray() ||
            obj instanceof java.util.Date || obj instanceof java.time.LocalDateTime ||
            obj instanceof java.time.LocalDate || obj instanceof java.time.LocalTime ||
            obj.getClass().isPrimitive())
        {
            return false;
        }

        // Java内置类型不需要检查
        String className = obj.getClass().getName();
        if (className.startsWith("java.") || className.startsWith("javax.") ||
            className.startsWith("sun.") || className.startsWith("com.sun."))
        {
            return false;
        }

        // 只检查自定义对象
        if (!className.startsWith("com.blog."))
        {
            return false;
        }

        // 获取对象的所有字段
        Class<?> clazz = obj.getClass();
        Field[] fields = getAllFields(clazz);

        for (Field field : fields)
        {
            // 检查字段是否标记了@SensitiveFilter注解
            SensitiveFilter annotation = field.getAnnotation(SensitiveFilter.class);
            if (annotation != null && annotation.enabled())
            {
                return true;
            }
        }

        return false;
    }
}
