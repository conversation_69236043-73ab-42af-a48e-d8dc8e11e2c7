# 清除缓存解决vue-i18n错误

## 问题描述

修改了国际化文件中的邮箱地址，使用字符串拼接避免vue-i18n解析@符号，但是浏览器仍然显示编译错误，说明存在缓存问题。

## 已完成的修复

### 中文文件 (block/src/lang/zh.ts)
```javascript
// 修复前
contacts:'联系后台邮箱：**************或****************',

// 修复后  
contacts:'联系后台邮箱：peakhy' + '@' + '126.com或598090079' + '@' + 'qq.com',
```

### 英文文件 (block/src/lang/en.ts)
```javascript
// 修复前
contacts:'Contact the backend email: peakhy@126.<NAME_EMAIL>',

// 修复后
contacts:'Contact the backend email: peakhy' + '@' + '126.com or 598090079' + '@' + 'qq.com',
```

## 清除缓存步骤

### 1. 停止开发服务器
在终端中按 `Ctrl + C` 停止当前运行的开发服务器。

### 2. 清除项目缓存
```bash
# 删除 node_modules/.cache 目录
rm -rf node_modules/.cache

# 或者在 Windows 中
rmdir /s node_modules\.cache

# 清除 Vite 缓存
rm -rf node_modules/.vite

# 或者在 Windows 中  
rmdir /s node_modules\.vite
```

### 3. 清除浏览器缓存
- **Chrome/Edge**: 按 `Ctrl + Shift + R` 强制刷新
- **Firefox**: 按 `Ctrl + F5` 强制刷新
- **或者**: 打开开发者工具，右键刷新按钮，选择"清空缓存并硬性重新加载"

### 4. 重启开发服务器
```bash
npm run dev
# 或者
yarn dev
# 或者
pnpm dev
```

### 5. 验证修复
1. 打开浏览器开发者工具的 Console 标签
2. 检查是否还有 vue-i18n 编译错误
3. 验证邮箱地址是否正常显示

## 预期结果

修复成功后应该：
- ✅ 控制台没有 vue-i18n 编译错误
- ✅ 邮箱地址正常显示为：`<EMAIL>`
- ✅ 国际化功能正常工作
- ✅ 语言切换正常

## 如果问题仍然存在

### 检查其他可能的位置

1. **检查是否有其他文件使用了相同的邮箱地址**：
```bash
# 在项目根目录搜索
grep -r "<EMAIL>" src/
grep -r "<EMAIL>" src/
```

2. **检查是否有硬编码的邮箱地址**：
```bash
# 搜索所有包含@符号的文件
grep -r "@" src/ --include="*.vue" --include="*.ts" --include="*.js"
```

### 替代解决方案

如果字符串拼接仍然有问题，可以尝试：

#### 方案1：使用HTML实体
```javascript
contacts:'联系后台邮箱：peakhy&#64;126.com或598090079&#64;qq.com',
```

#### 方案2：使用模板字符串
```javascript
contacts:`联系后台邮箱：peakhy${'@'}126.com或598090079${'@'}qq.com`,
```

#### 方案3：使用变量
```javascript
const at = '@'
contacts:`联系后台邮箱：peakhy${at}126.com或598090079${at}qq.com`,
```

#### 方案4：分离邮箱地址
```javascript
contacts:'联系后台邮箱：',
email1:'peakhy' + '@' + '126.com',
email2:'598090079' + '@' + 'qq.com',
```

然后在模板中使用：
```vue
<template>
  {{ $t('contacts') }}{{ $t('email1') }}或{{ $t('email2') }}
</template>
```

## 调试技巧

### 1. 检查编译后的内容
在浏览器开发者工具中：
```javascript
// 检查国际化对象
console.log(this.$i18n.messages.zh.contacts)
console.log(this.$i18n.messages.en.contacts)
```

### 2. 临时测试
可以临时修改为简单的文本测试：
```javascript
contacts:'联系后台邮箱：test-email',
```

如果这样不报错，说明问题确实是@符号引起的。

### 3. 检查vue-i18n版本
```bash
npm list vue-i18n
```

某些版本的vue-i18n对@符号的处理可能有差异。

## 总结

这个问题的根本原因是vue-i18n将@符号识别为链接语法的特殊字符。通过字符串拼接的方式可以避免这个问题，但需要清除缓存才能看到效果。

按照上述步骤清除缓存后，vue-i18n编译错误应该会消失。
