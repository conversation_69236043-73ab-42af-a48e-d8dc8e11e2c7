# 论坛用户密码重置功能

## 功能概述

在后台管理系统中，管理员可以为论坛用户重置密码。此功能位于"论坛管理" -> "用户管理"页面。

## 功能特点

### 🎯 主要功能
1. **管理员重置用户密码**：管理员可以为任何用户重置密码
2. **密码验证**：新密码需要确认输入，确保输入正确
3. **安全检查**：禁用用户无法重置密码
4. **操作日志**：重置密码操作会记录到系统日志

### 🔧 技术实现

#### 后端实现
- **控制器**：`ForumUserExtendController.resetPassword()`
- **权限控制**：`@PreAuthorize("@ss.hasPermi('forum:userExtend:resetPassword')")`
- **操作日志**：`@Log(title = "重置用户密码", businessType = BusinessType.UPDATE)`

#### 前端实现
- **页面**：`blog-ui/src/views/forum/userExtend/index.vue`
- **API**：`blog-ui/src/api/forum/userExtend.js`
- **权限指令**：`v-hasPermi="['forum:userExtend:resetPassword']"`

## 使用方法

### 1. 权限配置
首先需要执行SQL脚本添加权限：
```sql
-- 执行 blog/sql/forum_user_reset_password_permission.sql
```

### 2. 操作步骤
1. 登录后台管理系统
2. 进入"论坛管理" -> "用户管理"
3. 在用户列表中找到要重置密码的用户
4. 点击该用户行的"重置密码"按钮
5. 在弹出的对话框中输入新密码
6. 确认新密码
7. 点击"确定"完成重置

### 3. 界面说明

#### 重置密码对话框包含：
- **用户ID**：显示用户ID（只读）
- **用户邮箱**：显示用户邮箱（只读）
- **新密码**：输入新密码（6-20位）
- **确认密码**：再次输入新密码进行确认

## API接口

### 重置用户密码
```
POST /forum/userExtend/resetPassword
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| newPassword | String | 是 | 新密码 |

#### 请求示例
```javascript
resetUserPassword(123, 'newPassword123')
```

#### 响应示例
```json
{
    "code": 200,
    "msg": "密码重置成功",
    "data": null
}
```

## 安全特性

### 1. 权限控制
- 需要 `forum:userExtend:resetPassword` 权限
- 只有管理员可以执行此操作

### 2. 数据验证
- 检查用户是否存在
- 检查用户状态（禁用用户无法重置密码）
- 密码长度验证（6-20位）
- 密码确认验证

### 3. 密码安全
- 使用 `SecurityUtils.encryptPassword()` 加密密码
- 密码不会在日志中明文显示

### 4. 操作审计
- 所有重置密码操作都会记录到系统日志
- 包含操作人、操作时间、目标用户等信息

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 用户不存在 | 用户ID无效 | 检查用户ID是否正确 |
| 用户已被禁用，无法重置密码 | 目标用户被禁用 | 先启用用户再重置密码 |
| 新密码不能为空 | 密码输入为空 | 输入有效密码 |
| 密码长度为6-20位 | 密码长度不符合要求 | 调整密码长度 |
| 两次输入的密码不一致 | 确认密码不匹配 | 重新输入确认密码 |

## 前端代码示例

### Vue组件使用
```vue
<template>
  <el-button
    size="mini"
    type="text"
    icon="el-icon-key"
    @click="handleResetPassword(scope.row)"
    v-hasPermi="['forum:userExtend:resetPassword']"
  >重置密码</el-button>
</template>

<script>
import { resetUserPassword } from "@/api/forum/userExtend";

export default {
  methods: {
    handleResetPassword(row) {
      // 显示重置密码对话框
      this.resetPasswordForm = {
        userId: row.userId,
        email: row.email,
        newPassword: '',
        confirmPassword: ''
      };
      this.resetPasswordOpen = true;
    },
    
    submitResetPassword() {
      this.$refs["resetPasswordForm"].validate(valid => {
        if (valid) {
          resetUserPassword(this.resetPasswordForm.userId, this.resetPasswordForm.newPassword)
            .then(response => {
              this.$modal.msgSuccess("密码重置成功");
              this.resetPasswordOpen = false;
            });
        }
      });
    }
  }
}
</script>
```

## 注意事项

1. **权限管理**：确保只有授权的管理员才能使用此功能
2. **密码策略**：建议设置强密码策略，当前最低要求6位
3. **用户通知**：重置密码后建议通知用户新密码
4. **安全审计**：定期检查密码重置操作日志
5. **备份恢复**：重要操作前建议备份用户数据

## 扩展功能建议

1. **密码强度检查**：添加密码复杂度验证
2. **邮件通知**：重置密码后自动发送邮件通知用户
3. **临时密码**：生成临时密码，用户首次登录时强制修改
4. **批量重置**：支持批量重置多个用户密码
5. **密码历史**：记录密码历史，防止重复使用旧密码
