#!/bin/bash

# 设置应用目录
APP_DIR=/data/blog-admin
PID_FILE=$APP_DIR/application.pid

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "PID file not found. Application may not be running."
    exit 1
fi

# 读取PID
PID=$(cat $PID_FILE)

# 检查进程是否存在
if ! ps -p $PID > /dev/null; then
    echo "Process with PID $PID not found. Application may have crashed."
    rm -f $PID_FILE
    exit 1
fi

# 停止应用
echo "Stopping application with PID: $PID"
kill $PID

# 等待进程结束
echo "Waiting for application to stop..."
for i in {1..30}; do
    if ! ps -p $PID > /dev/null; then
        echo "Application stopped successfully."
        rm -f $PID_FILE
        exit 0
    fi
    sleep 1
done

# 如果进程仍然存在，强制终止
echo "Application did not stop gracefully. Forcing termination..."
kill -9 $PID
rm -f $PID_FILE
echo "Application terminated."
