以下是为您优化后的需求文档，已结构化并添加模拟数据方案，可直接用于生成代码：

---

# **Vue3 + Element Plus 博客/论坛前端需求文档**

## **一、技术规格**
```markdown
- 框架：Vue3 + Composition API
- UI库：Element Plus (v2.3.9+)
- 编辑器：TinyMCE 6.x
- 路由：Vue Router 4
- 状态管理：Pinia
- 请求库：Axios
- 模拟数据：Mock.js
- 响应式方案：
  - Element Plus 响应式栅格
  - CSS媒体查询
  - 动态rem适配
```

---

## **二、页面结构说明**

### **1. 首页 (`/home`)**
| 模块         | 功能描述                                                                 | 组件示例                                                                 |
|--------------|------------------------------------------------------------------------|-------------------------------------------------------------------------|
| 轮播图       | 显示3-5张宣传图，自动播放                                               | `<el-carousel>`                                                         |
| 帖子列表     | 分页显示帖子（模拟10条/页）                                              | `<el-table>` + 自定义列                                                 |
| 分类筛选     | 按预设类型过滤（技术/生活/问答）                                          | `<el-select>`                                                           |
| 排序控制     | 最新/最热排序                                                           | `<el-radio-group>`                                                      |
| 搜索框       | 关键词实时搜索                                                          | `<el-input>` + 防抖                                                     |
| 操作按钮     | 点赞(❤️)/评论(💬)/分享(📤)                                              | 图标按钮 + 点击事件                                                     |
| 快速发帖入口 | 浮动按钮跳转到"我要说"页面                                                | `<el-button type="primary" circle class="float-btn">+</el-button>`       |

### **2. 我要说 (`/create-post`)**
| 模块          | 功能描述                                 | 组件示例                                                                 |
|---------------|----------------------------------------|-------------------------------------------------------------------------|
| 标题输入       | 限制50个字符                           | `<el-input maxlength="50" show-word-limit>`                             |
| 富文本编辑器   | 支持图文混排                           | `<Editor v-model="content" />` (封装TinyMCE)                            |
| 分类选择       | 单选分类                               | `<el-select>`                                                           |
| 发布按钮       | 表单验证后提交                         | `<el-button @click="submit">发布</el-button>`                            |

### **3. 个人中心 (`/profile`)**
| 模块          | 功能描述                               | 组件示例                                                                 |
|---------------|--------------------------------------|-------------------------------------------------------------------------|
| 个人信息       | 显示头像、用户名、邮箱                 | `<el-avatar>` + `<el-descriptions>`                                     |
| 导航菜单       | Tab切换不同内容                       | `<el-tabs>`                                                             |
| 我的帖子       | 显示用户发布的帖子列表                 | `<PostList :data="myPosts" />`                                          |
| 我的评论       | 显示用户评论记录                       | `<CommentList />`                                                       |
| 我的收藏       | 显示收藏的帖子                         | `<PostList :data="favorites" />`                                        |
| 账户设置       | 修改密码/头像/删除账号                 | `<el-form>` + 验证逻辑                                                  |

---

## **三、核心功能实现方案**

### **1. 请求封装示例**
```javascript
// src/utils/request.js
import axios from 'axios'
import { ElMessage } from 'element-plus'

const service = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(config => {
  if (localStorage.token) {
    config.headers.Authorization = `Bearer ${localStorage.token}`
  }
  return config
})

// 响应拦截器
service.interceptors.response.use(
  response => response.data,
  error => {
    ElMessage.error(error.response?.data?.message || '请求失败')
    return Promise.reject(error)
  }
)

export default service
```

### **2. 模拟数据方案**
```javascript
// src/mocks/post.js
import Mock from 'mockjs'

Mock.mock('/api/posts', 'get', {
  'data|10': [{
    'id|+1': 1,
    'title': '@ctitle(15,25)',
    'content': '@cparagraph(3,5)',
    'author': '@cname',
    'likes|0-1000': 1,
    'comments|0-500': 1,
    'category': ['技术', '生活', '问答'][Mock.mock('@integer(0,2)')],
    'createTime': Mock.mock('@datetime')
  }]
})
```

### **3. 响应式布局示例**
```vue
<!-- 帖子列表项组件 -->
<template>
  <div class="post-item">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="18" :md="20">
        <h3>{{ title }}</h3>
        <p class="content">{{ summary }}</p>
      </el-col>
      <el-col :xs="24" :sm="6" :md="4">
        <div class="meta">
          <span>❤️ {{ likes }}</span>
          <span>💬 {{ comments }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss">
.post-item {
  padding: 16px;
  border-bottom: 1px solid #eee;
  
  @media (max-width: 768px) {
    .meta {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
}
</style>
```

---

## **四、可复制代码示例**

### **注册表单组件**
```vue
<template>
  <el-form :model="form" label-width="80px">
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email">
        <template #append>
          <el-button @click="sendCode" :disabled="countdown > 0">
            {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
          </el-button>
        </template>
      </el-input>
    </el-form-item>
    
    <el-form-item label="验证码" prop="code">
      <el-input v-model="form.code" />
    </el-form-item>

    <!-- 其他字段... -->
  </el-form>
</template>

<script setup>
import { ref } from 'vue'

const countdown = ref(0)

const sendCode = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    if (countdown.value <= 0) clearInterval(timer)
    else countdown.value--
  }, 1000)
}
</script>
```

---

## **五、扩展建议**
1. **组件库优化**：
   ```bash
   npm install @element-plus/icons-vue  # 安装图标库
   ```

2. **开发效率工具**：
   ```javascript
   // vite.config.js 配置按需加载
   import AutoImport from 'unplugin-auto-import/vite'
   import Components from 'unplugin-vue-components/vite'
   import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

   export default {
     plugins: [
       AutoImport({ resolvers: [ElementPlusResolver()] }),
       Components({ resolvers: [ElementPlusResolver()] }),
     ]
   }
   ```

3. **样式规范**：
   ```scss
   // variables.scss
   $primary-color: #409EFF;
   $success-color: #67C23A;
   $warning-color: #E6A23C;
   $danger-color: #F56C6C;
   ```

---

此文档可直接提供给 Cursor，配合提示词如：  
`/实现首页帖子列表组件，使用element-plus表格，带模拟数据`  
`/创建注册页面，包含邮箱验证码功能`  
`/实现响应式帖子卡片组件，适配移动端`  

建议开发顺序：  
1. 搭建基础路由  
2. 实现公共布局  
3. 开发模拟数据  
4. 逐个页面实现