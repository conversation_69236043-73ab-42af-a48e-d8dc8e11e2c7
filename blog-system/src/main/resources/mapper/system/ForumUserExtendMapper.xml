<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.system.mapper.ForumUserExtendMapper">

    <resultMap type="ForumUserExtend" id="ForumUserExtendResult">
        <result property="userId"    column="user_id"    />
        <result property="sysUserId" column="sys_user_id" />
        <result property="nickname"    column="nickname"    />
        <result property="avatar"    column="avatar"    />
        <result property="bio"    column="bio"    />
        <result property="postCount"    column="post_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="collectCount"    column="collect_count"    />
        <result property="shareCount"    column="share_count"    />
        <result property="isBannedPost"    column="is_banned_post"    />
        <result property="updateTime"    column="update_time"    />
        <result property="email"    column="email"    />
        <result property="emailVerified"    column="email_verified"    />
    </resultMap>

    <sql id="selectForumUserExtendVo">
        select
            user_id as userId,
            nickname,
            avatar,
            bio,
            post_count as postCount,
            comment_count as commentCount,
            like_count as likeCount,
            collect_count as collectCount,
            share_count as shareCount,
            is_banned_post as isBannedPost,
            update_time as updateTime,
            email,
            sys_user_id as sysUserId,
            email_verified as emailVerified
        from forum_user_extend
    </sql>

    <select id="selectForumUserExtendList" parameterType="ForumUserExtend" resultMap="ForumUserExtendResult">
        <include refid="selectForumUserExtendVo"/>
        <where>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="bio != null  and bio != ''"> and bio = #{bio}</if>
            <if test="postCount != null "> and post_count = #{postCount}</if>
            <if test="commentCount != null "> and comment_count = #{commentCount}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="collectCount != null "> and collect_count = #{collectCount}</if>
            <if test="shareCount != null "> and share_count = #{shareCount}</if>
            <if test="isBannedPost != null  and isBannedPost != ''"> and is_banned_post = #{isBannedPost}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="emailVerified != null "> and email_verified = #{emailVerified}</if>
        </where>
    </select>

    <select id="selectForumUserExtendByUserId" parameterType="Long" resultType="ForumUserExtend">
        <include refid="selectForumUserExtendVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertForumUserExtend" parameterType="ForumUserExtend">
        insert into forum_user_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatar != null">avatar,</if>
            <if test="bio != null">bio,</if>
            <if test="postCount != null">post_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="collectCount != null">collect_count,</if>
            <if test="shareCount != null">share_count,</if>
            <if test="isBannedPost != null">is_banned_post,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="emailVerified != null">email_verified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="bio != null">#{bio},</if>
            <if test="postCount != null">#{postCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="collectCount != null">#{collectCount},</if>
            <if test="shareCount != null">#{shareCount},</if>
            <if test="isBannedPost != null">#{isBannedPost},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="emailVerified != null">#{emailVerified},</if>
         </trim>
    </insert>

    <update id="updateForumUserExtend" parameterType="ForumUserExtend">
        update forum_user_extend
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="bio != null">bio = #{bio},</if>
            <if test="postCount != null">post_count = #{postCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="collectCount != null">collect_count = #{collectCount},</if>
            <if test="shareCount != null">share_count = #{shareCount},</if>
            <if test="isBannedPost != null">is_banned_post = #{isBannedPost},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="emailVerified != null">email_verified = #{emailVerified},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteForumUserExtendByUserId" parameterType="Long">
        delete from forum_user_extend where user_id = #{userId}
    </delete>

    <delete id="deleteForumUserExtendByUserIds" parameterType="String">
        delete from forum_user_extend where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="selectForumUserExtendByEmail" parameterType="String" resultMap="ForumUserExtendResult">
        SELECT * FROM forum_user_extend WHERE email = #{email}
    </select>

    <select id="checkEmailUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM forum_user_extend WHERE email = #{email}
    </select>

    <select id="checkNicknameUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM forum_user_extend WHERE nickname = #{nickname}
    </select>

    <select id="checkUserBannedPost" resultType="boolean">
        SELECT is_banned_post = '1'
        FROM forum_user_extend
        WHERE user_id = #{userId}
    </select>

    <select id="selectUserIdBySysUserId" resultType="Long">
        SELECT user_id
        FROM forum_user_extend
        WHERE sys_user_id = #{sysUserId}
    </select>

    <update id="incrementPostCount">
        UPDATE forum_user_extend
        SET post_count = post_count + 1
        WHERE user_id = #{userId}
    </update>

    <update id="incrementShareCount">
        UPDATE forum_user_extend
        SET share_count = share_count + 1
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserInfo">
        UPDATE forum_user_extend
        <set>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="bio != null">bio = #{bio},</if>
            update_time = sysdate()
        </set>
        WHERE user_id = #{userId}
    </update>

    <!-- 查询后台用户列表 -->
    <select id="selectAdminUserList" parameterType="com.blog.system.domain.dto.AdminUserQueryDTO"
            resultType="com.blog.system.domain.vo.AdminUserVO">
        SELECT
            e.user_id as userId,
            e.sys_user_id as sysUserId,
            u.user_name as userName,
            e.nickname,
            u.sex,
            e.email,
            e.avatar,
            e.bio,
            e.post_count as postConut,
            e.comment_count as commentCount,
            e.like_count as likeCount,
            e.collect_count as collectCount,
            e.is_banned_post as isBannedPost,
            u.status,
            u.login_date as loginDate
        FROM
            forum_user_extend e
        LEFT JOIN
            sys_user u ON e.sys_user_id = u.user_id
        <where>
            u.del_flag = '0'
            <if test="nameOrNickname != null and nameOrNickname != ''">
                AND (u.user_name LIKE CONCAT('%', #{nameOrNickname}, '%') OR e.nickname LIKE CONCAT('%', #{nameOrNickname}, '%'))
            </if>
            <if test="email != null and email != ''">
                AND e.email LIKE CONCAT('%', #{email}, '%')
            </if>
        </where>
        ORDER BY u.login_date DESC
    </select>

    <!-- 更新用户禁止发帖状态 -->
    <update id="updateUserBannedPost">
        UPDATE forum_user_extend
        SET is_banned_post = #{isBannedPost}
        WHERE user_id = #{userId}
    </update>
</mapper>
