<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.system.mapper.SysFileUploadMapper">
    
    <resultMap type="SysFileUpload" id="SysFileUploadResult">
        <result property="uploadId"    column="upload_id"    />
        <result property="fileIdentifier"    column="file_identifier"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="chunkTotal"    column="chunk_total"    />
        <result property="bizType"    column="biz_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysFileUploadVo">
        select upload_id, file_identifier, file_name, file_path, file_url, file_size, chunk_total, biz_type, status, create_time, update_time from sys_file_upload
    </sql>

    <select id="selectSysFileUploadList" parameterType="SysFileUpload" resultMap="SysFileUploadResult">
        <include refid="selectSysFileUploadVo"/>
        <where>  
            <if test="fileIdentifier != null  and fileIdentifier != ''"> and file_identifier = #{fileIdentifier}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysFileUploadById" parameterType="Long" resultMap="SysFileUploadResult">
        <include refid="selectSysFileUploadVo"/>
        where upload_id = #{uploadId}
    </select>
    
    <select id="selectSysFileUploadByFileIdentifier" parameterType="String" resultMap="SysFileUploadResult">
        <include refid="selectSysFileUploadVo"/>
        where file_identifier = #{fileIdentifier}
    </select>
        
    <insert id="insertSysFileUpload" parameterType="SysFileUpload" useGeneratedKeys="true" keyProperty="uploadId">
        insert into sys_file_upload
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileIdentifier != null">file_identifier,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="chunkTotal != null">chunk_total,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileIdentifier != null">#{fileIdentifier},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="chunkTotal != null">#{chunkTotal},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysFileUpload" parameterType="SysFileUpload">
        update sys_file_upload
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileIdentifier != null">file_identifier = #{fileIdentifier},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="chunkTotal != null">chunk_total = #{chunkTotal},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where upload_id = #{uploadId}
    </update>

    <delete id="deleteSysFileUploadById" parameterType="Long">
        delete from sys_file_upload where upload_id = #{uploadId}
    </delete>

    <delete id="deleteSysFileUploadByIds" parameterType="String">
        delete from sys_file_upload where upload_id in 
        <foreach item="uploadId" collection="array" open="(" separator="," close=")">
            #{uploadId}
        </foreach>
    </delete>
</mapper>
