<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.system.mapper.SysOssMapper">

    <resultMap type="SysOss" id="SysOssResult">
        <result property="ossId"    column="oss_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalName"    column="original_name"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="mimeType"    column="mime_type"    />
        <result property="serviceType"    column="service_type"    />
        <result property="bizType"    column="biz_type"    />
        <result property="bizId"    column="biz_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysOssVo">
        select oss_id, file_name, original_name, file_suffix, file_url, file_path, file_size, mime_type, service_type, biz_type, biz_id, create_by, create_time, update_by, update_time, remark from sys_oss
    </sql>

    <select id="selectSysOssList" parameterType="SysOss" resultMap="SysOssResult">
        <include refid="selectSysOssVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalName != null  and originalName != ''"> and original_name like concat('%', #{originalName}, '%')</if>
            <if test="fileSuffix != null  and fileSuffix != ''"> and file_suffix = #{fileSuffix}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="mimeType != null  and mimeType != ''"> and mime_type = #{mimeType}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="bizId != null "> and biz_id = #{bizId}</if>
        </where>
    </select>

    <select id="selectSysOssByOssId" parameterType="Long" resultMap="SysOssResult">
        <include refid="selectSysOssVo"/>
        where oss_id = #{ossId}
    </select>
    <select id="selectByUrlList" resultType="com.blog.system.domain.SysOss">
        select
            *
        from
            sys_oss
        where file_url in
              <foreach collection="urlList" item="url" close=")" open="(" separator=",">
                #{url}
              </foreach>
             and status = 0
    </select>

    <insert id="insertSysOss" parameterType="SysOss" useGeneratedKeys="true" keyProperty="ossId">
        insert into sys_oss
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalName != null">original_name,</if>
            <if test="fileSuffix != null">file_suffix,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="mimeType != null">mime_type,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalName != null">#{originalName},</if>
            <if test="fileSuffix != null">#{fileSuffix},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="mimeType != null">#{mimeType},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysOss" parameterType="SysOss">
        update sys_oss
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalName != null">original_name = #{originalName},</if>
            <if test="fileSuffix != null">file_suffix = #{fileSuffix},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="mimeType != null">mime_type = #{mimeType},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="bizId != null">biz_id = #{bizId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where oss_id = #{ossId}
    </update>

    <delete id="deleteSysOssByOssId" parameterType="Long">
        delete from sys_oss where oss_id = #{ossId}
    </delete>

    <delete id="deleteSysOssByOssIds" parameterType="String">
        delete from sys_oss where oss_id in
        <foreach item="ossId" collection="array" open="(" separator="," close=")">
            #{ossId}
        </foreach>
    </delete>
</mapper>
