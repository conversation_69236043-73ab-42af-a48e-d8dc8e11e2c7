<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.system.mapper.SensitiveWordMapper">
    
    <resultMap type="SensitiveWord" id="SensitiveWordResult">
        <result property="wordId"    column="word_id"    />
        <result property="word"      column="word"      />
        <result property="type"      column="type"      />
        <result property="status"    column="status"    />
        <result property="isSpecified" column="is_specified" />
        <result property="createBy"  column="create_by"  />
        <result property="createTime" column="create_time" />
        <result property="updateBy"  column="update_by"  />
        <result property="updateTime" column="update_time" />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSensitiveWordVo">
        select word_id, word, type, status, is_specified, create_by, create_time, update_by, update_time, remark from sys_sensitive_word
    </sql>

    <select id="selectSensitiveWordList" parameterType="SensitiveWord" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        <where>  
            <if test="word != null  and word != ''"> and word like concat('%', #{word}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isSpecified != null"> and is_specified = #{isSpecified}</if>
        </where>
    </select>
    
    <select id="selectSensitiveWordById" parameterType="Long" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where word_id = #{wordId}
    </select>
    
    <select id="selectAllEnabledSensitiveWords" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where status = '0'
    </select>
    
    <select id="selectAllSpecifiedSensitiveWords" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where is_specified = 1 and status = '0'
    </select>
    
    <select id="selectSensitiveWordByWord" parameterType="String" resultMap="SensitiveWordResult">
        <include refid="selectSensitiveWordVo"/>
        where word = #{word}
    </select>
        
    <insert id="insertSensitiveWord" parameterType="SensitiveWord" useGeneratedKeys="true" keyProperty="wordId">
        insert into sys_sensitive_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="word != null">word,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="isSpecified != null">is_specified,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="word != null">#{word},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="isSpecified != null">#{isSpecified},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSensitiveWord" parameterType="SensitiveWord">
        update sys_sensitive_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="word != null">word = #{word},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isSpecified != null">is_specified = #{isSpecified},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where word_id = #{wordId}
    </update>
    
    <update id="updateSensitiveWordSpecified">
        update sys_sensitive_word set is_specified = #{isSpecified} where word_id = #{wordId}
    </update>

    <delete id="deleteSensitiveWordById" parameterType="Long">
        delete from sys_sensitive_word where word_id = #{wordId}
    </delete>

    <delete id="deleteSensitiveWordByIds" parameterType="String">
        delete from sys_sensitive_word where word_id in 
        <foreach item="wordId" collection="array" open="(" separator="," close=")">
            #{wordId}
        </foreach>
    </delete>
</mapper>
