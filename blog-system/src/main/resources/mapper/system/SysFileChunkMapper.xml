<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.system.mapper.SysFileChunkMapper">
    
    <resultMap type="SysFileChunk" id="SysFileChunkResult">
        <result property="chunkId"    column="chunk_id"    />
        <result property="fileIdentifier"    column="file_identifier"    />
        <result property="chunkNumber"    column="chunk_number"    />
        <result property="chunkSize"    column="chunk_size"    />
        <result property="totalChunks"    column="total_chunks"    />
        <result property="totalSize"    column="total_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="chunkPath"    column="chunk_path"    />
        <result property="bizType"    column="biz_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysFileChunkVo">
        select chunk_id, file_identifier, chunk_number, chunk_size, total_chunks, total_size, file_name, chunk_path, biz_type, status, create_time, update_time from sys_file_chunk
    </sql>

    <select id="selectSysFileChunkList" parameterType="SysFileChunk" resultMap="SysFileChunkResult">
        <include refid="selectSysFileChunkVo"/>
        <where>  
            <if test="fileIdentifier != null  and fileIdentifier != ''"> and file_identifier = #{fileIdentifier}</if>
            <if test="chunkNumber != null "> and chunk_number = #{chunkNumber}</if>
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysFileChunkById" parameterType="Long" resultMap="SysFileChunkResult">
        <include refid="selectSysFileChunkVo"/>
        where chunk_id = #{chunkId}
    </select>
    
    <select id="selectSysFileChunkByFileIdentifier" parameterType="String" resultMap="SysFileChunkResult">
        <include refid="selectSysFileChunkVo"/>
        where file_identifier = #{fileIdentifier}
        order by chunk_number asc
    </select>
    
    <select id="selectSysFileChunkByFileIdentifierAndChunkNumber" resultMap="SysFileChunkResult">
        <include refid="selectSysFileChunkVo"/>
        where file_identifier = #{fileIdentifier} and chunk_number = #{chunkNumber}
    </select>
        
    <insert id="insertSysFileChunk" parameterType="SysFileChunk" useGeneratedKeys="true" keyProperty="chunkId">
        insert into sys_file_chunk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileIdentifier != null">file_identifier,</if>
            <if test="chunkNumber != null">chunk_number,</if>
            <if test="chunkSize != null">chunk_size,</if>
            <if test="totalChunks != null">total_chunks,</if>
            <if test="totalSize != null">total_size,</if>
            <if test="fileName != null">file_name,</if>
            <if test="chunkPath != null">chunk_path,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileIdentifier != null">#{fileIdentifier},</if>
            <if test="chunkNumber != null">#{chunkNumber},</if>
            <if test="chunkSize != null">#{chunkSize},</if>
            <if test="totalChunks != null">#{totalChunks},</if>
            <if test="totalSize != null">#{totalSize},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="chunkPath != null">#{chunkPath},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysFileChunk" parameterType="SysFileChunk">
        update sys_file_chunk
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileIdentifier != null">file_identifier = #{fileIdentifier},</if>
            <if test="chunkNumber != null">chunk_number = #{chunkNumber},</if>
            <if test="chunkSize != null">chunk_size = #{chunkSize},</if>
            <if test="totalChunks != null">total_chunks = #{totalChunks},</if>
            <if test="totalSize != null">total_size = #{totalSize},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="chunkPath != null">chunk_path = #{chunkPath},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where chunk_id = #{chunkId}
    </update>

    <delete id="deleteSysFileChunkById" parameterType="Long">
        delete from sys_file_chunk where chunk_id = #{chunkId}
    </delete>

    <delete id="deleteSysFileChunkByIds" parameterType="String">
        delete from sys_file_chunk where chunk_id in 
        <foreach item="chunkId" collection="array" open="(" separator="," close=")">
            #{chunkId}
        </foreach>
    </delete>
    
    <delete id="deleteSysFileChunkByFileIdentifier" parameterType="String">
        delete from sys_file_chunk where file_identifier = #{fileIdentifier}
    </delete>
</mapper>
