package com.blog.system.mapper;

import java.util.List;
import com.blog.system.domain.SensitiveWord;

/**
 * 敏感词Mapper接口
 * 
 * <AUTHOR>
 */
public interface SensitiveWordMapper 
{
    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词ID
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordById(Long wordId);

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord);

    /**
     * 查询所有有效的敏感词
     * 
     * @return 敏感词列表
     */
    public List<SensitiveWord> selectAllEnabledSensitiveWords();
    
    /**
     * 查询所有指定的敏感词
     * 
     * @return 敏感词列表
     */
    public List<SensitiveWord> selectAllSpecifiedSensitiveWords();
    
    /**
     * 根据敏感词内容查询敏感词
     * 
     * @param word 敏感词内容
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordByWord(String word);

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int insertSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int updateSensitiveWord(SensitiveWord sensitiveWord);
    
    /**
     * 设置敏感词指定状态
     * 
     * @param wordId 敏感词ID
     * @param isSpecified 是否指定
     * @return 结果
     */
    public int updateSensitiveWordSpecified(Long wordId, Boolean isSpecified);

    /**
     * 删除敏感词
     * 
     * @param wordId 敏感词ID
     * @return 结果
     */
    public int deleteSensitiveWordById(Long wordId);

    /**
     * 批量删除敏感词
     * 
     * @param wordIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSensitiveWordByIds(Long[] wordIds);
}
