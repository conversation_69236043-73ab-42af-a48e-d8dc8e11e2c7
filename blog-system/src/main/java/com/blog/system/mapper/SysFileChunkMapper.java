package com.blog.system.mapper;

import com.blog.system.domain.SysFileChunk;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件分片Mapper接口
 *
 * <AUTHOR>
 */
public interface SysFileChunkMapper {
    /**
     * 查询文件分片
     *
     * @param chunkId 分片ID
     * @return 文件分片
     */
    public SysFileChunk selectSysFileChunkById(Long chunkId);

    /**
     * 查询文件分片列表
     *
     * @param sysFileChunk 文件分片
     * @return 文件分片集合
     */
    public List<SysFileChunk> selectSysFileChunkList(SysFileChunk sysFileChunk);

    /**
     * 新增文件分片
     *
     * @param sysFileChunk 文件分片
     * @return 结果
     */
    public int insertSysFileChunk(SysFileChunk sysFileChunk);

    /**
     * 修改文件分片
     *
     * @param sysFileChunk 文件分片
     * @return 结果
     */
    public int updateSysFileChunk(SysFileChunk sysFileChunk);

    /**
     * 删除文件分片
     *
     * @param chunkId 分片ID
     * @return 结果
     */
    public int deleteSysFileChunkById(Long chunkId);

    /**
     * 批量删除文件分片
     *
     * @param chunkIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysFileChunkByIds(Long[] chunkIds);

    /**
     * 根据文件标识查询分片
     *
     * @param fileIdentifier 文件标识
     * @return 分片列表
     */
    public List<SysFileChunk> selectSysFileChunkByFileIdentifier(String fileIdentifier);

    /**
     * 根据文件标识和分片编号查询分片
     *
     * @param fileIdentifier 文件标识
     * @param chunkNumber 分片编号
     * @return 文件分片
     */
    public SysFileChunk selectSysFileChunkByFileIdentifierAndChunkNumber(@Param("fileIdentifier") String fileIdentifier, @Param("chunkNumber") Integer chunkNumber);

    /**
     * 根据文件标识删除分片
     *
     * @param fileIdentifier 文件标识
     * @return 结果
     */
    public int deleteSysFileChunkByFileIdentifier(String fileIdentifier);
}
