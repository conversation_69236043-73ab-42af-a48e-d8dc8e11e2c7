package com.blog.system.mapper;

import java.util.List;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.system.domain.dto.AdminUserQueryDTO;
import com.blog.system.domain.vo.AdminUserVO;
import org.apache.ibatis.annotations.Param;

/**
 * 用户管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumUserExtendMapper
{
    /**
     * 查询用户管理
     *
     * @param userId 用户管理主键
     * @return 用户管理
     */
    public ForumUserExtend selectForumUserExtendByUserId(Long userId);

    /**
     * 查询用户管理列表
     *
     * @param forumUserExtend 用户管理
     * @return 用户管理集合
     */
    public List<ForumUserExtend> selectForumUserExtendList(ForumUserExtend forumUserExtend);

    /**
     * 新增用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    public int insertForumUserExtend(ForumUserExtend forumUserExtend);

    /**
     * 修改用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    public int updateForumUserExtend(ForumUserExtend forumUserExtend);

    /**
     * 删除用户管理
     *
     * @param userId 用户管理主键
     * @return 结果
     */
    public int deleteForumUserExtendByUserId(Long userId);

    /**
     * 批量删除用户管理
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumUserExtendByUserIds(Long[] userIds);


    ForumUserExtend selectForumUserExtendByEmail(String email);

    int checkEmailUnique(String email);

    int checkNicknameUnique(String nickname);

    /**
     * 检查用户是否被禁止发帖
     */
    boolean checkUserBannedPost(@Param("userId") Long userId);

    /**
     * 根据系统用户ID查询论坛用户ID
     */
    Long selectUserIdBySysUserId(@Param("sysUserId") Long sysUserId);

    /**
     * 增加用户发帖数
     */
    int incrementPostCount(@Param("userId") Long userId);

    /**
     * 增加用户分享数
     */
    int incrementShareCount(@Param("userId") Long userId);

    /**
     * 更新用户信息
     */
    int updateUserInfo(
            @Param("userId") Long userId,
            @Param("nickname") String nickname,
            @Param("avatar") String avatar,
            @Param("bio") String bio);

    /**
     * 查询后台用户列表
     *
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    List<AdminUserVO> selectAdminUserList(AdminUserQueryDTO queryDTO);

    /**
     * 更新用户禁止发帖状态
     *
     * @param userId 用户ID
     * @param isBannedPost 是否禁止发帖（0否 1是）
     * @return 影响行数
     */
    int updateUserBannedPost(@Param("userId") Long userId, @Param("isBannedPost") String isBannedPost);

//    /**
//     * 查询后台用户列表
//     *
//     * @param queryDTO 查询条件
//     * @return 用户列表
//     */
//    List<AdminUserVO> selectAdminUserList(AdminUserQueryDTO queryDTO);
//
//    /**
//     * 更新用户禁止发帖状态
//     *
//     * @param userId 用户ID
//     * @param isBannedPost 是否禁止发帖（0否 1是）
//     * @return 影响行数
//     */
//    int updateUserBannedPost(@Param("userId") Long userId, @Param("isBannedPost") String isBannedPost);
}
