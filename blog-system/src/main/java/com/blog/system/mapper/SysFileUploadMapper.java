package com.blog.system.mapper;

import com.blog.system.domain.SysFileUpload;

import java.util.List;

/**
 * 文件上传记录Mapper接口
 *
 * <AUTHOR>
 */
public interface SysFileUploadMapper {
    /**
     * 查询文件上传记录
     *
     * @param uploadId 上传ID
     * @return 文件上传记录
     */
    public SysFileUpload selectSysFileUploadById(Long uploadId);

    /**
     * 查询文件上传记录列表
     *
     * @param sysFileUpload 文件上传记录
     * @return 文件上传记录集合
     */
    public List<SysFileUpload> selectSysFileUploadList(SysFileUpload sysFileUpload);

    /**
     * 新增文件上传记录
     *
     * @param sysFileUpload 文件上传记录
     * @return 结果
     */
    public int insertSysFileUpload(SysFileUpload sysFileUpload);

    /**
     * 修改文件上传记录
     *
     * @param sysFileUpload 文件上传记录
     * @return 结果
     */
    public int updateSysFileUpload(SysFileUpload sysFileUpload);

    /**
     * 删除文件上传记录
     *
     * @param uploadId 上传ID
     * @return 结果
     */
    public int deleteSysFileUploadById(Long uploadId);

    /**
     * 批量删除文件上传记录
     *
     * @param uploadIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysFileUploadByIds(Long[] uploadIds);

    /**
     * 根据文件标识查询上传记录
     *
     * @param fileIdentifier 文件标识
     * @return 文件上传记录
     */
    public SysFileUpload selectSysFileUploadByFileIdentifier(String fileIdentifier);
}
