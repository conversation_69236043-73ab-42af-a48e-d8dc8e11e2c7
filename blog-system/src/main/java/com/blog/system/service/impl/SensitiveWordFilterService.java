package com.blog.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 敏感词过滤服务
 * 
 * <AUTHOR>
 */
@Service
public class SensitiveWordFilterService
{
    @Autowired
    private SensitiveWordCacheService sensitiveWordCacheService;
    
    /**
     * 敏感词Map
     */
    private Map<String, Object> sensitiveWordMap = null;
    
    /**
     * 敏感词替换符
     */
    private static final String REPLACEMENT = "*";
    
    /**
     * 初始化敏感词库
     */
    public void initSensitiveWordMap()
    {
        // 从缓存中获取敏感词
        List<String> sensitiveWords = sensitiveWordCacheService.getSensitiveWords();
        
        // 初始化敏感词库
        Set<String> wordSet = new HashSet<>(sensitiveWords);
        
        // 将敏感词库加入到HashMap中
        sensitiveWordMap = new HashMap<>(wordSet.size());
        Map<String, Object> nowMap = null;
        Map<String, Object> newWorMap = null;
        
        // 迭代sensitiveWordList
        for (String word : wordSet)
        {
            nowMap = sensitiveWordMap;
            for (int i = 0; i < word.length(); i++)
            {
                // 转换成char型
                char keyChar = word.charAt(i);
                
                // 获取
                Object tempMap = nowMap.get(String.valueOf(keyChar));
                
                // 如果存在该key，直接赋值
                if (tempMap != null)
                {
                    nowMap = (Map<String, Object>) tempMap;
                }
                // 不存在则，则构建一个map，同时将isEnd设置为0，因为他不是最后一个
                else
                {
                    newWorMap = new HashMap<>(8);
                    // 不是最后一个
                    newWorMap.put("isEnd", "0");
                    nowMap.put(String.valueOf(keyChar), newWorMap);
                    nowMap = newWorMap;
                }
                
                if (i == word.length() - 1)
                {
                    // 最后一个
                    nowMap.put("isEnd", "1");
                }
            }
        }
    }
    
    /**
     * 过滤文本中的敏感词
     *
     * @param text 需要过滤的文本
     * @return 过滤后的文本
     */
    public String filter(String text)
    {
        if (sensitiveWordMap == null)
        {
            initSensitiveWordMap();
        }

        // 无敏感词或文本为空，直接返回
        if (sensitiveWordMap.isEmpty() || text == null || text.trim().length() == 0)
        {
            return text;
        }

        // 检查是否为富文本内容
        if (isHtmlContent(text))
        {
            return filterHtmlContent(text);
        }

        String resultTxt = text;
        // 获取所有的敏感词
        List<String> sensitiveWordList = getSensitiveWord(text);

        // 替换敏感词
        for (String sensitiveWord : sensitiveWordList)
        {
            String replaceString = getReplacement(sensitiveWord.length());
            resultTxt = resultTxt.replaceAll(sensitiveWord, replaceString);
        }

        return resultTxt;
    }

    /**
     * 判断是否为HTML内容
     *
     * @param text 文本
     * @return 是否为HTML内容
     */
    private boolean isHtmlContent(String text)
    {
        // 简单判断是否包含HTML标签
        return text.contains("<") && text.contains(">");
    }

    /**
     * 过滤HTML内容中的敏感词，保护HTML标签不被过滤
     *
     * @param htmlContent HTML内容
     * @return 过滤后的HTML内容
     */
    private String filterHtmlContent(String htmlContent)
    {
        StringBuilder result = new StringBuilder();
        int i = 0;

        while (i < htmlContent.length())
        {
            if (htmlContent.charAt(i) == '<')
            {
                // 找到HTML标签的结束位置
                int tagEnd = htmlContent.indexOf('>', i);
                if (tagEnd != -1)
                {
                    // 保留整个HTML标签
                    result.append(htmlContent.substring(i, tagEnd + 1));
                    i = tagEnd + 1;
                }
                else
                {
                    // 没有找到标签结束，当作普通文本处理
                    result.append(filterPlainText(String.valueOf(htmlContent.charAt(i))));
                    i++;
                }
            }
            else
            {
                // 提取标签外的文本内容
                int nextTag = htmlContent.indexOf('<', i);
                if (nextTag == -1)
                {
                    // 没有更多标签，处理剩余文本
                    String remainingText = htmlContent.substring(i);
                    result.append(filterPlainText(remainingText));
                    break;
                }
                else
                {
                    // 处理标签前的文本
                    String textContent = htmlContent.substring(i, nextTag);
                    result.append(filterPlainText(textContent));
                    i = nextTag;
                }
            }
        }

        return result.toString();
    }

    /**
     * 过滤纯文本中的敏感词
     *
     * @param text 纯文本
     * @return 过滤后的文本
     */
    private String filterPlainText(String text)
    {
        if (text == null || text.trim().isEmpty())
        {
            return text;
        }

        String resultTxt = text;
        // 获取所有的敏感词
        List<String> sensitiveWordList = getSensitiveWord(text);

        // 替换敏感词
        for (String sensitiveWord : sensitiveWordList)
        {
            String replaceString = getReplacement(sensitiveWord.length());
            resultTxt = resultTxt.replaceAll(sensitiveWord, replaceString);
        }

        return resultTxt;
    }
    
    /**
     * 获取文本中的敏感词
     * 
     * @param text 文本
     * @return 敏感词列表
     */
    private List<String> getSensitiveWord(String text)
    {
        List<String> sensitiveWordList = new ArrayList<>();
        
        for (int i = 0; i < text.length(); i++)
        {
            // 判断是否包含敏感字符
            int length = checkSensitiveWord(text, i);
            
            // 存在，加入list中
            if (length > 0)
            {
                sensitiveWordList.add(text.substring(i, i + length));
                // 减1的原因，是因为for会自增
                i = i + length - 1;
            }
        }
        
        return sensitiveWordList;
    }
    
    /**
     * 检查文本中是否包含敏感字符
     * 
     * @param text 文本
     * @param beginIndex 开始位置
     * @return 敏感词长度
     */
    private int checkSensitiveWord(String text, int beginIndex)
    {
        // 敏感词结束标识位：用于敏感词只有1位的情况
        boolean flag = false;
        
        // 匹配标识数默认为0
        int matchFlag = 0;
        
        char word;
        Map<String, Object> nowMap = sensitiveWordMap;
        
        for (int i = beginIndex; i < text.length(); i++)
        {
            word = text.charAt(i);
            
            // 获取指定key
            nowMap = (Map<String, Object>) nowMap.get(String.valueOf(word));
            
            // 存在，则判断是否为最后一个
            if (nowMap != null)
            {
                // 找到相应key，匹配标识+1
                matchFlag++;
                
                // 如果为最后一个匹配规则,结束循环，返回匹配标识数
                if ("1".equals(nowMap.get("isEnd")))
                {
                    // 结束标志位为true
                    flag = true;
                }
            }
            else
            {
                break;
            }
        }
        
        if (matchFlag < 1 || !flag)
        {
            // 长度必须大于等于1，为词
            matchFlag = 0;
        }
        
        return matchFlag;
    }
    
    /**
     * 获取替换字符串
     * 
     * @param length 敏感词长度
     * @return 替换字符串
     */
    private String getReplacement(int length)
    {
        StringBuilder replacement = new StringBuilder();
        for (int i = 0; i < length; i++)
        {
            replacement.append(REPLACEMENT);
        }
        return replacement.toString();
    }
}
