package com.blog.system.service;

import java.util.List;
import com.blog.system.domain.SensitiveWord;

/**
 * 敏感词Service接口
 * 
 * <AUTHOR>
 */
public interface ISensitiveWordService 
{
    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词ID
     * @return 敏感词
     */
    public SensitiveWord selectSensitiveWordById(Long wordId);

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词集合
     */
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord);

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int insertSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    public int updateSensitiveWord(SensitiveWord sensitiveWord);

    /**
     * 批量删除敏感词
     * 
     * @param wordIds 需要删除的敏感词ID
     * @return 结果
     */
    public int deleteSensitiveWordByIds(Long[] wordIds);

    /**
     * 删除敏感词信息
     * 
     * @param wordId 敏感词ID
     * @return 结果
     */
    public int deleteSensitiveWordById(Long wordId);
    
    /**
     * 过滤文本中的敏感词
     * 
     * @param text 需要过滤的文本
     * @return 过滤后的文本
     */
    public String filterSensitiveWord(String text);
    
    /**
     * 初始化敏感词库
     */
    public void initSensitiveWordCache();
    
    /**
     * 设置敏感词指定状态
     * 
     * @param wordId 敏感词ID
     * @param isSpecified 是否指定
     * @return 结果
     */
    public int updateSensitiveWordSpecified(Long wordId, Boolean isSpecified);
    
    /**
     * 批量设置指定敏感词
     * 
     * @param words 敏感词列表
     * @return 结果
     */
    public int batchSetSpecifiedWords(List<String> words);
    
    /**
     * 获取所有指定的敏感词
     * 
     * @return 敏感词列表
     */
    public List<SensitiveWord> getSpecifiedSensitiveWords();
    
    /**
     * 导入敏感词数据
     * 
     * @param wordList 敏感词数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importSensitiveWord(List<SensitiveWord> wordList, Boolean isUpdateSupport, String operName);
}
