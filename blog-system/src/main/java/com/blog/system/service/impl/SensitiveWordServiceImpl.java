package com.blog.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.common.core.text.Convert;
import com.blog.common.utils.DateUtils;
import com.blog.system.domain.SensitiveWord;
import com.blog.system.mapper.SensitiveWordMapper;
import com.blog.system.service.ISensitiveWordService;

/**
 * 敏感词Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SensitiveWordServiceImpl implements ISensitiveWordService 
{
    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;
    
    @Autowired
    private SensitiveWordCacheService sensitiveWordCacheService;
    
    @Autowired
    private SensitiveWordFilterService sensitiveWordFilterService;

    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词ID
     * @return 敏感词
     */
    @Override
    public SensitiveWord selectSensitiveWordById(Long wordId)
    {
        return sensitiveWordMapper.selectSensitiveWordById(wordId);
    }

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词
     */
    @Override
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord)
    {
        return sensitiveWordMapper.selectSensitiveWordList(sensitiveWord);
    }

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    public int insertSensitiveWord(SensitiveWord sensitiveWord)
    {
        sensitiveWord.setCreateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.insertSensitiveWord(sensitiveWord);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    public int updateSensitiveWord(SensitiveWord sensitiveWord)
    {
        sensitiveWord.setUpdateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.updateSensitiveWord(sensitiveWord);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 删除敏感词对象
     * 
     * @param wordIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordByIds(Long[] wordIds)
    {
        int result = sensitiveWordMapper.deleteSensitiveWordByIds(wordIds);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 删除敏感词信息
     * 
     * @param wordId 敏感词ID
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordById(Long wordId)
    {
        int result = sensitiveWordMapper.deleteSensitiveWordById(wordId);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }
    
    /**
     * 初始化敏感词库
     */
    @Override
    public void initSensitiveWordCache()
    {
        sensitiveWordCacheService.refreshCache();
        sensitiveWordFilterService.initSensitiveWordMap();
    }
    
    /**
     * 过滤文本中的敏感词
     * 
     * @param text 需要过滤的文本
     * @return 过滤后的文本
     */
    @Override
    public String filterSensitiveWord(String text)
    {
        return sensitiveWordFilterService.filter(text);
    }
    
    /**
     * 设置敏感词指定状态
     * 
     * @param wordId 敏感词ID
     * @param isSpecified 是否指定
     * @return 结果
     */
    @Override
    public int updateSensitiveWordSpecified(Long wordId, Boolean isSpecified)
    {
        int result = sensitiveWordMapper.updateSensitiveWordSpecified(wordId, isSpecified);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }
    
    /**
     * 批量设置指定敏感词
     * 
     * @param words 敏感词列表
     * @return 结果
     */
    @Override
    public int batchSetSpecifiedWords(List<String> words)
    {
        int result = 0;
        for (String word : words)
        {
            if (word == null || word.trim().isEmpty())
            {
                continue;
            }
            
            // 查询敏感词是否存在
            SensitiveWord sensitiveWord = sensitiveWordMapper.selectSensitiveWordByWord(word.trim());
            if (sensitiveWord != null)
            {
                // 如果存在，设置指定状态
                if (sensitiveWord.getIsSpecified() == null || !sensitiveWord.getIsSpecified())
                {
                    result += sensitiveWordMapper.updateSensitiveWordSpecified(sensitiveWord.getWordId(), true);
                }
            }
            else
            {
                // 如果不存在，新增敏感词
                sensitiveWord = new SensitiveWord();
                sensitiveWord.setWord(word.trim());
                sensitiveWord.setType("5"); // 默认为其他类型
                sensitiveWord.setStatus("0"); // 正常状态
                sensitiveWord.setIsSpecified(true); // 设置为指定
                sensitiveWord.setCreateTime(DateUtils.getNowDate());
                result += sensitiveWordMapper.insertSensitiveWord(sensitiveWord);
            }
        }
        
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        
        return result;
    }
    
    /**
     * 获取所有指定的敏感词
     * 
     * @return 敏感词列表
     */
    @Override
    public List<SensitiveWord> getSpecifiedSensitiveWords()
    {
        return sensitiveWordMapper.selectAllSpecifiedSensitiveWords();
    }
    
    /**
     * 导入敏感词数据
     * 
     * @param wordList 敏感词数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importSensitiveWord(List<SensitiveWord> wordList, Boolean isUpdateSupport, String operName)
    {
        if (wordList == null || wordList.isEmpty())
        {
            return "导入敏感词数据不能为空！";
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SensitiveWord sensitiveWord : wordList)
        {
            try
            {
                // 验证是否存在这个敏感词
                SensitiveWord existSensitiveWord = sensitiveWordMapper.selectSensitiveWordByWord(sensitiveWord.getWord());
                if (existSensitiveWord == null)
                {
                    sensitiveWord.setCreateBy(operName);
                    sensitiveWord.setCreateTime(DateUtils.getNowDate());
                    sensitiveWordMapper.insertSensitiveWord(sensitiveWord);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、敏感词 ").append(sensitiveWord.getWord()).append(" 导入成功");
                }
                else if (isUpdateSupport)
                {
                    sensitiveWord.setWordId(existSensitiveWord.getWordId());
                    sensitiveWord.setUpdateBy(operName);
                    sensitiveWord.setUpdateTime(DateUtils.getNowDate());
                    sensitiveWordMapper.updateSensitiveWord(sensitiveWord);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、敏感词 ").append(sensitiveWord.getWord()).append(" 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、敏感词 ").append(sensitiveWord.getWord()).append(" 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、敏感词 " + sensitiveWord.getWord() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        
        if (successNum > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        
        return "导入结果：共 " + wordList.size() + " 条数据，成功 " + successNum + " 条，失败 " + failureNum + " 条" + failureMsg.toString() + successMsg.toString();
    }
}
