package com.blog.system.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.blog.common.constant.CacheConstants;
import com.blog.common.core.redis.RedisCache;
import com.blog.system.domain.SensitiveWord;
import com.blog.system.mapper.SensitiveWordMapper;

/**
 * 敏感词缓存服务
 *
 * <AUTHOR>
 */
@Service
public class SensitiveWordCacheService
{
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    /**
     * 获取所有敏感词
     *
     * @return 敏感词列表
     */
    public List<String> getSensitiveWords()
    {
        // 从Redis缓存中获取敏感词
        Set<String> cachedWords = redisCache.getCacheSet(CacheConstants.SENSITIVE_WORDS_KEY);

        // 如果缓存中没有敏感词，则从数据库中获取并缓存
        if (cachedWords == null || cachedWords.isEmpty())
        {
            return refreshCache();
        }

        // 将缓存中的敏感词转换为List<String>
        List<String> sensitiveWords = new ArrayList<>(cachedWords);

        return sensitiveWords;
    }

    /**
     * 刷新敏感词缓存
     *
     * @return 敏感词列表
     */
    public List<String> refreshCache()
    {
        // 从数据库中获取所有有效的敏感词
        List<SensitiveWord> sensitiveWordList = sensitiveWordMapper.selectAllEnabledSensitiveWords();

        // 清除缓存
        redisCache.deleteObject(CacheConstants.SENSITIVE_WORDS_KEY);

        // 将敏感词添加到缓存中
        List<String> sensitiveWords = new ArrayList<>();
        Set<String> wordSet = new HashSet<>();
        for (SensitiveWord sensitiveWord : sensitiveWordList)
        {
            sensitiveWords.add(sensitiveWord.getWord());
            wordSet.add(sensitiveWord.getWord());
        }

        // 将敏感词添加到缓存中
        if (!wordSet.isEmpty())
        {
            redisCache.setCacheSet(CacheConstants.SENSITIVE_WORDS_KEY, wordSet);
            // 设置缓存过期时间（7天）
            redisCache.expire(CacheConstants.SENSITIVE_WORDS_KEY, 7 * 24 * 60 * 60);
        }

        return sensitiveWords;
    }
}
