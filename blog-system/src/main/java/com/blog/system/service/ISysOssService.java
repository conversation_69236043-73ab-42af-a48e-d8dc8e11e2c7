package com.blog.system.service;

import java.util.List;
import com.blog.system.domain.SysOss;

/**
 * OSS对象存储Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ISysOssService
{
    /**
     * 查询OSS对象存储
     *
     * @param ossId OSS对象存储主键
     * @return OSS对象存储
     */
    public SysOss selectSysOssByOssId(Long ossId);

    /**
     * 查询OSS对象存储列表
     *
     * @param sysOss OSS对象存储
     * @return OSS对象存储集合
     */
    public List<SysOss> selectSysOssList(SysOss sysOss);

    /**
     * 新增OSS对象存储
     *
     * @param sysOss OSS对象存储
     * @return 结果
     */
    public int insertSysOss(SysOss sysOss);

    /**
     * 修改OSS对象存储
     *
     * @param sysOss OSS对象存储
     * @return 结果
     */
    public int updateSysOss(SysOss sysOss);

    /**
     * 批量删除OSS对象存储
     *
     * @param ossIds 需要删除的OSS对象存储主键集合
     * @return 结果
     */
    public int deleteSysOssByOssIds(Long[] ossIds);

    /**
     * 删除OSS对象存储信息
     *
     * @param ossId OSS对象存储主键
     * @return 结果
     */
    public int deleteSysOssByOssId(Long ossId);
}
