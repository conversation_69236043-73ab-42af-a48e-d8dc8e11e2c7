package com.blog.system.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.system.mapper.SysOssMapper;
import com.blog.system.domain.SysOss;
import com.blog.system.service.ISysOssService;

/**
 * OSS对象存储Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class SysOssServiceImpl implements ISysOssService
{
    @Autowired
    private SysOssMapper sysOssMapper;

    /**
     * 查询OSS对象存储
     *
     * @param ossId OSS对象存储主键
     * @return OSS对象存储
     */
    @Override
    public SysOss selectSysOssByOssId(Long ossId)
    {
        return sysOssMapper.selectSysOssByOssId(ossId);
    }

    /**
     * 查询OSS对象存储列表
     *
     * @param sysOss OSS对象存储
     * @return OSS对象存储
     */
    @Override
    public List<SysOss> selectSysOssList(SysOss sysOss)
    {
        return sysOssMapper.selectSysOssList(sysOss);
    }

    /**
     * 新增OSS对象存储
     *
     * @param sysOss OSS对象存储
     * @return 结果
     */
    @Override
    public int insertSysOss(SysOss sysOss)
    {
        sysOss.setCreateTime(DateUtils.getNowDate());
        return sysOssMapper.insertSysOss(sysOss);
    }

    /**
     * 修改OSS对象存储
     *
     * @param sysOss OSS对象存储
     * @return 结果
     */
    @Override
    public int updateSysOss(SysOss sysOss)
    {
        sysOss.setUpdateTime(DateUtils.getNowDate());
        return sysOssMapper.updateSysOss(sysOss);
    }

    /**
     * 批量删除OSS对象存储
     *
     * @param ossIds 需要删除的OSS对象存储主键
     * @return 结果
     */
    @Override
    public int deleteSysOssByOssIds(Long[] ossIds)
    {
        return sysOssMapper.deleteSysOssByOssIds(ossIds);
    }

    /**
     * 删除OSS对象存储信息
     *
     * @param ossId OSS对象存储主键
     * @return 结果
     */
    @Override
    public int deleteSysOssByOssId(Long ossId)
    {
        return sysOssMapper.deleteSysOssByOssId(ossId);
    }
}
