package com.blog.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.common.core.text.Convert;
import com.blog.common.utils.DateUtils;
import com.blog.system.domain.SensitiveWord;
import com.blog.system.mapper.SensitiveWordMapper;
import com.blog.system.service.ISensitiveWordService;

/**
 * 敏感词Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SensitiveWordServiceImpl implements ISensitiveWordService 
{
    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;
    
    @Autowired
    private SensitiveWordFilterService sensitiveWordFilterService;

    /**
     * 查询敏感词
     * 
     * @param wordId 敏感词ID
     * @return 敏感词
     */
    @Override
    public SensitiveWord selectSensitiveWordById(Long wordId)
    {
        return sensitiveWordMapper.selectSensitiveWordById(wordId);
    }

    /**
     * 查询敏感词列表
     * 
     * @param sensitiveWord 敏感词
     * @return 敏感词
     */
    @Override
    public List<SensitiveWord> selectSensitiveWordList(SensitiveWord sensitiveWord)
    {
        return sensitiveWordMapper.selectSensitiveWordList(sensitiveWord);
    }

    /**
     * 新增敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    public int insertSensitiveWord(SensitiveWord sensitiveWord)
    {
        sensitiveWord.setCreateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.insertSensitiveWord(sensitiveWord);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 修改敏感词
     * 
     * @param sensitiveWord 敏感词
     * @return 结果
     */
    @Override
    public int updateSensitiveWord(SensitiveWord sensitiveWord)
    {
        sensitiveWord.setUpdateTime(DateUtils.getNowDate());
        int result = sensitiveWordMapper.updateSensitiveWord(sensitiveWord);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 删除敏感词对象
     * 
     * @param wordIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordByIds(Long[] wordIds)
    {
        int result = sensitiveWordMapper.deleteSensitiveWordByIds(wordIds);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }

    /**
     * 删除敏感词信息
     * 
     * @param wordId 敏感词ID
     * @return 结果
     */
    @Override
    public int deleteSensitiveWordById(Long wordId)
    {
        int result = sensitiveWordMapper.deleteSensitiveWordById(wordId);
        if (result > 0)
        {
            // 更新缓存
            initSensitiveWordCache();
        }
        return result;
    }
    
    /**
     * 初始化敏感词库
     */
    @Override
    public void initSensitiveWordCache()
    {
        sensitiveWordFilterService.initSensitiveWordMap();
    }
    
    /**
     * 过滤文本中的敏感词
     * 
     * @param text 需要过滤的文本
     * @return 过滤后的文本
     */
    @Override
    public String filterSensitiveWord(String text)
    {
        return sensitiveWordFilterService.filter(text);
    }
    
    /**
     * 设置敏感词指定状态
     * 
     * @param wordId 敏感词ID
     * @param isSpecified 是否指定
     * @return 结果
     */
    @Override
    public int updateSensitiveWordSpecified(Long wordId, Boolean isSpecified)
    {
        return sensitiveWordFilterService.updateSensitiveWordSpecified(wordId, isSpecified);
    }
    
    /**
     * 批量设置指定敏感词
     * 
     * @param words 敏感词列表
     * @return 结果
     */
    @Override
    public int batchSetSpecifiedWords(List<String> words)
    {
        return sensitiveWordFilterService.batchSetSpecifiedWords(words);
    }
    
    /**
     * 获取所有指定的敏感词
     * 
     * @return 敏感词列表
     */
    @Override
    public List<SensitiveWord> getSpecifiedSensitiveWords()
    {
        return sensitiveWordFilterService.getSpecifiedSensitiveWords();
    }
}
