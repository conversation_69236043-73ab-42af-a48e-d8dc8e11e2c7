package com.blog.forum.util;

import com.blog.common.core.redis.RedisCache;
import com.blog.common.utils.ServletUtils;
import com.blog.common.utils.StringUtils;
import com.blog.common.utils.spring.SpringUtils;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.mapper.ForumLanguageMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class LangUtils {
    private static final Logger log = LoggerFactory.getLogger(LangUtils.class);
    private static final String LANG_HEADER = "i18n";
    private static final String DEFAULT_LANG = "en-US";

    // Redis缓存相关常量
    private static final String LANG_CACHE_KEY = "forum:language";
    private static final String LANG_CODE_MAP_KEY = "forum:language:code_map";
    private static final int LANG_CACHE_TIMEOUT = 24 * 60 * 60; // 24小时

    /**
     * 获取当前请求的语言
     */
    public static String getCurrentLang() {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            if (request == null) {
                return DEFAULT_LANG;
            }

            String lang = request.getHeader(LANG_HEADER);
            if (StringUtils.isEmpty(lang)) {
                return DEFAULT_LANG;
            }
            return lang;
        } catch (Exception e) {
            log.error("获取语言标识失败", e);
            return DEFAULT_LANG;
        }
    }

    /**
     * 获取当前语言对应的语言ID
     */
    public static Long getCurrentLangId() {
        String langCode = getCurrentLang();

        // 从Redis缓存中获取语言ID
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Map<String, Long> langCodeMap = redisCache.getCacheMap(LANG_CODE_MAP_KEY);

        // 如果Redis中没有缓存，则初始化缓存
        if (langCodeMap == null || langCodeMap.isEmpty()) {
            langCodeMap = initLanguageCache();
        }

        // 从缓存中获取语言ID
        Long langId = langCodeMap.get(langCode);
        if (langId != null) {
            return langId;
        }

        // 如果缓存中没有找到，则从数据库中查询
        ForumLanguageMapper forumLanguageMapper = SpringUtils.getBean(ForumLanguageMapper.class);
        ForumLanguage language = forumLanguageMapper.selectByLangCode(langCode);

        // 如果数据库中找到了，则更新缓存
        if (language != null) {
            langId = language.getLangId();
            langCodeMap.put(langCode, langId);
            redisCache.setCacheMap(LANG_CODE_MAP_KEY, langCodeMap);
            redisCache.expire(LANG_CODE_MAP_KEY, LANG_CACHE_TIMEOUT, TimeUnit.SECONDS);
            return langId;
        }

        return 1L; // 默认语言ID
    }

    /**
     * 初始化语言缓存
     * @return 语言代码到语言ID的映射
     */
    private static Map<String, Long> initLanguageCache() {
        log.info("初始化语言缓存");
        Map<String, Long> langCodeMap = new HashMap<>();

        try {
            // 获取所有启用的语言
            ForumLanguageMapper forumLanguageMapper = SpringUtils.getBean(ForumLanguageMapper.class);
            List<ForumLanguage> languages = forumLanguageMapper.selectEnabledLanguageList();

            // 构建语言代码到语言ID的映射
            for (ForumLanguage language : languages) {
                langCodeMap.put(language.getLangCode(), language.getLangId());
            }

            // 缓存到Redis
            RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
            redisCache.setCacheMap(LANG_CODE_MAP_KEY, langCodeMap);
            redisCache.expire(LANG_CODE_MAP_KEY, LANG_CACHE_TIMEOUT, TimeUnit.SECONDS);

            // 同时缓存语言列表
            redisCache.setCacheObject(LANG_CACHE_KEY, languages);
            redisCache.expire(LANG_CACHE_KEY, LANG_CACHE_TIMEOUT, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("初始化语言缓存失败", e);
        }

        return langCodeMap;
    }

    /**
     * 刷新语言缓存
     */
    public static void refreshLanguageCache() {
        log.info("刷新语言缓存");
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        redisCache.deleteObject(LANG_CACHE_KEY);
        redisCache.deleteObject(LANG_CODE_MAP_KEY);
        initLanguageCache();
    }

    /**
     * 获取所有语言列表
     * @return 语言列表
     */
    public static List<ForumLanguage> getAllLanguages() {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        List<ForumLanguage> languages = redisCache.getCacheObject(LANG_CACHE_KEY);

        // 如果Redis中没有缓存，则初始化缓存
        if (languages == null || languages.isEmpty()) {
            initLanguageCache();
            languages = redisCache.getCacheObject(LANG_CACHE_KEY);
        }

        return languages;
    }
}
