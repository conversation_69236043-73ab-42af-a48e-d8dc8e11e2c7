package com.blog.forum.service.impl;

import java.util.List;

import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.dto.CollectCheckDTO;
import com.blog.forum.domain.vo.CollectCheckVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumCollectMapper;
import com.blog.forum.domain.ForumCollect;
import com.blog.forum.service.IForumCollectService;

/**
 * 收藏Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumCollectServiceImpl implements IForumCollectService
{
    @Autowired
    private ForumCollectMapper forumCollectMapper;

    /**
     * 查询收藏
     *
     * @param collectId 收藏主键
     * @return 收藏
     */
    @Override
    public ForumCollect selectForumCollectByCollectId(Long collectId)
    {
        return forumCollectMapper.selectForumCollectByCollectId(collectId);
    }

    /**
     * 查询收藏列表
     *
     * @param forumCollect 收藏
     * @return 收藏
     */
    @Override
    public List<ForumCollect> selectForumCollectList(ForumCollect forumCollect)
    {
        return forumCollectMapper.selectForumCollectList(forumCollect);
    }

    /**
     * 新增收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    @Override
    public int insertForumCollect(ForumCollect forumCollect)
    {
        forumCollect.setCreateTime(DateUtils.getNowDate());
        return forumCollectMapper.insertForumCollect(forumCollect);
    }

    /**
     * 修改收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    @Override
    public int updateForumCollect(ForumCollect forumCollect)
    {
        return forumCollectMapper.updateForumCollect(forumCollect);
    }

    /**
     * 批量删除收藏
     *
     * @param collectIds 需要删除的收藏主键
     * @return 结果
     */
    @Override
    public int deleteForumCollectByCollectIds(Long[] collectIds)
    {
        return forumCollectMapper.deleteForumCollectByCollectIds(collectIds);
    }

    /**
     * 删除收藏信息
     *
     * @param collectId 收藏主键
     * @return 结果
     */
    @Override
    public int deleteForumCollectByCollectId(Long collectId)
    {
        return forumCollectMapper.deleteForumCollectByCollectId(collectId);
    }

    @Override
    public CollectCheckVO checkIfCollected(CollectCheckDTO checkDTO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }
        return checkIfCollected(checkDTO, userId);
    }
    @Override
    public CollectCheckVO checkIfCollected(CollectCheckDTO checkDTO, Long userId) {
        ForumCollect collect = forumCollectMapper.selectCollectByUserAndContent(
                checkDTO.getContentType(),
                checkDTO.getContentId(),
                userId);

        CollectCheckVO result = new CollectCheckVO();
        result.setIsCollected(collect != null);
        if (collect != null) {
            result.setCollectId(collect.getCollectId());
        }
        return result;
    }

    // 提供一个高性能的简单检查方法
    public boolean isCollected(String contentType, Long contentId, Long userId) {
        return forumCollectMapper.checkCollectExists(contentType, contentId, userId);
    }
}
