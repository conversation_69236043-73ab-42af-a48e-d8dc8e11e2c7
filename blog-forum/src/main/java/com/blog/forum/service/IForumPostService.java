package com.blog.forum.service;

import java.util.List;

import com.blog.forum.domain.ForumPost;
import com.blog.forum.domain.dto.PostListQueryDTO;
import com.blog.forum.domain.dto.PostQueryDTO;
import com.blog.forum.domain.vo.CarouselVO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.PostVO;

/**
 * 帖子Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IForumPostService
{
    /**
     * 查询帖子
     *
     * @param postId 帖子主键
     * @return 帖子
     */
    public ForumPost selectForumPostByPostId(Long postId);

    /**
     * 查询帖子列表
     *
     * @param forumPost 帖子
     * @return 帖子集合
     */
    public List<ForumPost> selectForumPostList(ForumPost forumPost);

    /**
     * 新增帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    public int insertForumPost(ForumPost forumPost);

    /**
     * 修改帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    public int updateForumPost(ForumPost forumPost);

    /**
     * 批量删除帖子
     *
     * @param postIds 需要删除的帖子主键集合
     * @return 结果
     */
    public int deleteForumPostByPostIds(Long[] postIds);

    List<PostVO> selectPostList(PostQueryDTO query);

    PostVO selectPostById(Long postId);

    List<CategoryVO> selectCategoryList();

    List<CarouselVO> selectCarouselList();

    /**
     * 查询帖子列表（高级查询）
     *
     * @param query 查询条件
     * @return 帖子列表
     */
    List<PostDetailVO> selectPostListAdvanced(PostListQueryDTO query);

    /**
     * 设置帖子置顶状态
     *
     * @param postId 帖子ID
     * @param isTop 是否置顶（0否 1是）
     * @return 结果
     */
    public int updatePostTopStatus(Long postId, String isTop);
}
