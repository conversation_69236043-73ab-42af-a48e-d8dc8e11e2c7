package com.blog.forum.service;

import java.util.List;

import com.blog.common.core.domain.AjaxResult;
import com.blog.forum.domain.ForumComment;
import com.blog.forum.domain.dto.AdminCommentQueryDTO;
import com.blog.forum.domain.dto.CommentDTO;
import com.blog.forum.domain.vo.CommentDetailVO;

/**
 * 评论Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumCommentService
{
    /**
     * 查询评论
     *
     * @param commentId 评论主键
     * @return 评论
     */
    public ForumComment selectForumCommentByCommentId(Long commentId);

    /**
     * 查询评论列表
     *
     * @param forumComment 评论
     * @return 评论集合
     */
    public List<ForumComment> selectForumCommentList(ForumComment forumComment);

    /**
     * 新增评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    public int insertForumComment(ForumComment forumComment);

    /**
     * 修改评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    public int updateForumComment(ForumComment forumComment);

    /**
     * 批量删除评论
     *
     * @param commentIds 需要删除的评论主键集合
     * @return 结果
     */
    public int deleteForumCommentByCommentIds(Long[] commentIds);

    /**
     * 删除评论信息
     *
     * @param commentId 评论主键
     * @return 结果
     */
    public int deleteForumCommentByCommentId(Long commentId);

    /**
     * 获取评论列表
     */
    List<CommentDetailVO> getCommentList(String contentType, Long contentId, Long userId);

    /**
     * 发表评论
     */
    AjaxResult addComment(CommentDTO commentDTO, Long userId);

    /**
     * 删除评论
     */
    AjaxResult deleteComment(Long commentId, Long userId);

    /**
     * 点赞/取消点赞评论
     */
    AjaxResult likeComment(Long commentId, Long userId, boolean isLike);

    /**
     * 查询后台评论列表
     *
     * @param queryDTO 查询条件
     * @return 评论列表
     */
    List<CommentDetailVO> selectAdminCommentList(AdminCommentQueryDTO queryDTO);

    /**
     * 查询评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    List<CommentDetailVO> selectCommentReplies(Long parentId);
}
