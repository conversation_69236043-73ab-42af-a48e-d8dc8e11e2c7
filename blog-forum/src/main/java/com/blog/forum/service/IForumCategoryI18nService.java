package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumCategoryI18n;

/**
 * 分类多语言内容Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IForumCategoryI18nService 
{
    /**
     * 查询分类多语言内容
     * 
     * @param i18nId 分类多语言内容主键
     * @return 分类多语言内容
     */
    public ForumCategoryI18n selectForumCategoryI18nByI18nId(Long i18nId);

    /**
     * 查询分类多语言内容列表
     * 
     * @param forumCategoryI18n 分类多语言内容
     * @return 分类多语言内容集合
     */
    public List<ForumCategoryI18n> selectForumCategoryI18nList(ForumCategoryI18n forumCategoryI18n);

    /**
     * 新增分类多语言内容
     * 
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    public int insertForumCategoryI18n(ForumCategoryI18n forumCategoryI18n);

    /**
     * 修改分类多语言内容
     * 
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    public int updateForumCategoryI18n(ForumCategoryI18n forumCategoryI18n);

    /**
     * 批量删除分类多语言内容
     * 
     * @param i18nIds 需要删除的分类多语言内容主键集合
     * @return 结果
     */
    public int deleteForumCategoryI18nByI18nIds(Long[] i18nIds);

    /**
     * 删除分类多语言内容信息
     * 
     * @param i18nId 分类多语言内容主键
     * @return 结果
     */
    public int deleteForumCategoryI18nByI18nId(Long i18nId);
}
