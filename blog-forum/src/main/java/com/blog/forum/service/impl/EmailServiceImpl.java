//package com.blog.forum.service.impl;
//
//import cn.hutool.core.util.RandomUtil;
//import com.blog.common.core.redis.RedisCache;
//import com.blog.common.exception.ServiceException;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.mail.MailException;
//import org.springframework.mail.SimpleMailMessage;
//import org.springframework.mail.javamail.JavaMailSender;
//import org.springframework.mail.javamail.MimeMessageHelper;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.mail.MessagingException;
//import javax.mail.internet.MimeMessage;
//import com.blog.forum.service.EmailService;
//import java.util.concurrent.TimeUnit;
//
//@Service
//public class EmailServiceImpl implements EmailService {
//
//    private static final Logger log = LoggerFactory.getLogger(EmailServiceImpl.class);
//
//    @Resource
//    private JavaMailSender mailSender;
//
//    @Value("${spring.mail.from}")
//    private String from;
//
//    @Resource
//    private RedisCache redisCache;
//
//    // ???????��??????5????
//    private static final Integer CODE_EXPIRE = 5 * 60;
//
//    // ????????
//    private static final String REGISTER_TEMPLATE  = "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;\"> " +
//                "<h2 style=\"color: #333;\">?????</h2>" +
//                "<p>?????????????</p>" +
//                "<p>????????????<strong style=\"font-size: 18px; color: #1890ff;\">%s</strong></p>"+
//                "<p>???????5??????��????????��?</p>"+
//                "<p>??????????????????????????????</p>"+
//                "<hr style=\"border: none; border-top: 1px solid #eee; margin: 20px 0;\">"+
//                "<p style=\"color: #999; font-size: 12px;\">?????????????????????</p>"
//            +"</div>"            ;
//
//    // ???????????????
//    private static final String RESET_PASSWORD_TEMPLATE = " <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;\">"+
//                "<h2 style=\"color: #333;\">?????????????</h2>"+
//                "<p>?????????????</p>"+
//                "<p>??????????????????????<strong style=\"font-size: 18px; color: #1890ff;\">%s</strong></p>"+
//                "<p>???????5??????��????????��?</p>"+
//                "<p>?????????????????????????????????????????????</p>"+
//                "<hr style=\"border: none; border-top: 1px solid #eee; margin: 20px 0;\">"+
//                "<p style=\"color: #999; font-size: 12px;\">?????????????????????</p>"+
//            "</div>";
//
//    /**
//     * ?????????
//     * @param email ???????
//     * @param type ?????????(register/reset)
//     */
//    public void sendCode(String email, String type) {
//        // 1. ????6��????????
//        String code = RandomUtil.randomNumbers(6);
//
//        // 2. ?????????????��?????
//        String subject = "";
//        String content = "";
//
//        if ("register".equals(type)) {
//            subject = "????????????";
//            content = String.format(REGISTER_TEMPLATE, code);
//        } else if ("reset".equals(type)) {
//            subject = "?????????????????";
//            content = String.format(RESET_PASSWORD_TEMPLATE, code);
//        } else {
//            throw new ServiceException("???????????????");
//        }
//
//        try {
//            this.sendHtmlMail(email, subject, content);
//        } catch (Exception e) {
//            log.error("???????????????", e);
//            throw new ServiceException("????????????????????");
//        }
//
//        // 3. ????Redis??key?????email:code:{type}:{email}
//        String key = String.format("email:code:%s:%s", type, email);
//        redisCache.setCacheObject(key, code, CODE_EXPIRE, TimeUnit.SECONDS);
//
//        log.info("{}????????????????: {}, ?????: {}",
//                "register".equals(type) ? "???" : "????????", email, code);
//    }
//
//    /**
//     * ????????
//     */
//    public boolean verifyCode(String email, String code, String type) {
//        String key = String.format("email:code:%s:%s", type, email);
//        String correctCode = redisCache.getCacheObject(key);
//        return code.equals(correctCode);
//    }
//
//    /**
//     * ????????
//     */
//    public void sendSimpleMail(String to, String subject, String content) {
//        SimpleMailMessage message = new SimpleMailMessage();
//        message.setFrom(from);
//        message.setTo(to);
//        message.setSubject(subject);
//        message.setText(content);
//        try {
//            mailSender.send(message);
//            log.info("????????????{}", to);
//        } catch (MailException e) {
//            log.error("???????????????", e);
//            throw new ServiceException("??????????: " + e.getMessage());
//        }
//    }
//    /**
//     * ????HTML??????
//     */
//    public void sendHtmlMail(String to, String subject, String content) {
//        MimeMessage message = mailSender.createMimeMessage();
//        try {
//
//            // true?????????????multipart message
//            MimeMessageHelper helper = new MimeMessageHelper(message, true);
//            helper.setFrom(from);
//            helper.setTo(to);
//            helper.setSubject(subject);
//            helper.setText(content, true);
//            mailSender.send(message);
//            log.info("HTML??????????{}", to);
//        } catch (MessagingException e) {
//            log.error("????HTML??????????", e);
//            throw new ServiceException("??????????: " + e.getMessage());
//        }
//    }
//}
