package com.blog.forum.service;


import com.blog.common.core.domain.AjaxResult;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.domain.dto.UserPostDTO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.UserPostDetailVO;

import java.util.List;

public interface PostService {
    List<PostDetailVO> getPostList(PostListDTO postListDTO);

    PostDetailVO getPostDetail(Long postId);

    List<CategoryVO> getCategoryList();

    List<UserPostDetailVO> getUserPostList(PostListDTO postListDTO, Long userId);

    UserPostDetailVO getUserPostDetail(Long postId);

    AjaxResult submitUserPost(UserPostDTO userPostDTO);

}
