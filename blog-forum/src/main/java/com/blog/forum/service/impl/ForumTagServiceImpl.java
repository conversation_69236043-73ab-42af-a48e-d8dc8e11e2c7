package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumTagMapper;
import com.blog.forum.domain.ForumTag;
import com.blog.forum.service.IForumTagService;

/**
 * 帖子标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class ForumTagServiceImpl implements IForumTagService
{
    @Autowired
    private ForumTagMapper forumTagMapper;

    /**
     * 查询帖子标签
     *
     * @param tagId 帖子标签主键
     * @return 帖子标签
     */
    @Override
    public ForumTag selectForumTagByTagId(Long tagId)
    {
        return forumTagMapper.selectForumTagByTagId(tagId);
    }

    /**
     * 查询帖子标签列表
     *
     * @param forumTag 帖子标签
     * @return 帖子标签
     */
    @Override
    public List<ForumTag> selectForumTagList(ForumTag forumTag)
    {
        forumTag.setStatus("0");
        return forumTagMapper.selectForumTagList(forumTag);
    }

    /**
     * 新增帖子标签
     *
     * @param forumTag 帖子标签
     * @return 结果
     */
    @Override
    public int insertForumTag(ForumTag forumTag)
    {
        forumTag.setCreateTime(DateUtils.getNowDate());
        return forumTagMapper.insertForumTag(forumTag);
    }

    /**
     * 修改帖子标签
     *
     * @param forumTag 帖子标签
     * @return 结果
     */
    @Override
    public int updateForumTag(ForumTag forumTag)
    {
        forumTag.setUpdateTime(DateUtils.getNowDate());
        return forumTagMapper.updateForumTag(forumTag);
    }

    /**
     * 批量删除帖子标签
     *
     * @param tagIds 需要删除的帖子标签主键
     * @return 结果
     */
    @Override
    public int deleteForumTagByTagIds(Long[] tagIds)
    {
        int result = 0;
        for (Long tagId : tagIds) {
            ForumTag tag = forumTagMapper.selectForumTagByTagId(tagId);
            if (tag != null) {
                // 逻辑删除，将status设置为1（下线）
                tag.setStatus("1");
                tag.setUpdateTime(DateUtils.getNowDate());
                result += forumTagMapper.updateForumTag(tag);
            }
        }
        return result;
    }

    /**
     * 删除帖子标签信息
     *
     * @param tagId 帖子标签主键
     * @return 结果
     */
    @Override
    public int deleteForumTagByTagId(Long tagId)
    {
        return forumTagMapper.deleteForumTagByTagId(tagId);
    }
}
