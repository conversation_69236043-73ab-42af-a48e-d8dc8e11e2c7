package com.blog.forum.service;

import com.blog.forum.domain.dto.ChunkUploadDTO;
import com.blog.forum.domain.dto.ChunkUploadResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface FileService {
    /**
     * 文件上传
     * @param file 文件对象
     * @param bizType 业务类型
     * @return 文件访问URL
     */
    String upload(MultipartFile file, String bizType) throws IOException;

    /**
     * 大文件分片上传
     * @param file 文件对象
     * @param bizType 业务类型
     * @param chunkSize 分片大小（字节）
     * @return 文件访问URL
     */
    String uploadLargeFile(MultipartFile file, String bizType, int chunkSize) throws IOException;

    /**
     * 批量上传文件
     * @param files 文件列表
     * @param bizType 业务类型
     * @return 文件访问URL列表
     */
    List<String> uploadBatch(MultipartFile[] files, String bizType);

    /**
     * 根据URL删除文件
     * @param url 文件访问URL
     */
    boolean deleteByUrl(String url);

    /**
     * 根据业务类型和业务ID删除文件
     * @param bizType 业务类型
     * @param bizId 业务ID
     */
    boolean deleteByBiz(String bizType, Long bizId);

    /**
     * 更新业务ID
     * @param urls 文件URL列表
     * @param bizType 业务类型
     * @param bizId 业务ID
     */
    void updateBizInfo(List<String> urls, String bizType, Long bizId);

    /**
     * 根据URL获取文件流
     * @param url 文件URL
     * @return 文件流
     */
    InputStream getStreamByUrl(String url);

    /**
     * 获取文件访问URL前缀
     */
    String getEndpoint();

    /**
     * 检查分片是否存在
     *
     * @param identifier 文件标识（MD5）
     * @param chunkNumber 分片编号
     * @param bizType 业务类型
     * @return 检查结果
     */
    ChunkUploadResponseDTO checkChunk(String identifier, Integer chunkNumber, String bizType) throws IOException;

    /**
     * 上传分片
     *
     * @param file 分片文件
     * @param chunkUploadDTO 分片上传数据
     * @return 上传结果
     */
    ChunkUploadResponseDTO uploadChunk(MultipartFile file, ChunkUploadDTO chunkUploadDTO) throws IOException;

    /**
     * 合并分片
     *
     * @param identifier 文件标识（MD5）
     * @param filename 文件名
     * @param totalChunks 总分片数
     * @param totalSize 文件总大小
     * @param bizType 业务类型
     * @return 文件访问URL
     */
    String mergeChunks(String identifier, String filename, Integer totalChunks, Long totalSize, String bizType) throws IOException;
}
