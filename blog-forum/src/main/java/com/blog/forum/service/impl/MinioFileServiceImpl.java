package com.blog.forum.service.impl;

import com.blog.common.constant.CacheConstants;
import com.blog.common.core.redis.RedisCache;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.common.utils.file.FileTypeUtils;
import com.blog.common.utils.uuid.IdUtils;
import com.blog.forum.domain.dto.ChunkUploadDTO;
import com.blog.forum.domain.dto.ChunkUploadResponseDTO;
import com.blog.system.domain.SysFileChunk;
import com.blog.system.domain.SysFileUpload;
import com.blog.system.mapper.SysFileChunkMapper;
import com.blog.system.mapper.SysFileUploadMapper;
import io.minio.*;
import io.minio.errors.*;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.blog.forum.service.FileService;
import com.blog.forum.config.properties.MinioConfig;

import com.blog.system.mapper.SysOssMapper;
import com.blog.system.domain.SysOss;
import io.minio.messages.Item;

import java.util.ArrayList;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MinioFileServiceImpl implements FileService {
    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private SysOssMapper sysOssMapper;

    @Autowired
    private SysFileChunkMapper sysFileChunkMapper;

    @Autowired
    private SysFileUploadMapper sysFileUploadMapper;

    @Autowired
    private RedisCache redisCache;

    // 5MB = 5 * 1024 * 1024 字节，超过这个大小就使用分片上传
    private static final long CHUNK_THRESHOLD = 5 * 1024 * 1024;
    // 默认分片大小为5MB，满足MinIO的ComposeObject要求
    private static final int DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024;
    // Redis缓存过期时间（7天）
    private static final Integer CACHE_EXPIRE_TIME = 7 * 24 * 60 * 60;

    @Override
    public String upload(MultipartFile file, String bizType) {
        // 检查文件大小
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        // 如果文件大小超过5MB，使用分片上传
        // 注意：这里不会实际调用，因为我们在前端实现分片上传
        // 这里保留是为了兼容原有的上传逻辑
        if (file.getSize() > CHUNK_THRESHOLD) {
            try {
                log.info("文件大小超过{}MB，使用分片上传", CHUNK_THRESHOLD / (1024 * 1024));
                return uploadLargeFile(file, bizType, DEFAULT_CHUNK_SIZE);
            } catch (IOException e) {
                throw new ServiceException("分片上传文件失败: " + e.getMessage());
            }
        }

        // 使用 try-with-resources 自动关闭流
        try (InputStream inputStream = file.getInputStream()) {
            String fileName = extractFilename(file);
            // 使用当前日期作为目录路径，格式为：yyyy/MM/dd
            String objectName = DateUtils.datePath() + "/" + fileName;

            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();

            minioClient.putObject(args);

            // 保存文件记录
            String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + objectName;
            saveFileRecord(file, url, objectName, bizType, null);

            return url;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new ServiceException("上传文件失败: " + e.getMessage());
        }
    }
    private String extractFilename(MultipartFile file) throws IOException {
        // 获取原始文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            // 如果没有原始文件名，则生成一个随机文件名
            // 使用新的文件类型识别方法
            String fileExtension = FileTypeUtils.getRealFileType(file);
            if (StringUtils.isEmpty(fileExtension)) {
                // 如果无法识别，则使用原来的方法
                fileExtension = FileTypeUtils.getFileExtendName(file.getBytes());
            }
            return IdUtils.fastSimpleUUID() + "." + fileExtension.toLowerCase();
        }

        // 为了防止文件名冲突，在原始文件名前添加时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileNameWithoutExt = originalFilename;
        String extension = "";

        // 提取文件扩展名
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex > 0) {
            fileNameWithoutExt = originalFilename.substring(0, lastDotIndex);
            extension = originalFilename.substring(lastDotIndex);
        }

        // 返回格式：时间戳_原始文件名.扩展名
        return timestamp + "_" + fileNameWithoutExt + extension;
    }
    private void saveFileRecord(MultipartFile file, String url, String path, String bizType, Long bizId) throws IOException {
        SysOss oss = new SysOss();
        oss.setOriginalName(file.getOriginalFilename());
        oss.setFileName(path.substring(path.lastIndexOf("/") + 1));

        // 使用新的文件类型识别方法
        String fileExtension = FileTypeUtils.getRealFileType(file);
        if (StringUtils.isEmpty(fileExtension)) {
            // 如果无法识别，则使用原来的方法
            fileExtension = FileTypeUtils.getFileExtendName(file.getBytes());
        }
        oss.setFileSuffix(fileExtension.toLowerCase());

        oss.setFileUrl(url);
        oss.setFilePath(path);
        oss.setFileSize(file.getSize());
        oss.setMimeType(file.getContentType());
        oss.setServiceType("minio");
        oss.setBizType(bizType);
        oss.setBizId(bizId);
        oss.setCreateBy(SecurityUtils.getUsername());
        oss.setCreateTime(DateUtils.getNowDate());

        sysOssMapper.insertSysOss(oss);
    }
    @Override
    public boolean deleteByUrl(String url) {
        SysOss oss = sysOssMapper.selectByUrl(url);
        if (oss == null) {
            return false;
        }

        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(oss.getFilePath())
                    .build());
            oss.setStatus(1);
            sysOssMapper.updateSysOss(oss);
            return true;
        } catch (Exception e) {
            throw new ServiceException("删除文件失败" + e.getMessage());
        }
    }

    @Override
    public String uploadLargeFile(MultipartFile file, String bizType, int chunkSize) throws IOException {
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        // 创建临时目录存放分片
        String tempDir = System.getProperty("java.io.tmpdir");
        String uuid = UUID.randomUUID().toString();
        Path tempPath = Paths.get(tempDir, uuid);
        Files.createDirectories(tempPath);

        try {
            // 生成文件名
            String fileName = extractFilename(file);
            // 使用当前日期作为目录路径，格式为：yyyy/MM/dd
            String objectName = DateUtils.datePath() + "/" + fileName;

            // 分片信息
            long fileSize = file.getSize();
            int partCount = (int) Math.ceil((double) fileSize / chunkSize);
            log.info("文件大小: {}MB, 分片数: {}, 分片大小: {}MB",
                    fileSize / (1024 * 1024), partCount, chunkSize / (1024 * 1024));

            // 存储分片文件名
            List<String> partObjectNames = new ArrayList<>();

            // 将文件分片并上传
            byte[] buffer = new byte[chunkSize];
            int bytesRead;
            int partNumber = 1;

            // 使用 try-with-resources 自动关闭流
            try (InputStream inputStream = file.getInputStream()) {
                while ((bytesRead = inputStream.read(buffer)) > 0) {
                    // 创建分片文件
                    String partFileName = String.format("%s.part%d", fileName, partNumber);
                    Path partPath = tempPath.resolve(partFileName);
                    try (FileOutputStream fos = new FileOutputStream(partPath.toFile())) {
                        fos.write(buffer, 0, bytesRead);
                    }

                    // 上传分片
                    String partObjectName = String.format("%s.part%d", objectName, partNumber);
                    minioClient.uploadObject(
                        UploadObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(partObjectName)
                            .filename(partPath.toString())
                            .contentType(file.getContentType())
                            .build()
                    );

                    partObjectNames.add(partObjectName);
                    log.info("分片 {}/{} 上传成功", partNumber, partCount);
                    partNumber++;
                }
            }

            // 合并分片
            List<ComposeSource> sources = new ArrayList<>();
            for (String partObjectName : partObjectNames) {
                sources.add(
                    ComposeSource.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(partObjectName)
                        .build()
                );
            }

            // 执行合并操作
            minioClient.composeObject(
                ComposeObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .sources(sources)
                    .build()
            );

            log.info("分片合并成功");

            // 删除分片
            for (String partObjectName : partObjectNames) {
                try {
                    minioClient.removeObject(
                        RemoveObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(partObjectName)
                            .build()
                    );
                } catch (Exception e) {
                    log.warn("删除分片文件失败: {}", partObjectName, e);
                }
            }

            // 保存文件记录
            String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + objectName;
            saveFileRecord(file, url, objectName, bizType, null);

            return url;
        } catch (Exception e) {
            log.error("分片上传文件失败", e);
            throw new ServiceException("分片上传文件失败: " + e.getMessage());
        } finally {
            // 清理临时目录
            try {
                Files.walk(tempPath)
                    .sorted(java.util.Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
            } catch (Exception e) {
                log.warn("清理临时目录失败", e);
            }
        }
    }

    @Override
    public List<String> uploadBatch(MultipartFile[] files, String bizType) {
        if (files == null || files.length == 0) {
            throw new ServiceException("上传文件不能为空");
        }

        List<String> urls = new ArrayList<>();
        for (MultipartFile file : files) {
            String url = upload(file, bizType);
            urls.add(url);
        }

        return urls;
    }

    @Override
    public boolean deleteByBiz(String bizType, Long bizId) {
        return false;
    }

    @Override
    public void updateBizInfo(List<String> urls, String bizType, Long bizId) {

    }

    @Override
    public InputStream getStreamByUrl(String url) {
        return null;
    }

    @Override
    public String getEndpoint() {
        return null;
    }

    /**
     * 检查分片是否存在 - 支持断点续传
     */
    @Override
    public ChunkUploadResponseDTO checkChunk(String identifier, Integer chunkNumber, String bizType) throws IOException {
        ChunkUploadResponseDTO response = new ChunkUploadResponseDTO();

        // 首先检查文件是否已经上传完成
        SysFileUpload fileUpload = sysFileUploadMapper.selectSysFileUploadByFileIdentifier(identifier);
        if (fileUpload != null && fileUpload.getStatus() == 1) {
            // 文件已存在，设置跳过上传
            response.setSkipUpload(true);
            response.setProgress(100);
            response.setFileUrl(fileUpload.getFileUrl());
            response.setUploadedChunks(new Integer[0]); // 文件已完成，不需要分片信息
            return response;
        }

        // 检查Redis缓存中是否有该文件的路径
        String cachedPath = redisCache.getCacheObject(CacheConstants.FILE_PATH_CACHE_KEY + identifier);
        if (StringUtils.isNotEmpty(cachedPath)) {
            try {
                // 如果缓存中有路径，则检查MinIO中是否存在该文件
                minioClient.statObject(
                    io.minio.StatObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(cachedPath)
                        .build()
                );

                // 文件存在，设置跳过上传
                response.setSkipUpload(true);
                response.setProgress(100);

                // 获取文件URL
                String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + cachedPath;
                response.setFileUrl(url);

                // 保存到数据库
                if (fileUpload == null) {
                    saveFileUploadRecord(identifier, cachedPath, url, 0L, 0, bizType, 1);
                }

                return response;
            } catch (Exception e) {
                // 缓存中的路径已失效，移除缓存
                redisCache.deleteObject(CacheConstants.FILE_PATH_CACHE_KEY + identifier);
                log.debug("缓存中的文件路径已失效: {}", cachedPath);
            }
        }

        // 尝试从 MinIO 中获取已完成的文件
        try {
            // 首先检查是否存在使用日期路径的合并文件
            String datePath = DateUtils.datePath();
            String prefix = datePath + "/";

            // 列出当前日期目录下的所有文件
            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .prefix(prefix)
                    .recursive(true)
                    .build());

            // 检查是否有包含文件标识的文件
            for (Result<Item> result : results) {
                Item item = result.get();
                // 如果文件名包含文件标识，则认为文件已存在
                if (item.objectName().contains(identifier)) {
                    // 文件已存在，设置跳过上传
                    response.setSkipUpload(true);
                    response.setProgress(100);

                    // 获取文件URL
                    String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + item.objectName();
                    response.setFileUrl(url);

                    // 更新缓存
                    redisCache.setCacheObject(CacheConstants.FILE_PATH_CACHE_KEY + identifier, item.objectName(), Integer.valueOf(CACHE_EXPIRE_TIME), TimeUnit.SECONDS);

                    // 保存到数据库
                    if (fileUpload == null) {
                        saveFileUploadRecord(identifier, item.objectName(), url, item.size(), 0, bizType, 1);
                    }

                    return response;
                }
            }

            // 兼容旧的存储方式，检查chunks目录
            String oldObjectName = "chunks/" + identifier + "/merged";
            minioClient.statObject(
                io.minio.StatObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(oldObjectName)
                    .build()
            );

            // 如果没有抛出异常，说明文件已存在
            response.setSkipUpload(true);
            response.setProgress(100);

            // 获取文件URL
            String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + oldObjectName;
            response.setFileUrl(url);

            // 更新缓存
            redisCache.setCacheObject(CacheConstants.FILE_PATH_CACHE_KEY + identifier, oldObjectName, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);

            // 保存到数据库
            if (fileUpload == null) {
                saveFileUploadRecord(identifier, oldObjectName, url, 0L, 0, bizType, 1);
            }

            return response;
        } catch (Exception e) {
            // 文件不存在，继续检查
            log.debug("检查合并文件失败，可能文件不存在: {}", e.getMessage());
        }

        // 获取已上传的分片列表
        List<Integer> uploadedChunks = getUploadedChunks(identifier);
        response.setUploadedChunks(uploadedChunks.toArray(new Integer[0]));

        // 计算进度
        Integer progress = 0;
        if (fileUpload != null && fileUpload.getChunkTotal() > 0) {
            progress = (int) Math.floor((double) uploadedChunks.size() / fileUpload.getChunkTotal() * 100);
        } else {
            // 从Redis缓存中获取进度
            Integer cachedProgress = redisCache.getCacheObject(CacheConstants.FILE_UPLOAD_PROGRESS_KEY + identifier);
            if (cachedProgress != null) {
                progress = cachedProgress;
            }
        }
        response.setProgress(progress);

        // 检查当前分片是否存在
        SysFileChunk chunk = sysFileChunkMapper.selectSysFileChunkByFileIdentifierAndChunkNumber(identifier, chunkNumber);
        boolean currentChunkExists = chunk != null;

        // 设置是否跳过当前分片的上传
        response.setSkipUpload(currentChunkExists);

        // 在分片上传中，我们不需要返回实际的URL，因为文件还没有完全上传完成
        response.setFileUrl(null);

        log.info("检查分片结果 - 文件标识: {}, 分片号: {}, 已上传分片数: {}/{}, 进度: {}%, 当前分片存在: {}",
                identifier, chunkNumber, uploadedChunks.size(),
                fileUpload != null ? fileUpload.getChunkTotal() : "未知", progress, currentChunkExists);

        return response;
    }

    /**
     * 上传分片
     */
    @Override
    public ChunkUploadResponseDTO uploadChunk(MultipartFile file, ChunkUploadDTO chunkUploadDTO) throws IOException {
        ChunkUploadResponseDTO response = new ChunkUploadResponseDTO();

        // 检查文件上传记录是否存在
        SysFileUpload fileUpload = sysFileUploadMapper.selectSysFileUploadByFileIdentifier(chunkUploadDTO.getIdentifier());
        if (fileUpload == null) {
            // 创建文件上传记录
            fileUpload = new SysFileUpload();
            fileUpload.setFileIdentifier(chunkUploadDTO.getIdentifier());
            fileUpload.setFileName(chunkUploadDTO.getFilename());
            // 设置一个临时的文件路径，合并完成后会更新
            String tempPath = "chunks/" + chunkUploadDTO.getIdentifier() + "/temp";
            fileUpload.setFilePath(tempPath);
            // 设置一个临时的文件URL，合并完成后会更新
            String tempUrl = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + tempPath;
            fileUpload.setFileUrl(tempUrl);
            fileUpload.setFileSize(chunkUploadDTO.getTotalSize());
            fileUpload.setChunkTotal(chunkUploadDTO.getTotalChunks());
            fileUpload.setBizType(chunkUploadDTO.getBizType());
            fileUpload.setStatus(0); // 上传中
            fileUpload.setCreateTime(DateUtils.getNowDate());
            sysFileUploadMapper.insertSysFileUpload(fileUpload);
        }

        // 检查分片是否已经上传
        SysFileChunk chunk = sysFileChunkMapper.selectSysFileChunkByFileIdentifierAndChunkNumber(
                chunkUploadDTO.getIdentifier(), chunkUploadDTO.getChunkNumber());

        if (chunk == null) {
            // 上传分片到MinIO
            String chunkPath = String.format("chunks/%s/%d", chunkUploadDTO.getIdentifier(), chunkUploadDTO.getChunkNumber());
            try (InputStream inputStream = file.getInputStream()) {
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(chunkPath)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build()
                );

                // 保存分片记录
                chunk = new SysFileChunk();
                chunk.setFileIdentifier(chunkUploadDTO.getIdentifier());
                chunk.setChunkNumber(chunkUploadDTO.getChunkNumber());
                chunk.setChunkSize(file.getSize());
                chunk.setTotalChunks(chunkUploadDTO.getTotalChunks());
                chunk.setTotalSize(chunkUploadDTO.getTotalSize());
                chunk.setFileName(chunkUploadDTO.getFilename());
                chunk.setChunkPath(chunkPath);
                chunk.setBizType(chunkUploadDTO.getBizType());
                chunk.setStatus(0); // 上传中
                chunk.setCreateTime(DateUtils.getNowDate());
                sysFileChunkMapper.insertSysFileChunk(chunk);
            } catch (Exception e) {
                log.error("上传分片到MinIO失败", e);
                throw new ServiceException("上传分片失败: " + e.getMessage());
            }
        }

        // 获取已上传的分片
        List<Integer> uploadedChunks = getUploadedChunks(chunkUploadDTO.getIdentifier());
        response.setUploadedChunks(uploadedChunks.toArray(new Integer[0]));

        // 计算进度
        int progress = (int) Math.floor((double) uploadedChunks.size() / chunkUploadDTO.getTotalChunks() * 100);
        // 更新Redis缓存中的进度
        redisCache.setCacheObject(CacheConstants.FILE_UPLOAD_PROGRESS_KEY + chunkUploadDTO.getIdentifier(),
                progress, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
        response.setProgress(progress);

        // 如果所有分片都上传完成，则自动合并
        if (uploadedChunks.size() == chunkUploadDTO.getTotalChunks()) {
            try {
                String url = mergeChunks(
                    chunkUploadDTO.getIdentifier(),
                    chunkUploadDTO.getFilename(),
                    chunkUploadDTO.getTotalChunks(),
                    chunkUploadDTO.getTotalSize(),
                    chunkUploadDTO.getBizType()
                );
                response.setFileUrl(url);
                response.setProgress(100);
            } catch (Exception e) {
                log.error("自动合并分片失败", e);
            }
        } else {
            // 在分片上传过程中，我们不需要返回实际的URL，因为文件还没有完全上传完成
            response.setFileUrl(null);
        }

        return response;
    }

    /**
     * 合并分片
     */
    @Override
    public String mergeChunks(String identifier, String filename, Integer totalChunks, Long totalSize, String bizType) throws IOException {
        // 检查分片是否完整 - 使用专门的方法检查所有分片（包括已完成的）
        List<Integer> allChunks = getAllChunks(identifier);
        if (allChunks.size() != totalChunks) {
            throw new ServiceException("分片不完整，已上传 " + allChunks.size() + "/" + totalChunks);
        }

        // 获取文件扩展名
        String extension = filename.contains(".") ? filename.substring(filename.lastIndexOf(".")) : "";
        // 使用原始文件名，但添加时间戳前缀以避免冲突
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileNameWithoutExt = filename;
        if (filename.contains(".")) {
            fileNameWithoutExt = filename.substring(0, filename.lastIndexOf("."));
        }
        String newFileName = timestamp + "_" + fileNameWithoutExt + extension;
        // 使用当前日期作为目录路径，格式为：yyyy/MM/dd
        String objectName = DateUtils.datePath() + "/" + newFileName;

        try {
            // 准备合并源
            List<ComposeSource> sources = new ArrayList<>();
            for (int i = 1; i <= totalChunks; i++) {
                // 获取分片路径
                SysFileChunk chunk = sysFileChunkMapper.selectSysFileChunkByFileIdentifierAndChunkNumber(identifier, i);
                if (chunk == null) {
                    throw new ServiceException("分片 " + i + " 不存在");
                }

                // 验证分片在MinIO中是否真的存在
                try {
                    minioClient.statObject(
                        StatObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(chunk.getChunkPath())
                            .build()
                    );
                } catch (Exception e) {
                    log.error("分片 {} 在MinIO中不存在: {}", i, chunk.getChunkPath(), e);
                    throw new ServiceException("分片 " + i + " 在存储中不存在");
                }

                sources.add(
                    ComposeSource.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(chunk.getChunkPath())
                        .build()
                );
            }

            // 执行合并操作
            minioClient.composeObject(
                ComposeObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .sources(sources)
                    .build()
            );

            log.info("分片合并成功");

            // 删除分片
            for (int i = 1; i <= totalChunks; i++) {
                SysFileChunk chunk = sysFileChunkMapper.selectSysFileChunkByFileIdentifierAndChunkNumber(identifier, i);
                if (chunk != null) {
                    try {
                        // 删除MinIO中的分片
                        minioClient.removeObject(
                            RemoveObjectArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .object(chunk.getChunkPath())
                                .build()
                        );

                        // 更新分片状态
                        chunk.setStatus(1); // 已完成
                        chunk.setUpdateTime(DateUtils.getNowDate());
                        sysFileChunkMapper.updateSysFileChunk(chunk);
                    } catch (Exception e) {
                        log.warn("删除分片文件失败: {}", chunk.getChunkPath(), e);
                    }
                }
            }

            // 保存文件记录
            String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + objectName;

            // 保存到SysOss表
            saveChunkFileRecord(filename, url, objectName, bizType, totalSize);

            // 更新文件上传记录
            SysFileUpload fileUpload = sysFileUploadMapper.selectSysFileUploadByFileIdentifier(identifier);
            if (fileUpload != null) {
                fileUpload.setFilePath(objectName);
                fileUpload.setFileUrl(url);
                fileUpload.setStatus(1); // 已完成
                fileUpload.setUpdateTime(DateUtils.getNowDate());
                sysFileUploadMapper.updateSysFileUpload(fileUpload);
            }

            // 更新Redis缓存中的进度
            redisCache.setCacheObject(CacheConstants.FILE_UPLOAD_PROGRESS_KEY + identifier, 100, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);

            // 在Redis缓存中存储文件路径，便于后续检查
            redisCache.setCacheObject(CacheConstants.FILE_PATH_CACHE_KEY + identifier, objectName, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);

            return url;
        } catch (Exception e) {
            log.error("合并分片失败", e);
            throw new ServiceException("合并分片失败: " + e.getMessage());
        }
    }



    /**
     * 获取已上传的分片（只返回状态为0的分片，即上传中的分片）
     */
    private List<Integer> getUploadedChunks(String identifier) {
        // 从数据库中查询已上传的分片
        List<SysFileChunk> chunks = sysFileChunkMapper.selectSysFileChunkByFileIdentifier(identifier);
        if (chunks == null || chunks.isEmpty()) {
            return new ArrayList<>();
        }

        return chunks.stream()
            .filter(chunk -> chunk.getStatus() == 0) // 只返回状态为0（上传中）的分片
            .map(SysFileChunk::getChunkNumber)
            .sorted()
            .collect(Collectors.toList());
    }

    /**
     * 获取所有分片（包括已完成的分片）- 用于合并时检查分片完整性
     */
    private List<Integer> getAllChunks(String identifier) {
        // 从数据库中查询所有分片
        List<SysFileChunk> chunks = sysFileChunkMapper.selectSysFileChunkByFileIdentifier(identifier);
        if (chunks == null || chunks.isEmpty()) {
            return new ArrayList<>();
        }

        return chunks.stream()
            .map(SysFileChunk::getChunkNumber)
            .sorted()
            .collect(Collectors.toList());
    }

    /**
     * 保存文件上传记录
     */
    private void saveFileUploadRecord(String fileIdentifier, String filePath, String fileUrl, Long fileSize,
                                     Integer chunkTotal, String bizType, Integer status) {
        SysFileUpload fileUpload = new SysFileUpload();
        fileUpload.setFileIdentifier(fileIdentifier);
        fileUpload.setFileName(filePath.substring(filePath.lastIndexOf("/") + 1));
        fileUpload.setFilePath(filePath);
        fileUpload.setFileUrl(fileUrl);
        fileUpload.setFileSize(fileSize);
        fileUpload.setChunkTotal(chunkTotal);
        fileUpload.setBizType(bizType);
        fileUpload.setStatus(status);
        fileUpload.setCreateTime(DateUtils.getNowDate());

        sysFileUploadMapper.insertSysFileUpload(fileUpload);
    }

    /**
     * 保存分片文件记录
     */
    private void saveChunkFileRecord(String filename, String url, String path, String bizType, Long fileSize) {
        SysOss oss = new SysOss();
        // 设置原始文件名
        oss.setOriginalName(filename);
        // 设置存储的文件名（包含时间戳前缀）
        oss.setFileName(path.substring(path.lastIndexOf("/") + 1));

        // 获取文件后缀
        String extension = filename.contains(".") ? filename.substring(filename.lastIndexOf(".") + 1) : "";
        oss.setFileSuffix(extension.toLowerCase());

        oss.setFileUrl(url);
        oss.setFilePath(path);
        oss.setFileSize(fileSize);
        oss.setMimeType(getContentType(extension));
        oss.setServiceType("minio");
        oss.setBizType(bizType);
        oss.setCreateBy(SecurityUtils.getUsername());
        oss.setCreateTime(DateUtils.getNowDate());

        sysOssMapper.insertSysOss(oss);
    }

    /**
     * 根据文件后缀获取内容类型
     */
    private String getContentType(String extension) {
        if (StringUtils.isEmpty(extension)) {
            return "application/octet-stream";
        }

        extension = extension.toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "7z":
                return "application/x-7z-compressed";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            default:
                return "application/octet-stream";
        }
    }
}
