package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumTagI18n;

/**
 * 标签多语言内容Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IForumTagI18nService 
{
    /**
     * 查询标签多语言内容
     * 
     * @param i18nId 标签多语言内容主键
     * @return 标签多语言内容
     */
    public ForumTagI18n selectForumTagI18nByI18nId(Long i18nId);

    /**
     * 查询标签多语言内容列表
     * 
     * @param forumTagI18n 标签多语言内容
     * @return 标签多语言内容集合
     */
    public List<ForumTagI18n> selectForumTagI18nList(ForumTagI18n forumTagI18n);

    /**
     * 新增标签多语言内容
     * 
     * @param forumTagI18n 标签多语言内容
     * @return 结果
     */
    public int insertForumTagI18n(ForumTagI18n forumTagI18n);

    /**
     * 修改标签多语言内容
     * 
     * @param forumTagI18n 标签多语言内容
     * @return 结果
     */
    public int updateForumTagI18n(ForumTagI18n forumTagI18n);

    /**
     * 批量删除标签多语言内容
     * 
     * @param i18nIds 需要删除的标签多语言内容主键集合
     * @return 结果
     */
    public int deleteForumTagI18nByI18nIds(Long[] i18nIds);

    /**
     * 删除标签多语言内容信息
     * 
     * @param i18nId 标签多语言内容主键
     * @return 结果
     */
    public int deleteForumTagI18nByI18nId(Long i18nId);
}
