package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumCarousel;

/**
 * 轮播图Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumCarouselService 
{
    /**
     * 查询轮播图
     * 
     * @param carouselId 轮播图主键
     * @return 轮播图
     */
    public ForumCarousel selectForumCarouselByCarouselId(Long carouselId);

    /**
     * 查询轮播图列表
     * 
     * @param forumCarousel 轮播图
     * @return 轮播图集合
     */
    public List<ForumCarousel> selectForumCarouselList(ForumCarousel forumCarousel);

    /**
     * 新增轮播图
     * 
     * @param forumCarousel 轮播图
     * @return 结果
     */
    public int insertForumCarousel(ForumCarousel forumCarousel);

    /**
     * 修改轮播图
     * 
     * @param forumCarousel 轮播图
     * @return 结果
     */
    public int updateForumCarousel(ForumCarousel forumCarousel);

    /**
     * 批量删除轮播图
     * 
     * @param carouselIds 需要删除的轮播图主键集合
     * @return 结果
     */
    public int deleteForumCarouselByCarouselIds(Long[] carouselIds);

    /**
     * 删除轮播图信息
     * 
     * @param carouselId 轮播图主键
     * @return 结果
     */
    public int deleteForumCarouselByCarouselId(Long carouselId);
}
