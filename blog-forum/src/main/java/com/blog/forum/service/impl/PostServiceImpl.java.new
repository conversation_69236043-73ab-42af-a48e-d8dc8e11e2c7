package com.blog.forum.service.impl;

import com.blog.common.core.domain.entity.SysUser;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.common.utils.SensitiveWordUtils;
import com.blog.common.utils.StringUtils;
import com.blog.forum.domain.ForumPost;
import com.blog.forum.domain.ForumUserCollect;
import com.blog.forum.domain.ForumUserLike;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.TagVO;
import com.blog.forum.domain.vo.UserPostDetailVO;
import com.blog.forum.mapper.ForumPostMapper;
import com.blog.forum.mapper.ForumPostTagMapper;
import com.blog.forum.mapper.ForumUserCollectMapper;
import com.blog.forum.mapper.ForumUserLikeMapper;
import com.blog.forum.service.PostService;
import com.blog.system.utils.LangUtils;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PostServiceImpl implements PostService {

    @Autowired
    private ForumPostMapper postMapper;

    @Autowired
    private ForumPostTagMapper postTagMapper;

    @Autowired
    private ForumUserLikeMapper userLikeMapper;

    @Autowired
    private ForumUserCollectMapper userCollectMapper;

    @Override
    public List<PostDetailVO> getPostList(PostListDTO postListDTO) {
        Long langId = LangUtils.getCurrentLangId();
        // 设置状态为0（正常）
        postListDTO.setStatus("0");
        List<PostDetailVO> postList = postMapper.selectPostQuery(langId, postListDTO);

        // 设置标签和点赞状态
        Long userId = null;
        try {
            userId = SecurityUtils.getLoginUser().getUser().getUserId(); // 获取当前用户ID，未登录则为null
        } catch (Exception e) {
            // 未登录用户，userId保持为null
        }

        if (postList != null && !postList.isEmpty()) {
            for (PostDetailVO post : postList) {
                // 查询帖子标签
                List<TagVO> tags = postTagMapper.selectTagsByPostId(langId, post.getPostId());
                post.setTags(tags);

                // 设置是否点赞
                if (userId != null) {
                    ForumUserLike userLike = new ForumUserLike();
                    userLike.setUserId(userId);
                    userLike.setContentId(post.getPostId());
                    userLike.setContentType("post");
                    post.setLiked(userLikeMapper.checkUserLike(userLike) > 0);

                    // 设置是否收藏
                    ForumUserCollect userCollect = new ForumUserCollect();
                    userCollect.setUserId(userId);
                    userCollect.setContentId(post.getPostId());
                    userCollect.setContentType("post");
                    post.setCollected(userCollectMapper.checkUserCollect(userCollect) > 0);
                }

                // 过滤敏感词
                post.setTitle(SensitiveWordUtils.filter(post.getTitle()));
                post.setSummary(SensitiveWordUtils.filter(post.getSummary()));
                post.setContent(SensitiveWordUtils.filter(post.getContent()));
            }
        }

        return postList;
    }

    @Override
    public PostDetailVO getPostDetail(Long postId) {
        Long langId = LangUtils.getCurrentLangId();
        PostDetailVO post = postMapper.selectPostDetail(langId, postId);

        if (post == null) {
            throw new ServiceException("帖子不存在或已删除");
        }

        // 增加浏览量
        postMapper.incrementViewCount(postId);

        // 查询帖子标签
        List<TagVO> tags = postTagMapper.selectTagsByPostId(langId, postId);
        post.setTags(tags);

        // 设置是否点赞和收藏
        try {
            Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
            if (userId != null) {
                ForumUserLike userLike = new ForumUserLike();
                userLike.setUserId(userId);
                userLike.setContentId(postId);
                userLike.setContentType("post");
                post.setLiked(userLikeMapper.checkUserLike(userLike) > 0);

                ForumUserCollect userCollect = new ForumUserCollect();
                userCollect.setUserId(userId);
                userCollect.setContentId(postId);
                userCollect.setContentType("post");
                post.setCollected(userCollectMapper.checkUserCollect(userCollect) > 0);
            }
        } catch (Exception e) {
            // 未登录用户，不设置点赞和收藏状态
        }

        // 过滤敏感词
        post.setTitle(SensitiveWordUtils.filter(post.getTitle()));
        post.setSummary(SensitiveWordUtils.filter(post.getSummary()));
        post.setContent(SensitiveWordUtils.filter(post.getContent()));

        return post;
    }

    @Override
    public List<UserPostDetailVO> getUserPostList(PostListDTO postListDTO, Long userId) {
        PageHelper.startPage(postListDTO.getPageNum(), postListDTO.getPageSize());
        Long langId = LangUtils.getCurrentLangId();

        List<UserPostDetailVO> postList = postMapper.selectUserPostList(
                langId, userId, postListDTO.getCategoryId(),
                postListDTO.getSortType(), postListDTO.getKeyword()
        );

        // 设置标签和点赞状态
        Long currentUserId = SecurityUtils.getUserId();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if (currentUserId == null && user.getUserId() == null) {
            throw new ServiceException("请先登录");
        }

        if (postList != null && !postList.isEmpty()) {
            for (UserPostDetailVO post : postList) {
                // 查询帖子标签
                List<TagVO> tags = postTagMapper.selectTagsByPostId(langId, post.getPostId());
                post.setTags(tags);

                // 设置是否点赞
                ForumUserLike userLike = new ForumUserLike();
                userLike.setUserId(currentUserId);
                userLike.setContentId(post.getPostId());
                userLike.setContentType("post");
                post.setLiked(userLikeMapper.checkUserLike(userLike) > 0);

                // 设置是否收藏
                ForumUserCollect userCollect = new ForumUserCollect();
                userCollect.setUserId(currentUserId);
                userCollect.setContentId(post.getPostId());
                userCollect.setContentType("post");
                post.setCollected(userCollectMapper.checkUserCollect(userCollect) > 0);

                // 过滤敏感词
                post.setTitle(SensitiveWordUtils.filter(post.getTitle()));
                post.setSummary(SensitiveWordUtils.filter(post.getSummary()));
                post.setContent(SensitiveWordUtils.filter(post.getContent()));
            }
        }

        return postList;
    }

    @Override
    public UserPostDetailVO getUserPostDetail(Long postId) {
        Long langId = LangUtils.getCurrentLangId();
        UserPostDetailVO post = postMapper.selectUserPostDetail(langId, postId);

        if (post == null) {
            throw new ServiceException("帖子不存在或已删除");
        }

        // 增加浏览量
        postMapper.incrementViewCount(postId);

        // 查询帖子标签
        List<TagVO> tags = postTagMapper.selectTagsByPostId(langId, postId);
        post.setTags(tags);

        // 设置是否点赞和收藏
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId != null) {
            ForumUserLike userLike = new ForumUserLike();
            userLike.setUserId(currentUserId);
            userLike.setContentId(postId);
            userLike.setContentType("post");
            post.setLiked(userLikeMapper.checkUserLike(userLike) > 0);

            ForumUserCollect userCollect = new ForumUserCollect();
            userCollect.setUserId(currentUserId);
            userCollect.setContentId(postId);
            userCollect.setContentType("post");
            post.setCollected(userCollectMapper.checkUserCollect(userCollect) > 0);
        }

        // 过滤敏感词
        post.setTitle(SensitiveWordUtils.filter(post.getTitle()));
        post.setSummary(SensitiveWordUtils.filter(post.getSummary()));
        post.setContent(SensitiveWordUtils.filter(post.getContent()));

        return post;
    }

    @Override
    public int likePost(Long postId) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }

        // 检查帖子是否存在
        ForumPost post = postMapper.selectForumPostByPostId(postId);
        if (post == null || !"0".equals(post.getStatus())) {
            throw new ServiceException("帖子不存在或已删除");
        }

        // 检查是否已点赞
        ForumUserLike userLike = new ForumUserLike();
        userLike.setUserId(userId);
        userLike.setContentId(postId);
        userLike.setContentType("post");

        if (userLikeMapper.checkUserLike(userLike) > 0) {
            // 已点赞，取消点赞
            userLikeMapper.deleteUserLike(userLike);
            // 减少点赞数
            postMapper.incrementLikeCount(postId, -1);
            return 0; // 返回0表示取消点赞
        } else {
            // 未点赞，添加点赞
            userLike.setCreateTime(DateUtils.getNowDate());
            userLikeMapper.insertUserLike(userLike);
            // 增加点赞数
            postMapper.incrementLikeCount(postId, 1);
            return 1; // 返回1表示点赞成功
        }
    }

    @Override
    public int collectPost(Long postId) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }

        // 检查帖子是否存在
        ForumPost post = postMapper.selectForumPostByPostId(postId);
        if (post == null || !"0".equals(post.getStatus())) {
            throw new ServiceException("帖子不存在或已删除");
        }

        // 检查是否已收藏
        ForumUserCollect userCollect = new ForumUserCollect();
        userCollect.setUserId(userId);
        userCollect.setContentId(postId);
        userCollect.setContentType("post");

        if (userCollectMapper.checkUserCollect(userCollect) > 0) {
            // 已收藏，取消收藏
            userCollectMapper.deleteUserCollect(userCollect);
            // 减少收藏数
            postMapper.incrementCollectCount(postId, -1);
            return 0; // 返回0表示取消收藏
        } else {
            // 未收藏，添加收藏
            userCollect.setCreateTime(DateUtils.getNowDate());
            userCollectMapper.insertUserCollect(userCollect);
            // 增加收藏数
            postMapper.incrementCollectCount(postId, 1);
            return 1; // 返回1表示收藏成功
        }
    }

    @Override
    public void sharePost(Long postId) {
        // 检查帖子是否存在
        ForumPost post = postMapper.selectForumPostByPostId(postId);
        if (post == null || !"0".equals(post.getStatus())) {
            throw new ServiceException("帖子不存在或已删除");
        }

        // 增加分享数
        postMapper.incrementShareCount(postId);
    }
}
