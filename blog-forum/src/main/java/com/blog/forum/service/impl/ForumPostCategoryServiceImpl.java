package com.blog.forum.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.blog.common.utils.DateUtils;
import com.blog.forum.domain.ForumCategoryI18n;
import com.blog.forum.mapper.ForumCategoryI18nMapper;
import com.blog.forum.util.LangUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumPostCategoryMapper;
import com.blog.forum.mapper.ForumLanguageMapper;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.domain.ForumPostCategory;
import com.blog.forum.service.IForumPostCategoryService;

/**
 * 帖子分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumPostCategoryServiceImpl implements IForumPostCategoryService
{
    @Autowired
    private ForumPostCategoryMapper forumPostCategoryMapper;

    @Autowired
    private ForumLanguageMapper forumLanguageMapper;

    @Autowired
    private ForumCategoryI18nMapper forumCategoryI18nMapper;

    /**
     * 查询帖子分类
     *
     * @param categoryId 帖子分类主键
     * @return 帖子分类
     */
    @Override
    public ForumPostCategory selectForumPostCategoryByCategoryId(Long categoryId)
    {
        // 获取当前语言ID
        Long langId = LangUtils.getCurrentLangId();
        return forumPostCategoryMapper.selectForumPostCategoryByCategoryId(categoryId, langId);
    }

    /**
     * 查询帖子分类列表
     *
     * @param forumPostCategory 帖子分类
     * @return 帖子分类
     */
    @Override
    public List<ForumPostCategory> selectForumPostCategoryList(ForumPostCategory forumPostCategory)
    {
        if (forumPostCategory.getStatus() == null) {
            forumPostCategory.setStatus("0");
        }
        // 获取当前语言ID
        Long langId = LangUtils.getCurrentLangId();
        return forumPostCategoryMapper.selectForumPostCategoryList(forumPostCategory, langId);
    }

    /**
     * 新增帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    @Override
    public int insertForumPostCategory(ForumPostCategory forumPostCategory)
    {
        forumPostCategory.setCreateTime(DateUtils.getNowDate());
        return forumPostCategoryMapper.insertForumPostCategory(forumPostCategory);
    }

    /**
     * 修改帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    @Override
    public int updateForumPostCategory(ForumPostCategory forumPostCategory)
    {
        forumPostCategory.setUpdateTime(DateUtils.getNowDate());
        return forumPostCategoryMapper.updateForumPostCategory(forumPostCategory);
    }

    /**
     * 批量删除帖子分类
     *
     * @param categoryIds 需要删除的帖子分类主键
     * @return 结果
     */
    @Override
    public int deleteForumPostCategoryByCategoryIds(Long[] categoryIds)
    {
        Long currentLangId = LangUtils.getCurrentLangId();
        int result = 0;
        for (Long categoryId : categoryIds) {
            ForumPostCategory category = forumPostCategoryMapper.selectForumPostCategoryByCategoryId(categoryId,currentLangId);
            if (category != null) {
                // 逻辑删除，将status设置为1（下线）
                category.setStatus("1");
                category.setUpdateTime(DateUtils.getNowDate());
                result += forumPostCategoryMapper.updateForumPostCategory(category);
            }
        }
        return result;
    }

    /**
     * 删除帖子分类信息
     *
     * @param categoryId 帖子分类主键
     * @return 结果
     */
    @Override
    public int deleteForumPostCategoryByCategoryId(Long categoryId)
    {
        return forumPostCategoryMapper.deleteForumPostCategoryByCategoryId(categoryId);
    }

    /**
     * 根据语言ID获取语言代码
     *
     * @param langId 语言ID
     * @return 语言代码
     */
    @Override
    public String getLangCodeById(Long langId) {
        ForumLanguage language = forumLanguageMapper.selectForumLanguageByLangId(langId);
        return language != null ? language.getLangCode() : null;
    }

    /**
     * 根据语言代码获取语言ID
     *
     * @param langCode 语言代码
     * @return 语言ID
     */
    @Override
    public Long getLangIdByCode(String langCode) {
        ForumLanguage language = forumLanguageMapper.selectByLangCode(langCode);
        return language != null ? language.getLangId() : null;
    }

    /**
     * 查询帖子分类列表（支持多语言）
     *
     * @param forumPostCategory 帖子分类
     * @param langId 语言ID
     * @return 帖子分类集合
     */
    @Override
    public List<ForumPostCategory> selectForumPostCategoryListWithI18n(ForumPostCategory forumPostCategory, Long langId) {
        // 先获取基本的分类列表
        if (forumPostCategory.getStatus() == null) {
            forumPostCategory.setStatus("0"); // 只查询正常状态的分类
        }

        List<ForumPostCategory> categoryList = forumPostCategoryMapper.selectForumPostCategoryList(forumPostCategory,langId);

        // 如果列表为空，直接返回
        if (categoryList == null || categoryList.isEmpty()) {
            return categoryList;
        }

        // 获取所有分类ID
        List<Long> categoryIds = categoryList.stream()
                .map(ForumPostCategory::getCategoryId)
                .collect(Collectors.toList());

        if (categoryIds.isEmpty()) {
            return categoryList;
        }

        try {
            // 查询这些分类的多语言信息
            List<ForumCategoryI18n> i18nList = forumCategoryI18nMapper.selectByCategoryIdsAndLangId(categoryIds, langId);

            if (i18nList == null || i18nList.isEmpty()) {
                return categoryList;
            }

            // 创建一个映射，方便查找
            Map<Long, String> categoryNameMap = i18nList.stream()
                    .collect(Collectors.toMap(
                            ForumCategoryI18n::getCategoryId,
                            ForumCategoryI18n::getCategoryName,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            // 更新分类名称
            for (ForumPostCategory category : categoryList) {
                String i18nName = categoryNameMap.get(category.getCategoryId());
                if (i18nName != null) {
                    category.setCategoryName(i18nName);
                }
            }
        } catch (Exception e) {
            // 如果发生异常，记录日志并返回原始列表
            e.printStackTrace();
        }

        return categoryList;
    }

    /**
     * 获取所有语言
     *
     * @return 语言列表
     */
    @Override
    public List<ForumLanguage> getAllLanguages() {
        return forumLanguageMapper.selectEnabledLanguageList();
    }
}
