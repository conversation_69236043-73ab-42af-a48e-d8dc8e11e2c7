package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumUserPostImageMapper;
import com.blog.forum.domain.ForumUserPostImage;
import com.blog.forum.service.IForumUserPostImageService;

/**
 * 用户发帖图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class ForumUserPostImageServiceImpl implements IForumUserPostImageService
{
    @Autowired
    private ForumUserPostImageMapper forumUserPostImageMapper;

    /**
     * 查询用户发帖图片
     *
     * @param imageId 用户发帖图片主键
     * @return 用户发帖图片
     */
    @Override
    public ForumUserPostImage selectForumUserPostImageByImageId(Long imageId)
    {
        return forumUserPostImageMapper.selectForumUserPostImageByImageId(imageId);
    }

    /**
     * 查询用户发帖图片列表
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 用户发帖图片
     */
    @Override
    public List<ForumUserPostImage> selectForumUserPostImageList(ForumUserPostImage forumUserPostImage)
    {
        return forumUserPostImageMapper.selectForumUserPostImageList(forumUserPostImage);
    }

    /**
     * 新增用户发帖图片
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 结果
     */
    @Override
    public int insertForumUserPostImage(ForumUserPostImage forumUserPostImage)
    {
        forumUserPostImage.setCreateTime(DateUtils.getNowDate());
        return forumUserPostImageMapper.insertForumUserPostImage(forumUserPostImage);
    }

    /**
     * 修改用户发帖图片
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 结果
     */
    @Override
    public int updateForumUserPostImage(ForumUserPostImage forumUserPostImage)
    {
        return forumUserPostImageMapper.updateForumUserPostImage(forumUserPostImage);
    }

    /**
     * 批量删除用户发帖图片
     *
     * @param imageIds 需要删除的用户发帖图片主键
     * @return 结果
     */
    @Override
    public int deleteForumUserPostImageByImageIds(Long[] imageIds)
    {
        return forumUserPostImageMapper.deleteForumUserPostImageByImageIds(imageIds);
    }

    /**
     * 删除用户发帖图片信息
     *
     * @param imageId 用户发帖图片主键
     * @return 结果
     */
    @Override
    public int deleteForumUserPostImageByImageId(Long imageId)
    {
        return forumUserPostImageMapper.deleteForumUserPostImageByImageId(imageId);
    }
}
