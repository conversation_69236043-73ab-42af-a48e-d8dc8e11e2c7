package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumLanguageMapper;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.service.IForumLanguageService;

/**
 * 语言类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumLanguageServiceImpl implements IForumLanguageService
{
    @Autowired
    private ForumLanguageMapper forumLanguageMapper;

    /**
     * 查询语言类型
     *
     * @param langId 语言类型主键
     * @return 语言类型
     */
    @Override
    public ForumLanguage selectForumLanguageByLangId(Long langId)
    {
        return forumLanguageMapper.selectForumLanguageByLangId(langId);
    }

    /**
     * 查询语言类型列表
     *
     * @param forumLanguage 语言类型
     * @return 语言类型
     */
    @Override
    public List<ForumLanguage> selectForumLanguageList(ForumLanguage forumLanguage)
    {
        return forumLanguageMapper.selectForumLanguageList(forumLanguage);
    }

    /**
     * 新增语言类型
     *
     * @param forumLanguage 语言类型
     * @return 结果
     */
    @Override
    public int insertForumLanguage(ForumLanguage forumLanguage)
    {
        forumLanguage.setCreateTime(DateUtils.getNowDate());
        return forumLanguageMapper.insertForumLanguage(forumLanguage);
    }

    /**
     * 修改语言类型
     *
     * @param forumLanguage 语言类型
     * @return 结果
     */
    @Override
    public int updateForumLanguage(ForumLanguage forumLanguage)
    {
        forumLanguage.setUpdateTime(DateUtils.getNowDate());
        return forumLanguageMapper.updateForumLanguage(forumLanguage);
    }

    /**
     * 批量删除语言类型
     *
     * @param langIds 需要删除的语言类型主键
     * @return 结果
     */
    @Override
    public int deleteForumLanguageByLangIds(Long[] langIds)
    {
        return forumLanguageMapper.deleteForumLanguageByLangIds(langIds);
    }

    /**
     * 删除语言类型信息
     *
     * @param langId 语言类型主键
     * @return 结果
     */
    @Override
    public int deleteForumLanguageByLangId(Long langId)
    {
        return forumLanguageMapper.deleteForumLanguageByLangId(langId);
    }
}
