package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumLanguage;

/**
 * 语言类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumLanguageService 
{
    /**
     * 查询语言类型
     * 
     * @param langId 语言类型主键
     * @return 语言类型
     */
    public ForumLanguage selectForumLanguageByLangId(Long langId);

    /**
     * 查询语言类型列表
     * 
     * @param forumLanguage 语言类型
     * @return 语言类型集合
     */
    public List<ForumLanguage> selectForumLanguageList(ForumLanguage forumLanguage);

    /**
     * 新增语言类型
     * 
     * @param forumLanguage 语言类型
     * @return 结果
     */
    public int insertForumLanguage(ForumLanguage forumLanguage);

    /**
     * 修改语言类型
     * 
     * @param forumLanguage 语言类型
     * @return 结果
     */
    public int updateForumLanguage(ForumLanguage forumLanguage);

    /**
     * 批量删除语言类型
     * 
     * @param langIds 需要删除的语言类型主键集合
     * @return 结果
     */
    public int deleteForumLanguageByLangIds(Long[] langIds);

    /**
     * 删除语言类型信息
     * 
     * @param langId 语言类型主键
     * @return 结果
     */
    public int deleteForumLanguageByLangId(Long langId);
}
