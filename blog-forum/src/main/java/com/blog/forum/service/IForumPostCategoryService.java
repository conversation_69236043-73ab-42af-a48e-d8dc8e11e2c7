package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumLanguage;
import com.blog.forum.domain.ForumPostCategory;

/**
 * 帖子分类Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumPostCategoryService
{
    /**
     * 查询帖子分类
     *
     * @param categoryId 帖子分类主键
     * @return 帖子分类
     */
    public ForumPostCategory selectForumPostCategoryByCategoryId(Long categoryId);

    /**
     * 查询帖子分类列表
     *
     * @param forumPostCategory 帖子分类
     * @return 帖子分类集合
     */
    public List<ForumPostCategory> selectForumPostCategoryList(ForumPostCategory forumPostCategory);

    /**
     * 新增帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    public int insertForumPostCategory(ForumPostCategory forumPostCategory);

    /**
     * 修改帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    public int updateForumPostCategory(ForumPostCategory forumPostCategory);

    /**
     * 批量删除帖子分类
     *
     * @param categoryIds 需要删除的帖子分类主键集合
     * @return 结果
     */
    public int deleteForumPostCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除帖子分类信息
     *
     * @param categoryId 帖子分类主键
     * @return 结果
     */
    public int deleteForumPostCategoryByCategoryId(Long categoryId);

    /**
     * 根据语言ID获取语言代码
     *
     * @param langId 语言ID
     * @return 语言代码
     */
    public String getLangCodeById(Long langId);

    /**
     * 根据语言代码获取语言ID
     *
     * @param langCode 语言代码
     * @return 语言ID
     */
    public Long getLangIdByCode(String langCode);

    /**
     * 查询帖子分类列表（支持多语言）
     *
     * @param forumPostCategory 帖子分类
     * @param langId 语言ID
     * @return 帖子分类集合
     */
    public List<ForumPostCategory> selectForumPostCategoryListWithI18n(ForumPostCategory forumPostCategory, Long langId);

    /**
     * 获取所有语言
     *
     * @return 语言列表
     */
    public List<ForumLanguage> getAllLanguages();
}
