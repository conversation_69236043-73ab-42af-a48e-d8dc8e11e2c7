package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumCarouselMapper;
import com.blog.forum.domain.ForumCarousel;
import com.blog.forum.service.IForumCarouselService;

/**
 * 轮播图Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumCarouselServiceImpl implements IForumCarouselService
{
    @Autowired
    private ForumCarouselMapper forumCarouselMapper;

    /**
     * 查询轮播图
     *
     * @param carouselId 轮播图主键
     * @return 轮播图
     */
    @Override
    public ForumCarousel selectForumCarouselByCarouselId(Long carouselId)
    {
        return forumCarouselMapper.selectForumCarouselByCarouselId(carouselId);
    }

    /**
     * 查询轮播图列表
     *
     * @param forumCarousel 轮播图
     * @return 轮播图
     */
    @Override
    public List<ForumCarousel> selectForumCarouselList(ForumCarousel forumCarousel)
    {
        return forumCarouselMapper.selectForumCarouselList(forumCarousel);
    }

    /**
     * 新增轮播图
     *
     * @param forumCarousel 轮播图
     * @return 结果
     */
    @Override
    public int insertForumCarousel(ForumCarousel forumCarousel)
    {
        forumCarousel.setCreateTime(DateUtils.getNowDate());
        return forumCarouselMapper.insertForumCarousel(forumCarousel);
    }

    /**
     * 修改轮播图
     *
     * @param forumCarousel 轮播图
     * @return 结果
     */
    @Override
    public int updateForumCarousel(ForumCarousel forumCarousel)
    {
        forumCarousel.setUpdateTime(DateUtils.getNowDate());
        return forumCarouselMapper.updateForumCarousel(forumCarousel);
    }

    /**
     * 批量删除轮播图
     *
     * @param carouselIds 需要删除的轮播图主键
     * @return 结果
     */
    @Override
    public int deleteForumCarouselByCarouselIds(Long[] carouselIds)
    {
        int result = 0;
        for (Long carouselId : carouselIds) {
            ForumCarousel carousel = forumCarouselMapper.selectForumCarouselByCarouselId(carouselId);
            if (carousel != null) {
                // 逻辑删除，将status设置为1（下线）
                carousel.setStatus("1");
                carousel.setUpdateTime(DateUtils.getNowDate());
                result += forumCarouselMapper.updateForumCarousel(carousel);
            }
        }
        return result;
    }

    /**
     * 删除轮播图信息
     *
     * @param carouselId 轮播图主键
     * @return 结果
     */
    @Override
    public int deleteForumCarouselByCarouselId(Long carouselId)
    {
        return forumCarouselMapper.deleteForumCarouselByCarouselId(carouselId);
    }
}
