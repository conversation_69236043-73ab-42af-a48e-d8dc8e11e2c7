package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumTagI18nMapper;
import com.blog.forum.domain.ForumTagI18n;
import com.blog.forum.service.IForumTagI18nService;

/**
 * 标签多语言内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class ForumTagI18nServiceImpl implements IForumTagI18nService
{
    @Autowired
    private ForumTagI18nMapper forumTagI18nMapper;

    /**
     * 查询标签多语言内容
     *
     * @param i18nId 标签多语言内容主键
     * @return 标签多语言内容
     */
    @Override
    public ForumTagI18n selectForumTagI18nByI18nId(Long i18nId)
    {
        return forumTagI18nMapper.selectForumTagI18nByI18nId(i18nId);
    }

    /**
     * 查询标签多语言内容列表
     *
     * @param forumTagI18n 标签多语言内容
     * @return 标签多语言内容
     */
    @Override
    public List<ForumTagI18n> selectForumTagI18nList(ForumTagI18n forumTagI18n)
    {
        return forumTagI18nMapper.selectForumTagI18nList(forumTagI18n);
    }

    /**
     * 新增标签多语言内容
     *
     * @param forumTagI18n 标签多语言内容
     * @return 结果
     */
    @Override
    public int insertForumTagI18n(ForumTagI18n forumTagI18n)
    {
        forumTagI18n.setCreateTime(DateUtils.getNowDate());
        return forumTagI18nMapper.insertForumTagI18n(forumTagI18n);
    }

    /**
     * 修改标签多语言内容
     *
     * @param forumTagI18n 标签多语言内容
     * @return 结果
     */
    @Override
    public int updateForumTagI18n(ForumTagI18n forumTagI18n)
    {
        forumTagI18n.setUpdateTime(DateUtils.getNowDate());
        return forumTagI18nMapper.updateForumTagI18n(forumTagI18n);
    }

    /**
     * 批量删除标签多语言内容
     *
     * @param i18nIds 需要删除的标签多语言内容主键
     * @return 结果
     */
    @Override
    public int deleteForumTagI18nByI18nIds(Long[] i18nIds)
    {
        return forumTagI18nMapper.deleteForumTagI18nByI18nIds(i18nIds);
    }

    /**
     * 删除标签多语言内容信息
     *
     * @param i18nId 标签多语言内容主键
     * @return 结果
     */
    @Override
    public int deleteForumTagI18nByI18nId(Long i18nId)
    {
        return forumTagI18nMapper.deleteForumTagI18nByI18nId(i18nId);
    }
}
