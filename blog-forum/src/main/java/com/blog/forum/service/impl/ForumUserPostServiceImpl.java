package com.blog.forum.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.*;
import com.blog.forum.domain.dto.UserPostCreateDTO;
import com.blog.forum.domain.dto.UserPostQueryDTO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.UserPostDetailVO;
import com.blog.forum.mapper.*;
import com.blog.forum.util.LangUtils;
import com.blog.system.mapper.ForumUserExtendMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.service.IForumUserPostService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户发帖Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumUserPostServiceImpl implements IForumUserPostService
{
    @Autowired
    private ForumUserPostMapper forumUserPostMapper;

    @Autowired
    private ForumUserPostImageMapper forumUserPostImageMapper;

    @Autowired
    private ForumUserExtendMapper forumUserExtendMapper;

    @Autowired
    private ForumPostCategoryMapper forumPostCategoryMapper;

    @Autowired
    private ForumTagMapper forumTagMapper;

    @Autowired
    private ForumPostTagMapper forumPostTagMapper;


    /**
     * 查询用户发帖
     *
     * @param userPostId 用户发帖主键
     * @return 用户发帖
     */
    @Override
    public ForumUserPost selectForumUserPostByUserPostId(Long userPostId)
    {
        return forumUserPostMapper.selectForumUserPostByUserPostId(userPostId);
    }

    /**
     * 查询用户发帖列表
     *
     * @param forumUserPost 用户发帖
     * @return 用户发帖
     */
    @Override
    public List<ForumUserPost> selectForumUserPostList(ForumUserPost forumUserPost)
    {
        return forumUserPostMapper.selectForumUserPostList(forumUserPost);
    }

    /**
     * 新增用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    @Override
    public int insertForumUserPost(ForumUserPost forumUserPost)
    {
        forumUserPost.setCreateTime(DateUtils.getNowDate());
        return forumUserPostMapper.insertForumUserPost(forumUserPost);
    }

    /**
     * 修改用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    @Override
    public int updateForumUserPost(ForumUserPost forumUserPost)
    {
        forumUserPost.setUpdateTime(DateUtils.getNowDate());
        return forumUserPostMapper.updateForumUserPost(forumUserPost);
    }

    /**
     * 批量删除用户发帖
     *
     * @param userPostIds 需要删除的用户发帖主键
     * @return 结果
     */
    @Override
    public int deleteForumUserPostByUserPostIds(Long[] userPostIds)
    {
        return forumUserPostMapper.deleteForumUserPostByUserPostIds(userPostIds);
    }

    /**
     * 删除用户发帖信息
     *
     * @param userPostId 用户发帖主键
     * @return 结果
     */
    @Override
    public int deleteForumUserPostByUserPostId(Long userPostId)
    {
        return forumUserPostMapper.deleteForumUserPostByUserPostId(userPostId);
    }

    @Override
    public List<UserPostDetailVO> selectUserPostList(UserPostQueryDTO query) {
        return forumUserPostMapper.selectUserPostList(query);
    }
    @Override
    public UserPostDetailVO selectUserPostDetail(Long userPostId) {
        // 增加浏览数
        forumUserPostMapper.updateViewCount(userPostId);

        UserPostDetailVO detail = forumUserPostMapper.selectUserPostDetail(userPostId);
        if (detail == null) {
            throw new ServiceException("帖子不存在或已被删除");
        }
        return detail;
    }
    @Override
    @Transactional
    public AjaxResult createUserPost(UserPostCreateDTO dto) {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUser().getUserId();
        // 验证分类存在
        if (forumPostCategoryMapper.selectCategoryById(dto.getCategoryId()) == null) {
            throw new ServiceException("选择的分类不存在或已禁用");
        }

        // 检查用户是否有发帖权限
        if (StrUtil.equals(loginUser.getForumUserExtend().getIsBannedPost(),"1")) {
            throw new ServiceException("您已被禁止发帖");
        }

        ForumUserPost post = new ForumUserPost();
        post.setUserId(userId);
        post.setContent(dto.getContent());
        post.setCategoryId(dto.getCategoryId()); // 设置分类ID

        // 创建帖子
        post.setUserId(userId);
        post.setContent(dto.getContent());
        post.setViewCount(0l);
        post.setCommentCount(0l);
        post.setLikeCount(0l);
        post.setStatus("0"); // 默认显示
        post.setCreateTime(DateUtils.getNowDate());
        post.setUpdateTime(DateUtils.getNowDate());

        forumUserPostMapper.insertUserPost(post);

        // 处理标签
        if (dto.getTagIds() != null && !dto.getTagIds().isEmpty()) {
            List<ForumPostTag> postTags = dto.getTagIds().stream()
                    .map(tagId -> {
                        ForumPostTag postTag = new ForumPostTag();
                        postTag.setPostId(post.getUserPostId());
                        postTag.setTagId(tagId);
                        postTag.setCreateTime(DateUtils.getNowDate());
                        return postTag;
                    })
                    .collect(Collectors.toList());

            forumPostTagMapper.batchInsert(postTags);
        }

        // 如果有图片，保存图片信息
        if (dto.getImages() != null && !dto.getImages().isEmpty()) {
            for (int i = 0 ; i < dto.getImages().size() ; i++){
                String fileUrl = dto.getImages().get(i);
//                String fileUrl = uploadFile(multipartFile); // 实现文件上传逻辑

                ForumUserPostImage image = new ForumUserPostImage();
                image.setUserPostId(post.getUserPostId());
                image.setImageUrl(fileUrl);
                image.setSort(Long.valueOf(1));
                image.setCreateTime(DateUtils.getNowDate());
                forumUserPostImageMapper.insertForumUserPostImage(image);
            }


        }
        ForumUserExtend forumUserExtend = forumUserExtendMapper.selectForumUserExtendByUserId(loginUser.getUserId());
        // 更新用户发帖数
        if (forumUserExtend != null){
            forumUserExtend.setPostCount(forumUserExtend.getPostCount() + 1);
            forumUserExtendMapper.updateForumUserExtend(forumUserExtend);
        }

        return AjaxResult.success("发帖成功", post.getUserPostId());
    }

//    private Long getUserId(SysUser loginUser, Long sysUserId) {
//        // 如果提供了sysUserId且当前用户是管理员，则使用该ID
//        if (sysUserId != null && SecurityUtils.isAdmin(loginUser.getUserId())) {
//            return forumUserPostMapper.selectUserIdBySysUserId(sysUserId);
//        }
//        // 否则使用当前登录用户的ID
//        return forumUserPostMapper.selectUserIdBySysUserId(loginUser.getUserId());
//    }

    private String uploadFile(MultipartFile file) {
        // 实现文件上传到OSS或本地
        // 返回文件URL
        return "uploaded_file_url";
    }

    @Override
    public List<CategoryVO> selectAvailableCategories() {
        List<CategoryVO> categoryVOList = forumPostCategoryMapper.selectCategoryList(LangUtils.getCurrentLangId());
        // 只返回status=0的分类
        return categoryVOList.stream()
                .filter(category -> "0".equals(category.getStatus()))
                .collect(Collectors.toList());
    }
    @Override
    public List<ForumTag> selectAvailableTags() {
        ForumTag query = new ForumTag();
        query.setStatus("0"); // 只查询status=0的标签
        return forumTagMapper.selectTagList(query, LangUtils.getCurrentLangId());
    }

}
