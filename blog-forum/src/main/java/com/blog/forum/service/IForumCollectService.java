package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumCollect;
import com.blog.forum.domain.dto.CollectCheckDTO;
import com.blog.forum.domain.vo.CollectCheckVO;

/**
 * 收藏Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumCollectService
{
    /**
     * 查询收藏
     *
     * @param collectId 收藏主键
     * @return 收藏
     */
    public ForumCollect selectForumCollectByCollectId(Long collectId);

    /**
     * 查询收藏列表
     *
     * @param forumCollect 收藏
     * @return 收藏集合
     */
    public List<ForumCollect> selectForumCollectList(ForumCollect forumCollect);

    /**
     * 新增收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    public int insertForumCollect(ForumCollect forumCollect);

    /**
     * 修改收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    public int updateForumCollect(ForumCollect forumCollect);

    /**
     * 批量删除收藏
     *
     * @param collectIds 需要删除的收藏主键集合
     * @return 结果
     */
    public int deleteForumCollectByCollectIds(Long[] collectIds);

    /**
     * 删除收藏信息
     *
     * @param collectId 收藏主键
     * @return 结果
     */
    public int deleteForumCollectByCollectId(Long collectId);

    /**
     * 检查当前用户是否收藏指定内容
     */
    CollectCheckVO checkIfCollected(CollectCheckDTO checkDTO);

    /**
     * 检查指定用户是否收藏指定内容
     */
    CollectCheckVO checkIfCollected(CollectCheckDTO checkDTO, Long userId);

    boolean isCollected(String contentType, Long contentId, Long userId);
}
