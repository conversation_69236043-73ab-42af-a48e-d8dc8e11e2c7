package com.blog.forum.service.impl;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.common.exception.ServiceException;
import com.blog.forum.domain.ForumCollect;
import com.blog.forum.domain.ForumLike;
import com.blog.forum.domain.dto.CollectDTO;
import com.blog.forum.domain.dto.LikeDTO;
import com.blog.forum.domain.dto.ShareDTO;
import com.blog.forum.domain.vo.MyCollectVO;
import com.blog.forum.domain.vo.MyCommentVO;
import com.blog.forum.domain.vo.MyLikeVO;
import com.blog.forum.domain.vo.UserStatsVO;
import com.blog.forum.mapper.*;
import com.blog.forum.service.IForumInteractionService;
import com.blog.forum.util.LangUtils;
import com.blog.system.mapper.ForumUserExtendMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;

@Service
public class ForumInteractionServiceImpl implements IForumInteractionService {
    @Autowired
    private ForumLikeMapper likeMapper;
    @Autowired
    private ForumCollectMapper collectMapper;
    @Autowired
    private ForumPostMapper postMapper;
    @Autowired
    private ForumUserPostMapper userPostMapper;
    @Autowired
    private ForumCommentMapper commentMapper;

    @Autowired
    private ForumUserExtendMapper forumUserExtendMapper;

    @Override
    @Transactional
    public AjaxResult like(LikeDTO likeDTO, Long userId) {
        String contentType = likeDTO.getContentType();
        Long contentId = likeDTO.getContentId();
        boolean isLike = likeDTO.getIsLike();
        // 检查内容是否存在
        if (!checkContentExists(contentType, contentId)) {
            throw new ServiceException("点赞内容不存在");
        }
        boolean exists = likeMapper.checkExists(contentType, contentId, userId);
        if (isLike) {
            if (exists) {
                throw new ServiceException("您已经点过赞了");
            }

            // 添加点赞记录
            ForumLike like = new ForumLike();
            like.setContentType(contentType);
            like.setContentId(contentId);
            like.setUserId(userId);
            like.setCreateTime(new Date());
            likeMapper.insertLike(like);

            // 更新点赞数
            updateLikeCount(contentType, contentId, 1);

            // 如果是帖子，更新发帖用户的获赞数
            if ("0".equals(contentType)) {
                // 获取帖子信息
                Long authorId = postMapper.selectPostAuthorId(contentId);
                if (authorId != null && !authorId.equals(userId)) {
                    // 更新发帖用户的获赞数
                    ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(authorId);
                    if (userExtend != null) {
                        userExtend.setLikeCount(userExtend.getLikeCount() + 1);
                        forumUserExtendMapper.updateForumUserExtend(userExtend);
                    }
                }
            }
        } else {
            if (!exists) {
                throw new ServiceException("您还没有点赞");
            }

            // 删除点赞记录
            likeMapper.deleteLike(contentType, contentId, userId);

            // 更新点赞数
            updateLikeCount(contentType, contentId, -1);

            // 如果是帖子，更新发帖用户的获赞数
            if ("0".equals(contentType)) {
                // 获取帖子信息
                Long authorId = postMapper.selectPostAuthorId(contentId);
                if (authorId != null && !authorId.equals(userId)) {
                    // 更新发帖用户的获赞数
                    ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(authorId);
                    if (userExtend != null && userExtend.getLikeCount() > 0) {
                        userExtend.setLikeCount(userExtend.getLikeCount() - 1);
                        forumUserExtendMapper.updateForumUserExtend(userExtend);
                    }
                }
            }
        }

        int likeCount = likeMapper.selectLikeCount(contentType, contentId);
        return AjaxResult.success("操作成功").put("likeCount", likeCount);
    }
    @Override
    @Transactional
    public AjaxResult share(ShareDTO shareDTO, Long userId) {
        Long postId = shareDTO.getPostId();

        // 检查帖子是否存在
        if (postMapper.selectPostById(postId, LangUtils.getCurrentLangId()) == null) {
            throw new ServiceException("帖子不存在");
        }

        // 更新帖子分享数
        postMapper.incrementShareCount(postId);

        // 更新用户分享数
        ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(userId);
        if (userExtend != null) {
            userExtend.setShareCount(userExtend.getShareCount() + 1);
            forumUserExtendMapper.updateForumUserExtend(userExtend);
        }

        return AjaxResult.success("分享成功");
    }
    @Override
    @Transactional
    public AjaxResult collect(CollectDTO collectDTO, Long userId) {
        String contentType = collectDTO.getContentType();
        Long contentId = collectDTO.getContentId();
        boolean isCollect = collectDTO.getIsCollect();
        // 检查内容是否存在
        if (!checkContentExists(contentType, contentId)) {
            throw new ServiceException("收藏内容不存在");
        }
        boolean exists = collectMapper.checkExists(contentType, contentId, userId);
        if (isCollect) {
            if (exists) {
                throw new ServiceException("您已经收藏过了");
            }

            // 添加收藏记录
            ForumCollect collect = new ForumCollect();
            collect.setContentType(contentType);
            collect.setContentId(contentId);
            collect.setUserId(userId);
            collect.setCreateTime(new Date());
            collectMapper.insertCollect(collect);

            // 更新收藏数
            updateCollectCount(contentType, contentId, 1);

            // 更新用户收藏数
            ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(userId);
            if (userExtend != null) {
                userExtend.setCollectCount(userExtend.getCollectCount() + 1);
                forumUserExtendMapper.updateForumUserExtend(userExtend);
            }
        } else {
            if (!exists) {
                throw new ServiceException("您还没有收藏");
            }

            // 删除收藏记录
            collectMapper.deleteCollect(contentType, contentId, userId);

            // 更新收藏数
            updateCollectCount(contentType, contentId, -1);

            // 更新用户收藏数
            ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(userId);
            if (userExtend != null && userExtend.getCollectCount() > 0) {
                userExtend.setCollectCount(userExtend.getCollectCount() - 1);
                forumUserExtendMapper.updateForumUserExtend(userExtend);
            }
        }

        int collectCount = collectMapper.selectCollectCount(contentType, contentId);
        return AjaxResult.success("操作成功").put("collectCount", collectCount);
    }
    private boolean checkContentExists(String contentType, Long contentId) {
        Long currentLangId = LangUtils.getCurrentLangId();
        switch (contentType) {
            case "0":
                return postMapper.selectPostById(contentId,currentLangId) != null;
//            case "1": return userPostMapper.selectUserPostById(contentId) != null;
            case "2":
                return commentMapper.selectCommentById(contentId) != null;
            default: return false;
        }
    }
    private void updateLikeCount(String contentType, Long contentId, int increment) {
        switch (contentType) {
            case "0": postMapper.incrementLikeCount(contentId, increment); break;
//            case "1": userPostMapper.incrementLikeCount(contentId, increment); break;
            case "2": commentMapper.incrementLikeCount(contentId, increment); break;
        }
    }
    private void updateCollectCount(String contentType, Long contentId, int increment) {
        switch (contentType) {
            case "0": postMapper.incrementCollectCount(contentId, increment); break;
//            case "1": userPostMapper.incrementCollectCount(contentId, increment); break;
        }
    }

    @Override
    public List<MyCollectVO> getMyCollectList(Long userId) {
        Long langId = LangUtils.getCurrentLangId();
        List<MyCollectVO> list = collectMapper.selectUserCollectList(userId, langId);
        return list;
    }
    @Override
    public List<MyLikeVO> getMyLikeList(Long userId) {
        Long langId = LangUtils.getCurrentLangId();
        List<MyLikeVO> list = likeMapper.selectUserLikeList(userId, langId);
        return list;
    }
    @Override
    public List<MyCommentVO> getMyCommentList(Long userId) {
        Long langId = LangUtils.getCurrentLangId();
        List<MyCommentVO> list = commentMapper.selectUserCommentList(userId, langId);
        return list;
    }
    @Override
    public UserStatsVO getUserStats(Long userId) {
        UserStatsVO stats = new UserStatsVO();
        stats.setCollectCount(collectMapper.selectUserCollectCount(userId));
        stats.setLikeCount(likeMapper.selectUserLikeCount(userId));
        stats.setCommentCount(commentMapper.selectUserCommentCount(userId));

        // 从用户扩展表获取最新的统计数据
//        ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(userId);
//        if (userExtend != null) {
//            stats.setCollectCount(userExtend.getCollectCount().intValue());
//            stats.setLikeCount(userExtend.getLikeCount().intValue());
//            stats.setCommentCount(userExtend.getCommentCount().intValue());
//        }

        return stats;
    }
}
