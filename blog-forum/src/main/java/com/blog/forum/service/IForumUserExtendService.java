package com.blog.forum.service;

import java.util.List;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.common.core.domain.entity.SysUser;
import com.blog.forum.domain.dto.UserInfoUpdateDTO;
import com.blog.system.domain.dto.AdminUserQueryDTO;
import com.blog.system.domain.vo.AdminUserVO;

/**
 * 用户管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumUserExtendService
{
    /**
     * 查询用户管理
     *
     * @param userId 用户管理主键
     * @return 用户管理
     */
    public ForumUserExtend selectForumUserExtendByUserId(Long userId);

    /**
     * 查询用户管理列表
     *
     * @param forumUserExtend 用户管理
     * @return 用户管理集合
     */
    public List<ForumUserExtend> selectForumUserExtendList(ForumUserExtend forumUserExtend);

    /**
     * 新增用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    public int insertForumUserExtend(ForumUserExtend forumUserExtend);

    /**
     * 修改用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    public int updateForumUserExtend(ForumUserExtend forumUserExtend);

    /**
     * 批量删除用户管理
     *
     * @param userIds 需要删除的用户管理主键集合
     * @return 结果
     */
    public int deleteForumUserExtendByUserIds(Long[] userIds);

    /**
     * 删除用户管理信息
     *
     * @param userId 用户管理主键
     * @return 结果
     */
    public int deleteForumUserExtendByUserId(Long userId);

    /**
     * 更新当前登录用户信息
     */
    AjaxResult updateUserInfo(UserInfoUpdateDTO updateDTO);

    /**
     * 查询后台用户列表
     *
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    List<AdminUserVO> selectAdminUserList(AdminUserQueryDTO queryDTO);

    /**
     * 更新用户禁止发帖状态
     *
     * @param userId 用户ID
     * @param isBannedPost 是否禁止发帖（0否 1是）
     * @return 结果
     */
    AjaxResult updateUserBannedPost(Long userId, String isBannedPost);

    /**
     * 更新用户状态
     *
     * @param sysUserId 系统用户ID
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    AjaxResult updateUserStatus(Long sysUserId, String status);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 结果
     */
    AjaxResult resetUserPassword(Long userId, String newPassword);

}
