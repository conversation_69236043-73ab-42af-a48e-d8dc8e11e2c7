package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumTag;

/**
 * 帖子标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IForumTagService 
{
    /**
     * 查询帖子标签
     * 
     * @param tagId 帖子标签主键
     * @return 帖子标签
     */
    public ForumTag selectForumTagByTagId(Long tagId);

    /**
     * 查询帖子标签列表
     * 
     * @param forumTag 帖子标签
     * @return 帖子标签集合
     */
    public List<ForumTag> selectForumTagList(ForumTag forumTag);

    /**
     * 新增帖子标签
     * 
     * @param forumTag 帖子标签
     * @return 结果
     */
    public int insertForumTag(ForumTag forumTag);

    /**
     * 修改帖子标签
     * 
     * @param forumTag 帖子标签
     * @return 结果
     */
    public int updateForumTag(ForumTag forumTag);

    /**
     * 批量删除帖子标签
     * 
     * @param tagIds 需要删除的帖子标签主键集合
     * @return 结果
     */
    public int deleteForumTagByTagIds(Long[] tagIds);

    /**
     * 删除帖子标签信息
     * 
     * @param tagId 帖子标签主键
     * @return 结果
     */
    public int deleteForumTagByTagId(Long tagId);
}
