package com.blog.forum.service.impl;

import java.util.List;
import java.util.ArrayList;

import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.*;
import com.blog.forum.domain.dto.PostListQueryDTO;
import com.blog.forum.domain.dto.PostQueryDTO;
import com.blog.forum.domain.vo.CarouselVO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.PostVO;
import com.blog.forum.domain.vo.TagVO;
import com.blog.forum.mapper.*;
import com.blog.forum.service.FileService;
import com.blog.forum.util.LangUtils;
import com.blog.system.mapper.ForumUserExtendMapper;
import com.blog.system.mapper.SysOssMapper;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.blog.forum.service.IForumPostService;

import static com.blog.common.utils.PageUtils.startPage;

/**
 * 帖子Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class ForumPostServiceImpl implements IForumPostService
{
    @Autowired
    private ForumPostMapper forumPostMapper;


    @Autowired
    private ForumPostMapper postMapper;
    @Autowired
    private ForumPostCategoryMapper categoryMapper;

    @Autowired
    private ForumUserExtendMapper forumUserExtendMapper;
    @Autowired
    private ForumCarouselMapper carouselMapper;
    @Autowired
    private ForumPostTagMapper postTagMapper;

    @Autowired
    private ForumPostAttachmentMapper attachmentMapper;

    /**
     * 查询帖子
     *
     * @param postId 帖子主键
     * @return 帖子
     */
    @Override
    public ForumPost selectForumPostByPostId(Long postId)
    {
        ForumPost post = forumPostMapper.selectForumPostByPostId(postId);
        if (post != null) {
            // 查询帖子关联的标签
            List<Long> tagIds = postTagMapper.selectTagIdsByPostId(postId);
            post.setTagIds(tagIds);

            // 查询帖子关联的附件
            ForumPostAttachment attachmentCondition = new ForumPostAttachment();
            attachmentCondition.setPostId(postId);
            attachmentCondition.setContentType("1");
            List<ForumPostAttachment> attachments = attachmentMapper.selectForumPostAttachmentList(attachmentCondition);
            post.setAttachments(attachments);
        }
        return post;
    }

    /**
     * 查询帖子列表
     *
     * @param forumPost 帖子
     * @return 帖子
     */
    @Override
    public List<ForumPost> selectForumPostList(ForumPost forumPost)
    {
        return forumPostMapper.selectForumPostList(forumPost);
    }

    /**
     * 新增帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertForumPost(ForumPost forumPost)
    {
        forumPost.setCreateTime(DateUtils.getNowDate());

        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        if (userId == null && loginUser != null) {
            userId = loginUser.getUser().getUserId();
        }
        forumPost.setStatus("0");
        forumPost.setPostStatus("1");
        forumPost.setCreateBy(userId.toString());
        forumPost.setSysUserId(userId);
        forumPost.setLangId(LangUtils.getCurrentLangId());
        int rows = forumPostMapper.insertForumPost(forumPost);
        // 处理附件
        if (rows > 0 && forumPost.getAttachments() != null && !forumPost.getAttachments().isEmpty()) {
            for (ForumPostAttachment attachment : forumPost.getAttachments()) {
                attachment.setPostId(forumPost.getPostId());
                attachment.setContentType("1"); // 1表示附件
                attachment.setCreateTime(DateUtils.getNowDate());
            }
            attachmentMapper.batchInsert(forumPost.getAttachments());
        }

        return rows;
    }

    /**
     * 修改帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateForumPost(ForumPost forumPost)
    {
        // 打印调试日志

        forumPost.setUpdateTime(DateUtils.getNowDate());
        int rows = forumPostMapper.updateForumPost(forumPost);
        forumPost.setPostStatus("0");
        forumPost.setStatus("1");

        // 先删除原有标签关联
        int deleteCount = postTagMapper.deleteByPostId(forumPost.getPostId());
        // 处理标签关联
        if (rows > 0 && forumPost.getTagIds() != null && !forumPost.getTagIds().isEmpty()) {

            // 添加新的标签关联
            List<ForumPostTag> postTags = new ArrayList<>();
            for (Long tagId : forumPost.getTagIds()) {
                ForumPostTag postTag = new ForumPostTag();
                postTag.setPostId(forumPost.getPostId());
                postTag.setTagId(tagId);
                postTag.setCreateTime(DateUtils.getNowDate());
                postTags.add(postTag);
            }

            if (!postTags.isEmpty()) {
                try {
                    postTagMapper.batchInsert(postTags);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 处理附件
        if (rows > 0 && forumPost.getAttachments() != null) {
            // 先删除原有附件
            ForumPostAttachment deleteCondition = new ForumPostAttachment();
            deleteCondition.setPostId(forumPost.getPostId());
            deleteCondition.setContentType("1");
            List<ForumPostAttachment> existingAttachments = attachmentMapper.selectForumPostAttachmentList(deleteCondition);
            if (!existingAttachments.isEmpty()) {
                Long[] attachmentIds = existingAttachments.stream()
                    .map(ForumPostAttachment::getAttachmentId)
                    .toArray(Long[]::new);
                attachmentMapper.deleteForumPostAttachmentByAttachmentIds(attachmentIds);
            }

            // 添加新的附件
            if (!forumPost.getAttachments().isEmpty()) {
                for (ForumPostAttachment attachment : forumPost.getAttachments()) {
                    attachment.setPostId(forumPost.getPostId());
                    attachment.setContentType("1"); // 1表示附件
                    attachment.setCreateTime(DateUtils.getNowDate());
                }
                attachmentMapper.batchInsert(forumPost.getAttachments());
            }
        }

        return rows;
    }

    /**
     * 批量删除帖子
     *
     * @param postIds 需要删除的帖子主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteForumPostByPostIds(Long[] postIds)
    {
        int result = 0;

        for (Long postId : postIds) {
            // 查询帖子信息
            ForumPost post = forumPostMapper.selectForumPostByPostId(postId);
            if (post == null) {
                continue;
            }

            // 逻辑删除，将status设置为1（下线）
            post.setStatus("1");
            result += forumPostMapper.updateForumPost(post);

            // 如果是用户帖子，减少用户发帖数量
            if ("1".equals(post.getIsUserPost()) && post.getSysUserId() != null) {
                // 查询用户扩展信息
                ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(post.getSysUserId());
                if (userExtend != null && userExtend.getPostCount() > 0) {
                    // 减少发帖数量
                    userExtend.setPostCount(userExtend.getPostCount() - 1);
                    forumUserExtendMapper.updateForumUserExtend(userExtend);
                }
            }
        }

        return result;
    }



    @Override
    public List<PostVO> selectPostList(PostQueryDTO query) {
        Long langId = LangUtils.getCurrentLangId();
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        return postMapper.selectPostList(query, langId);
    }
    @Override
    public PostVO selectPostById(Long postId) {
        Long langId = LangUtils.getCurrentLangId();
        PostVO post = postMapper.selectPostById(postId, langId);
        if (post != null) {
            postMapper.incrementViewCount(postId);
        }
        return post;
    }
    @Override
    public List<CategoryVO> selectCategoryList() {
        Long langId = LangUtils.getCurrentLangId();
        return categoryMapper.selectCategoryList(langId);
    }
    @Override
    public List<CarouselVO> selectCarouselList() {
        return carouselMapper.selectCarouselList();
    }

    /**
     * 查询帖子列表（高级查询）
     *
     * @param query 查询条件
     * @return 帖子列表
     */
    @Override
    public List<PostDetailVO> selectPostListAdvanced(PostListQueryDTO query) {
        Long langId = LangUtils.getCurrentLangId();
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PostDetailVO> postList = forumPostMapper.selectPostListAdvanced(query, langId);

        // 查询帖子关联的标签
        if (postList != null && !postList.isEmpty()) {
            for (PostDetailVO post : postList) {
                List<TagVO> tags = postTagMapper.selectTagsByPostId(langId, post.getPostId());
                post.setTags(tags);
            }
        }

        return postList;
    }

    /**
     * 设置帖子置顶状态
     *
     * @param postId 帖子ID
     * @param isTop 是否置顶（0否 1是）
     * @return 结果
     */
    @Override
    public int updatePostTopStatus(Long postId, String isTop) {
        return forumPostMapper.updatePostTopStatus(postId, isTop);
    }
}
