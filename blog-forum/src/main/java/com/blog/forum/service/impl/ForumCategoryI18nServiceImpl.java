package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumCategoryI18nMapper;
import com.blog.forum.domain.ForumCategoryI18n;
import com.blog.forum.service.IForumCategoryI18nService;

/**
 * 分类多语言内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class ForumCategoryI18nServiceImpl implements IForumCategoryI18nService
{
    @Autowired
    private ForumCategoryI18nMapper forumCategoryI18nMapper;

    /**
     * 查询分类多语言内容
     *
     * @param i18nId 分类多语言内容主键
     * @return 分类多语言内容
     */
    @Override
    public ForumCategoryI18n selectForumCategoryI18nByI18nId(Long i18nId)
    {
        return forumCategoryI18nMapper.selectForumCategoryI18nByI18nId(i18nId);
    }

    /**
     * 查询分类多语言内容列表
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 分类多语言内容
     */
    @Override
    public List<ForumCategoryI18n> selectForumCategoryI18nList(ForumCategoryI18n forumCategoryI18n)
    {
        return forumCategoryI18nMapper.selectForumCategoryI18nList(forumCategoryI18n);
    }

    /**
     * 新增分类多语言内容
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    @Override
    public int insertForumCategoryI18n(ForumCategoryI18n forumCategoryI18n)
    {
        forumCategoryI18n.setCreateTime(DateUtils.getNowDate());
        return forumCategoryI18nMapper.insertForumCategoryI18n(forumCategoryI18n);
    }

    /**
     * 修改分类多语言内容
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    @Override
    public int updateForumCategoryI18n(ForumCategoryI18n forumCategoryI18n)
    {
        forumCategoryI18n.setUpdateTime(DateUtils.getNowDate());
        return forumCategoryI18nMapper.updateForumCategoryI18n(forumCategoryI18n);
    }

    /**
     * 批量删除分类多语言内容
     *
     * @param i18nIds 需要删除的分类多语言内容主键
     * @return 结果
     */
    @Override
    public int deleteForumCategoryI18nByI18nIds(Long[] i18nIds)
    {
        return forumCategoryI18nMapper.deleteForumCategoryI18nByI18nIds(i18nIds);
    }

    /**
     * 删除分类多语言内容信息
     *
     * @param i18nId 分类多语言内容主键
     * @return 结果
     */
    @Override
    public int deleteForumCategoryI18nByI18nId(Long i18nId)
    {
        return forumCategoryI18nMapper.deleteForumCategoryI18nByI18nId(i18nId);
    }
}
