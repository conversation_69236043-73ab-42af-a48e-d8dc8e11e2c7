package com.blog.forum.service;

import java.util.List;

import com.blog.common.core.domain.AjaxResult;
import com.blog.forum.domain.ForumTag;
import com.blog.forum.domain.ForumUserPost;
import com.blog.forum.domain.dto.UserPostCreateDTO;
import com.blog.forum.domain.dto.UserPostQueryDTO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.UserPostDetailVO;

/**
 * 用户发帖Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumUserPostService
{
    /**
     * 查询用户发帖
     *
     * @param userPostId 用户发帖主键
     * @return 用户发帖
     */
    public ForumUserPost selectForumUserPostByUserPostId(Long userPostId);

    /**
     * 查询用户发帖列表
     *
     * @param forumUserPost 用户发帖
     * @return 用户发帖集合
     */
    public List<ForumUserPost> selectForumUserPostList(ForumUserPost forumUserPost);

    /**
     * 新增用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    public int insertForumUserPost(ForumUserPost forumUserPost);

    /**
     * 修改用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    public int updateForumUserPost(ForumUserPost forumUserPost);

    /**
     * 批量删除用户发帖
     *
     * @param userPostIds 需要删除的用户发帖主键集合
     * @return 结果
     */
    public int deleteForumUserPostByUserPostIds(Long[] userPostIds);

    /**
     * 删除用户发帖信息
     *
     * @param userPostId 用户发帖主键
     * @return 结果
     */
    public int deleteForumUserPostByUserPostId(Long userPostId);

    /**
     * 获取用户发帖列表(分页)
     */
    List<UserPostDetailVO> selectUserPostList(UserPostQueryDTO query);

    /**
     * 获取用户发帖详情
     */
    UserPostDetailVO selectUserPostDetail(Long userPostId);

    /**
     * 发表用户帖子(需登录)
     */
    AjaxResult createUserPost(UserPostCreateDTO dto);

    // 原有方法不变，新增一个方法用于获取分类和标签数据：
    List<CategoryVO> selectAvailableCategories();

    List<ForumTag> selectAvailableTags();

}
