
package com.blog.forum.service.impl;

import java.util.Date;
import java.util.List;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SensitiveWordUtils;
import com.blog.forum.domain.dto.AdminCommentQueryDTO;
import com.blog.forum.domain.dto.CommentDTO;
import com.blog.forum.domain.vo.CommentDetailVO;
import com.blog.forum.mapper.ForumLikeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumCommentMapper;
import com.blog.forum.domain.ForumComment;
import com.blog.forum.service.IForumCommentService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumCommentServiceImpl implements IForumCommentService
{
    @Autowired
    private ForumCommentMapper forumCommentMapper;

    @Autowired
    private ForumLikeMapper forumLikeMapper;

    /**
     * 查询评论
     *
     * @param commentId 评论主键
     * @return 评论
     */
    @Override
    public ForumComment selectForumCommentByCommentId(Long commentId)
    {
        return forumCommentMapper.selectForumCommentByCommentId(commentId);
    }

    /**
     * 查询评论列表
     *
     * @param forumComment 评论
     * @return 评论
     */
    @Override
    public List<ForumComment> selectForumCommentList(ForumComment forumComment)
    {
        return forumCommentMapper.selectForumCommentList(forumComment);
    }

    /**
     * 新增评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    @Override
    public int insertForumComment(ForumComment forumComment)
    {
        forumComment.setCreateTime(DateUtils.getNowDate());
        return forumCommentMapper.insertForumComment(forumComment);
    }

    /**
     * 修改评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    @Override
    public int updateForumComment(ForumComment forumComment)
    {
        forumComment.setUpdateTime(DateUtils.getNowDate());
        return forumCommentMapper.updateForumComment(forumComment);
    }

    /**
     * 批量删除评论
     *
     * @param commentIds 需要删除的评论主键
     * @return 结果
     */
    @Override
    public int deleteForumCommentByCommentIds(Long[] commentIds)
    {
        return forumCommentMapper.deleteForumCommentByCommentIds(commentIds);
    }

    /**
     * 删除评论信息
     *
     * @param commentId 评论主键
     * @return 结果
     */
    @Override
    public int deleteForumCommentByCommentId(Long commentId)
    {
        return forumCommentMapper.deleteForumCommentByCommentId(commentId);
    }

    @Override
    public List<CommentDetailVO> getCommentList(String contentType, Long contentId, Long userId) {
        List<CommentDetailVO> commentDetailVOList = forumCommentMapper.selectCommentList(contentType, contentId, userId);
        
        // 过滤评论中的敏感词
        if (commentDetailVOList != null && !commentDetailVOList.isEmpty()) {
            for (CommentDetailVO comment : commentDetailVOList) {
                // 过滤评论内容中的敏感词
                if (comment.getContent() != null) {
                    comment.setContent(SensitiveWordUtils.filter(comment.getContent()));
                }
                
                // 获取回复列表
                List<CommentDetailVO> detailVOS = forumCommentMapper.selectReplies("0", contentId, userId, comment.getCommentId());
                
                // 过滤回复中的敏感词
                if (detailVOS != null && !detailVOS.isEmpty()) {
                    for (CommentDetailVO reply : detailVOS) {
                        if (reply.getContent() != null) {
                            reply.setContent(SensitiveWordUtils.filter(reply.getContent()));
                        }
                    }
                }
                
                comment.setReplies(detailVOS);
            }
        }
        
        return commentDetailVOList;
    }
    
    @Override
    @Transactional
    public AjaxResult addComment(CommentDTO commentDTO, Long userId) {
        // 过滤评论内容中的敏感词
        String filteredContent = SensitiveWordUtils.filter(commentDTO.getContent());
        
        ForumComment comment = new ForumComment();
        comment.setContentType(commentDTO.getContentType());
        comment.setContentId(commentDTO.getContentId());
        comment.setParentId(commentDTO.getParentId());
        comment.setContent(filteredContent); // 使用过滤后的内容
        comment.setUserId(userId);
        comment.setLikeCount(0L);
        comment.setStatus("0");
        comment.setCreateTime(new Date());
        comment.setUpdateTime(new Date());

        forumCommentMapper.insertComment(comment);
        return AjaxResult.success("评论成功");
    }
    
    @Override
    @Transactional
    public AjaxResult deleteComment(Long commentId, Long userId) {
        ForumComment comment = forumCommentMapper.selectCommentById(commentId);
        if (comment == null) {
            return AjaxResult.error("评论不存在");
        }
        if (!comment.getUserId().equals(userId)) {
            return AjaxResult.error("只能删除自己的评论");
        }

        int result = forumCommentMapper.updateCommentStatus(commentId, userId, "1");
        if (result > 0) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败");
        }
    }
    
    @Override
    @Transactional
    public AjaxResult likeComment(Long commentId, Long userId, boolean isLike) {
//        // 检查评论是否存在
//        ForumComment comment = commentMapper.selectCommentById(commentId);
//        if (comment == null || "1".equals(comment.getStatus())) {
//            throw new ServiceException("评论不存在或已删除");
//        }
//
//        // 检查是否已经点赞
//        boolean exists = forumLikeMapper.checkExists("2", commentId, userId);
//
//        if (isLike) {
//            if (exists) {
//                throw new ServiceException("您已经点赞过该评论");
//            }
//
//            // 添加点赞记录
//            ForumLike like = new ForumLike();
//            like.setContentType("2");
//            like.setContentId(commentId);
//            like.setUserId(userId);
//            like.setCreateTime(new Date());
//            forumLikeMapper.insertLike(like);
//
//            // 增加点赞数
//            forumCommentMapper.incrementLikeCount(commentId, 1);
//        } else {
//            if (!exists) {
//                throw new ServiceException("您还未点赞该评论");
//            }
//
//            // 删除点赞记录
//            forumLikeMapper.deleteLike("2", commentId, userId);
//
//            // 减少点赞数
//            forumCommentMapper.incrementLikeCount(commentId, -1);
//        }

        return AjaxResult.success(isLike ? "点赞成功" : "取消点赞成功");
    }

    /**
     * 查询后台评论列表
     *
     * @param queryDTO 查询条件
     * @return 评论列表
     */
    @Override
    public List<CommentDetailVO> selectAdminCommentList(AdminCommentQueryDTO queryDTO) {
        List<CommentDetailVO> commentList = forumCommentMapper.selectAdminCommentList(queryDTO);
        
        // 过滤评论中的敏感词（管理员查看时也显示过滤后的内容，方便预览效果）
        if (commentList != null && !commentList.isEmpty()) {
            for (CommentDetailVO comment : commentList) {
                if (comment.getContent() != null) {
                    comment.setContent(SensitiveWordUtils.filter(comment.getContent()));
                }
            }
        }
        
        return commentList;
    }

    /**
     * 查询评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    @Override
    public List<CommentDetailVO> selectCommentReplies(Long parentId) {
        List<CommentDetailVO> replyList = forumCommentMapper.selectCommentReplies(parentId);
        
        // 过滤回复中的敏感词
        if (replyList != null && !replyList.isEmpty()) {
            for (CommentDetailVO reply : replyList) {
                if (reply.getContent() != null) {
                    reply.setContent(SensitiveWordUtils.filter(reply.getContent()));
                }
            }
        }
        
        return replyList;
    }
}
