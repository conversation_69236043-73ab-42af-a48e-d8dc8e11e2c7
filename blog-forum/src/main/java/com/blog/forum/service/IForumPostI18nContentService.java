package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumPostI18nContent;

/**
 * 多语言帖子内容Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumPostI18nContentService 
{
    /**
     * 查询多语言帖子内容
     * 
     * @param contentId 多语言帖子内容主键
     * @return 多语言帖子内容
     */
    public ForumPostI18nContent selectForumPostI18nContentByContentId(Long contentId);

    /**
     * 查询多语言帖子内容列表
     * 
     * @param forumPostI18nContent 多语言帖子内容
     * @return 多语言帖子内容集合
     */
    public List<ForumPostI18nContent> selectForumPostI18nContentList(ForumPostI18nContent forumPostI18nContent);

    /**
     * 新增多语言帖子内容
     * 
     * @param forumPostI18nContent 多语言帖子内容
     * @return 结果
     */
    public int insertForumPostI18nContent(ForumPostI18nContent forumPostI18nContent);

    /**
     * 修改多语言帖子内容
     * 
     * @param forumPostI18nContent 多语言帖子内容
     * @return 结果
     */
    public int updateForumPostI18nContent(ForumPostI18nContent forumPostI18nContent);

    /**
     * 批量删除多语言帖子内容
     * 
     * @param contentIds 需要删除的多语言帖子内容主键集合
     * @return 结果
     */
    public int deleteForumPostI18nContentByContentIds(Long[] contentIds);

    /**
     * 删除多语言帖子内容信息
     * 
     * @param contentId 多语言帖子内容主键
     * @return 结果
     */
    public int deleteForumPostI18nContentByContentId(Long contentId);
}
