package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumPostAttachmentMapper;
import com.blog.forum.domain.ForumPostAttachment;
import com.blog.forum.service.IForumPostAttachmentService;

/**
 * 帖子附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumPostAttachmentServiceImpl implements IForumPostAttachmentService
{
    @Autowired
    private ForumPostAttachmentMapper forumPostAttachmentMapper;

    /**
     * 查询帖子附件
     *
     * @param attachmentId 帖子附件主键
     * @return 帖子附件
     */
    @Override
    public ForumPostAttachment selectForumPostAttachmentByAttachmentId(Long attachmentId)
    {
        return forumPostAttachmentMapper.selectForumPostAttachmentByAttachmentId(attachmentId);
    }

    /**
     * 查询帖子附件列表
     *
     * @param forumPostAttachment 帖子附件
     * @return 帖子附件
     */
    @Override
    public List<ForumPostAttachment> selectForumPostAttachmentList(ForumPostAttachment forumPostAttachment)
    {
        return forumPostAttachmentMapper.selectForumPostAttachmentList(forumPostAttachment);
    }

    /**
     * 新增帖子附件
     *
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    @Override
    public int insertForumPostAttachment(ForumPostAttachment forumPostAttachment)
    {
        forumPostAttachment.setCreateTime(DateUtils.getNowDate());
        return forumPostAttachmentMapper.insertForumPostAttachment(forumPostAttachment);
    }

    /**
     * 修改帖子附件
     *
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    @Override
    public int updateForumPostAttachment(ForumPostAttachment forumPostAttachment)
    {
        return forumPostAttachmentMapper.updateForumPostAttachment(forumPostAttachment);
    }

    /**
     * 批量删除帖子附件
     *
     * @param attachmentIds 需要删除的帖子附件主键
     * @return 结果
     */
    @Override
    public int deleteForumPostAttachmentByAttachmentIds(Long[] attachmentIds)
    {
        return forumPostAttachmentMapper.deleteForumPostAttachmentByAttachmentIds(attachmentIds);
    }

    /**
     * 删除帖子附件信息
     *
     * @param attachmentId 帖子附件主键
     * @return 结果
     */
    @Override
    public int deleteForumPostAttachmentByAttachmentId(Long attachmentId)
    {
        return forumPostAttachmentMapper.deleteForumPostAttachmentByAttachmentId(attachmentId);
    }
}
