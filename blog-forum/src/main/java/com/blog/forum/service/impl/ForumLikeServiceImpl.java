package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumLikeMapper;
import com.blog.forum.domain.ForumLike;
import com.blog.forum.service.IForumLikeService;

/**
 * 点赞Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumLikeServiceImpl implements IForumLikeService
{
    @Autowired
    private ForumLikeMapper forumLikeMapper;

    /**
     * 查询点赞
     *
     * @param likeId 点赞主键
     * @return 点赞
     */
    @Override
    public ForumLike selectForumLikeByLikeId(Long likeId)
    {
        return forumLikeMapper.selectForumLikeByLikeId(likeId);
    }

    /**
     * 查询点赞列表
     *
     * @param forumLike 点赞
     * @return 点赞
     */
    @Override
    public List<ForumLike> selectForumLikeList(ForumLike forumLike)
    {
        return forumLikeMapper.selectForumLikeList(forumLike);
    }

    /**
     * 新增点赞
     *
     * @param forumLike 点赞
     * @return 结果
     */
    @Override
    public int insertForumLike(ForumLike forumLike)
    {
        forumLike.setCreateTime(DateUtils.getNowDate());
        return forumLikeMapper.insertForumLike(forumLike);
    }

    /**
     * 修改点赞
     *
     * @param forumLike 点赞
     * @return 结果
     */
    @Override
    public int updateForumLike(ForumLike forumLike)
    {
        return forumLikeMapper.updateForumLike(forumLike);
    }

    /**
     * 批量删除点赞
     *
     * @param likeIds 需要删除的点赞主键
     * @return 结果
     */
    @Override
    public int deleteForumLikeByLikeIds(Long[] likeIds)
    {
        return forumLikeMapper.deleteForumLikeByLikeIds(likeIds);
    }

    /**
     * 删除点赞信息
     *
     * @param likeId 点赞主键
     * @return 结果
     */
    @Override
    public int deleteForumLikeByLikeId(Long likeId)
    {
        return forumLikeMapper.deleteForumLikeByLikeId(likeId);
    }

    @Override
    public boolean checkIsLiked(Long userId, String contentType, Long contentId) {
        return forumLikeMapper.checkExists(contentType, contentId, userId);
    }
}
