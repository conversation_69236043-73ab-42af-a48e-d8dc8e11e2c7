package com.blog.forum.service.impl;

import java.util.List;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.SysUser;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.common.utils.StringUtils;
import com.blog.forum.domain.dto.UserInfoUpdateDTO;
import com.blog.system.domain.dto.AdminUserQueryDTO;
import com.blog.system.domain.vo.AdminUserVO;
import com.blog.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.system.mapper.ForumUserExtendMapper;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.forum.service.IForumUserExtendService;
import com.blog.system.mapper.SysUserMapper;

/**
 * 用户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumUserExtendServiceImpl implements IForumUserExtendService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ForumUserExtendMapper forumUserExtendMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询用户管理
     *
     * @param userId 用户管理主键
     * @return 用户管理
     */
    @Override
    public ForumUserExtend selectForumUserExtendByUserId(Long userId)
    {
        return forumUserExtendMapper.selectForumUserExtendByUserId(userId);
    }

    /**
     * 查询用户管理列表
     *
     * @param forumUserExtend 用户管理
     * @return 用户管理
     */
    @Override
    public List<ForumUserExtend> selectForumUserExtendList(ForumUserExtend forumUserExtend)
    {
        return forumUserExtendMapper.selectForumUserExtendList(forumUserExtend);
    }

    /**
     * 新增用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    @Override
    public int insertForumUserExtend(ForumUserExtend forumUserExtend)
    {
        return forumUserExtendMapper.insertForumUserExtend(forumUserExtend);
    }

    /**
     * 修改用户管理
     *
     * @param forumUserExtend 用户管理
     * @return 结果
     */
    @Override
    public int updateForumUserExtend(ForumUserExtend forumUserExtend)
    {
        forumUserExtend.setUpdateTime(DateUtils.getNowDate());
        return forumUserExtendMapper.updateForumUserExtend(forumUserExtend);
    }

    /**
     * 批量删除用户管理
     *
     * @param userIds 需要删除的用户管理主键
     * @return 结果
     */
    @Override
    public int deleteForumUserExtendByUserIds(Long[] userIds)
    {
        return forumUserExtendMapper.deleteForumUserExtendByUserIds(userIds);
    }

    /**
     * 删除用户管理信息
     *
     * @param userId 用户管理主键
     * @return 结果
     */
    @Override
    public int deleteForumUserExtendByUserId(Long userId)
    {
        return forumUserExtendMapper.deleteForumUserExtendByUserId(userId);
    }


    @Override
    public AjaxResult updateUserInfo(UserInfoUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (userId == null) {
            throw new ServiceException("请先登录");
        }

        int rows = forumUserExtendMapper.updateUserInfo(
                userId,
                updateDTO.getNickname(),
                updateDTO.getAvatar(),
                updateDTO.getBio()
        );

        if (rows > 0) {
            return AjaxResult.success("更新成功");
        }
        return AjaxResult.error("更新失败");
    }

    /**
     * 查询后台用户列表
     *
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    @Override
    public List<AdminUserVO> selectAdminUserList(AdminUserQueryDTO queryDTO) {
        return forumUserExtendMapper.selectAdminUserList(queryDTO);
    }

    /**
     * 更新用户禁止发帖状态
     *
     * @param userId 用户ID
     * @param isBannedPost 是否禁止发帖（0否 1是）
     * @return 结果
     */
    @Override
    public AjaxResult updateUserBannedPost(Long userId, String isBannedPost) {
        if (userId == null) {
            return AjaxResult.error("用户ID不能为空");
        }

        if (!StringUtils.equals(isBannedPost, "0") && !StringUtils.equals(isBannedPost, "1")) {
            return AjaxResult.error("禁止发帖状态参数错误");
        }

        int rows = forumUserExtendMapper.updateUserBannedPost(userId, isBannedPost);
        if (rows > 0) {
            return AjaxResult.success(StringUtils.equals(isBannedPost, "1") ? "禁止发帖成功" : "允许发帖成功");
        }
        return AjaxResult.error("操作失败");
    }

    /**
     * 更新用户状态
     *
     * @param sysUserId 系统用户ID
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    @Override
    public AjaxResult updateUserStatus(Long sysUserId, String status) {
        if (sysUserId == null) {
            return AjaxResult.error("用户ID不能为空");
        }

        if (!StringUtils.equals(status, "0") && !StringUtils.equals(status, "1")) {
            return AjaxResult.error("用户状态参数错误");
        }

        // 不能修改管理员状态
        if (SysUser.isAdmin(sysUserId)) {
            return AjaxResult.error("不允许修改超级管理员用户");
        }

        // 查询用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserId(sysUserId);
        sysUser.setStatus(status);

        int rows = sysUserMapper.updateUser(sysUser);
        if (rows > 0) {
            return AjaxResult.success(StringUtils.equals(status, "0") ? "启用成功" : "停用成功");
        }
        return AjaxResult.error("操作失败");
    }

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 结果
     */
    @Override
    public AjaxResult resetUserPassword(Long userId, String newPassword) {
        if (userId == null) {
            return AjaxResult.error("用户ID不能为空");
        }

        if (StringUtils.isEmpty(newPassword)) {
            return AjaxResult.error("新密码不能为空");
        }

        if (newPassword.length() < 6 || newPassword.length() > 20) {
            return AjaxResult.error("密码长度为6-20位");
        }

        // 查询用户信息
        ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(userId);
        if (userExtend == null) {
            return AjaxResult.error("用户不存在");
        }

        // 查询系统用户信息
        SysUser sysUser = userService.selectUserById(userExtend.getSysUserId());
        if (sysUser == null) {
            return AjaxResult.error("系统用户不存在");
        }

        // 检查用户状态
        if ("1".equals(sysUser.getStatus())) {
            return AjaxResult.error("用户已被禁用，无法重置密码");
        }

        // 不能重置管理员密码
        if (SysUser.isAdmin(sysUser.getUserId())) {
            return AjaxResult.error("不允许重置超级管理员密码");
        }

        // 更新密码
        sysUser.setPassword(SecurityUtils.encryptPassword(newPassword));
        int rows = sysUserMapper.updateUser(sysUser);
        if (rows > 0) {
            return AjaxResult.success("密码重置成功");
        }
        return AjaxResult.error("密码重置失败");
    }
}
