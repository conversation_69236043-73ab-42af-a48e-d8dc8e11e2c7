package com.blog.forum.service;

import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.page.TableDataInfo;
import com.blog.forum.domain.dto.CollectDTO;
import com.blog.forum.domain.dto.LikeDTO;
import com.blog.forum.domain.dto.ShareDTO;
import com.blog.forum.domain.vo.MyCollectVO;
import com.blog.forum.domain.vo.MyCommentVO;
import com.blog.forum.domain.vo.MyLikeVO;
import com.blog.forum.domain.vo.UserStatsVO;

import java.util.List;

public interface IForumInteractionService {
    AjaxResult like(LikeDTO likeDTO, Long userId);
    AjaxResult share(ShareDTO shareDTO, Long userId);
    AjaxResult collect(<PERSON>lect<PERSON><PERSON> collectDTO, Long userId);

    List<MyCollectVO> getMyCollectList(Long userId);

    List<MyLikeVO> getMyLikeList(Long userId);

    List<MyCommentVO> getMyCommentList(Long userId);

    UserStatsVO getUserStats(Long userId);
}
