package com.blog.forum.service.impl;

import java.util.List;
import com.blog.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.blog.forum.mapper.ForumPostI18nContentMapper;
import com.blog.forum.domain.ForumPostI18nContent;
import com.blog.forum.service.IForumPostI18nContentService;

/**
 * 多语言帖子内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class ForumPostI18nContentServiceImpl implements IForumPostI18nContentService
{
    @Autowired
    private ForumPostI18nContentMapper forumPostI18nContentMapper;

    /**
     * 查询多语言帖子内容
     *
     * @param contentId 多语言帖子内容主键
     * @return 多语言帖子内容
     */
    @Override
    public ForumPostI18nContent selectForumPostI18nContentByContentId(Long contentId)
    {
        return forumPostI18nContentMapper.selectForumPostI18nContentByContentId(contentId);
    }

    /**
     * 查询多语言帖子内容列表
     *
     * @param forumPostI18nContent 多语言帖子内容
     * @return 多语言帖子内容
     */
    @Override
    public List<ForumPostI18nContent> selectForumPostI18nContentList(ForumPostI18nContent forumPostI18nContent)
    {
        return forumPostI18nContentMapper.selectForumPostI18nContentList(forumPostI18nContent);
    }

    /**
     * 新增多语言帖子内容
     *
     * @param forumPostI18nContent 多语言帖子内容
     * @return 结果
     */
    @Override
    public int insertForumPostI18nContent(ForumPostI18nContent forumPostI18nContent)
    {
        forumPostI18nContent.setCreateTime(DateUtils.getNowDate());
        return forumPostI18nContentMapper.insertForumPostI18nContent(forumPostI18nContent);
    }

    /**
     * 修改多语言帖子内容
     *
     * @param forumPostI18nContent 多语言帖子内容
     * @return 结果
     */
    @Override
    public int updateForumPostI18nContent(ForumPostI18nContent forumPostI18nContent)
    {
        forumPostI18nContent.setUpdateTime(DateUtils.getNowDate());
        return forumPostI18nContentMapper.updateForumPostI18nContent(forumPostI18nContent);
    }

    /**
     * 批量删除多语言帖子内容
     *
     * @param contentIds 需要删除的多语言帖子内容主键
     * @return 结果
     */
    @Override
    public int deleteForumPostI18nContentByContentIds(Long[] contentIds)
    {
        return forumPostI18nContentMapper.deleteForumPostI18nContentByContentIds(contentIds);
    }

    /**
     * 删除多语言帖子内容信息
     *
     * @param contentId 多语言帖子内容主键
     * @return 结果
     */
    @Override
    public int deleteForumPostI18nContentByContentId(Long contentId)
    {
        return forumPostI18nContentMapper.deleteForumPostI18nContentByContentId(contentId);
    }
}
