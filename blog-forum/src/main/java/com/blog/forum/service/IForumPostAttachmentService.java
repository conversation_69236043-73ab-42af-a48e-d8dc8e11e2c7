package com.blog.forum.service;

import java.util.List;
import com.blog.forum.domain.ForumPostAttachment;

/**
 * 帖子附件Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IForumPostAttachmentService 
{
    /**
     * 查询帖子附件
     * 
     * @param attachmentId 帖子附件主键
     * @return 帖子附件
     */
    public ForumPostAttachment selectForumPostAttachmentByAttachmentId(Long attachmentId);

    /**
     * 查询帖子附件列表
     * 
     * @param forumPostAttachment 帖子附件
     * @return 帖子附件集合
     */
    public List<ForumPostAttachment> selectForumPostAttachmentList(ForumPostAttachment forumPostAttachment);

    /**
     * 新增帖子附件
     * 
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    public int insertForumPostAttachment(ForumPostAttachment forumPostAttachment);

    /**
     * 修改帖子附件
     * 
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    public int updateForumPostAttachment(ForumPostAttachment forumPostAttachment);

    /**
     * 批量删除帖子附件
     * 
     * @param attachmentIds 需要删除的帖子附件主键集合
     * @return 结果
     */
    public int deleteForumPostAttachmentByAttachmentIds(Long[] attachmentIds);

    /**
     * 删除帖子附件信息
     * 
     * @param attachmentId 帖子附件主键
     * @return 结果
     */
    public int deleteForumPostAttachmentByAttachmentId(Long attachmentId);
}
