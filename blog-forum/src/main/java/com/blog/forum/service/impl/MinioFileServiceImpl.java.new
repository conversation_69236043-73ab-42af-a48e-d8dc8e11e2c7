package com.blog.forum.service.impl;

import com.blog.common.exception.ServiceException;
import com.blog.common.utils.DateUtils;
import com.blog.common.utils.SecurityUtils;
import com.blog.common.utils.file.FileTypeUtils;
import com.blog.common.utils.uuid.IdUtils;
import com.blog.forum.service.FileService;
import com.blog.forum.config.properties.MinioConfig;

import com.blog.system.mapper.SysOssMapper;
import com.blog.system.domain.SysOss;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Service
public class MinioFileServiceImpl implements FileService {
    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private SysOssMapper sysOssMapper;
    @Override
    public String upload(MultipartFile file, String bizType) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            String fileName = extractFilename(file);
            String objectName = DateUtils.datePath() + "/" + fileName;

            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();

            minioClient.putObject(args);

            // 保存文件记录
            String url = minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + objectName;
            saveFileRecord(file, url, objectName, bizType, null);

            return url;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("上传文件失败:" +e.getMessage());
        }finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                }catch (Exception e){
                    e.printStackTrace();
                }

            }
        }
    }
    private String extractFilename(MultipartFile file) throws IOException {
        return IdUtils.fastSimpleUUID() + "." + FileTypeUtils.getFileExtendName(file.getBytes());
    }
    private void saveFileRecord(MultipartFile file, String url, String path, String bizType, Long bizId) throws IOException {
        SysOss oss = new SysOss();
        oss.setOriginalName(file.getOriginalFilename());
        oss.setFileName(path.substring(path.lastIndexOf("/") + 1));
        oss.setFileSuffix(FileTypeUtils.getFileExtendName(file.getBytes()));
        oss.setFileUrl(url);
        oss.setFilePath(path);
        oss.setFileSize(file.getSize());
        oss.setMimeType(file.getContentType());
        oss.setServiceType("minio");
        oss.setBizType(bizType);
        oss.setBizId(bizId);
        oss.setCreateBy(SecurityUtils.getUsername());
        oss.setCreateTime(DateUtils.getNowDate());

        sysOssMapper.insertSysOss(oss);
    }
    @Override
    public boolean deleteByUrl(String url) {
        SysOss oss = sysOssMapper.selectByUrl(url);
        if (oss == null) {
            return false;
        }

        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(oss.getFilePath())
                    .build());
            oss.setStatus(1);
            sysOssMapper.updateSysOss(oss);
            return true;
        } catch (Exception e) {
            throw new ServiceException("删除文件失败" + e.getMessage());
        }
    }

    @Override
    public List<String> uploadBatch(MultipartFile[] files, String bizType) {
        return null;
    }

    @Override
    public boolean deleteByBiz(String bizType, Long bizId) {
        return false;
    }

    @Override
    public void updateBizInfo(List<String> urls, String bizType, Long bizId) {

    }

    @Override
    public InputStream getStreamByUrl(String url) {
        return null;
    }

    @Override
    public String getEndpoint() {
        return null;
    }
}
