package com.blog.forum.service.impl;
import com.blog.common.core.domain.AjaxResult;
import com.blog.common.core.domain.entity.ForumUserExtend;
import com.blog.common.core.domain.entity.SysUser;
import com.blog.common.core.domain.model.LoginUser;
import com.blog.common.exception.ServiceException;
import com.blog.common.utils.SecurityUtils;
import com.blog.forum.domain.*;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.domain.dto.UserPostDTO;
import com.blog.forum.domain.vo.CategoryVO;
import com.blog.forum.domain.vo.PostAttachmentVO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.UserPostDetailVO;
import com.blog.forum.mapper.*;
import com.blog.forum.service.IForumCollectService;
import com.blog.forum.service.IForumCommentService;
import com.blog.forum.service.IForumLikeService;
import com.blog.forum.service.PostService;
import com.blog.forum.util.LangUtils;
import com.blog.system.mapper.ForumUserExtendMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PostServiceImpl implements PostService {
    @Autowired
    private ForumPostMapper postMapper;
    @Autowired
    private ForumPostTagMapper postTagMapper;
    @Autowired
    private ForumPostCategoryMapper categoryMapper;
    @Autowired
    private IForumLikeService likeService;

    @Autowired
    private ForumPostAttachmentMapper forumPostAttachmentMapper;

    @Autowired
    private ForumLanguageMapper languageMapper;

    @Autowired
    private ForumPostI18nContentMapper i18nContentMapper;

    @Autowired
    private ForumCommentMapper forumCommentMapper;

    @Autowired
    private IForumCommentService commentService;

    @Autowired
    private IForumCollectService collectService;

    @Autowired
    private ForumUserExtendMapper forumUserExtendMapper;

    @Override
    public List<PostDetailVO> getPostList(PostListDTO postListDTO) {
        Long langId = LangUtils.getCurrentLangId();
        // 设置状态为0（正常）
        postListDTO.setStatus("0");
        List<PostDetailVO> postList = postMapper.selectPostQuery(langId,postListDTO);

        // 设置标签和点赞状态
        Long userId = null;
        try {
            userId = SecurityUtils.getLoginUser().getUser().getUserId(); // 获取当前用户ID，未登录则为null
        }catch (Exception e){

        }

        Long finalUserId = userId;
        postList.forEach(post -> {
            post.setTags(postTagMapper.selectTagsByPostId(langId, post.getPostId()));
            ForumPostCategory category = categoryMapper.selectCategoryById(post.getCategoryId());
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyProperties(category,categoryVO);
            post.setCategory(categoryVO);
//            postca
            if (finalUserId != null) {
                boolean b = likeService.checkIsLiked(finalUserId, "0", post.getPostId());
                post.setIsLiked(b);
                boolean collected = collectService.isCollected("0", post.getPostId(), finalUserId);
                post.setCollected(collected);
//                post.setIsLiked(likeService.checkIsLiked(finalUserId, "0", post.getPostId()));
            } else {
                post.setIsLiked(false);
            }
        });

        return postList;
    }
    @Override
    public PostDetailVO getPostDetail(Long postId) {
        // 查询帖子信息
        ForumPost post = postMapper.selectForumPostByPostId(postId);
        if (post == null || !"0".equals(post.getStatus())) {
            throw new ServiceException("帖子不存在或已下线");
        }

        // 先增加浏览数
        postMapper.incrementViewCount(postId);

        Long langId = LangUtils.getCurrentLangId();
        PostDetailVO postDetail = postMapper.selectPostDetail(langId, postId);
        if (postDetail == null) {
            throw new ServiceException("帖子不存在或已被删除");
        }

        // 设置分类、标签和点赞状态
        postDetail.setTags(postTagMapper.selectTagsByPostId(langId, postId));

        ForumPostCategory category = categoryMapper.selectCategoryById(postDetail.getCategoryId());
        CategoryVO categoryVO = new CategoryVO();
        BeanUtils.copyProperties(category,categoryVO);
        postDetail.setCategory(categoryVO);
        List<PostAttachmentVO> postAttachmentVOS = forumPostAttachmentMapper.selectAttachmentsByPostId(postId);
        postDetail.setAttachments(postAttachmentVOS);
        // 设置标签和点赞状态
        Long userId = null;
        try {
            userId = SecurityUtils.getLoginUser().getUser().getUserId(); // 获取当前用户ID，未登录则为null
        }catch (Exception e){

        }
        if (userId != null) {
            boolean b = likeService.checkIsLiked(userId, "0", postId);
            postDetail.setIsLiked(b);
            boolean collected = collectService.isCollected("0", postId, userId);
            postDetail.setCollected(collected);
        } else {
            postDetail.setIsLiked(false);
        }

        return postDetail;
    }
    @Override
    public List<CategoryVO> getCategoryList() {
        Long langId = LangUtils.getCurrentLangId();
        // 只查询状态为0（正常）的分类
        return categoryMapper.selectCategoryList(langId).stream()
                .filter(category -> "0".equals(category.getStatus()))
                .collect(Collectors.toList());
    }


    @Override
    public List<UserPostDetailVO> getUserPostList(PostListDTO postListDTO, Long userId) {
        PageHelper.startPage(postListDTO.getPageNum(), postListDTO.getPageSize());
        Long langId = LangUtils.getCurrentLangId();

        List<UserPostDetailVO> postList = postMapper.selectUserPostList(
                langId, userId, postListDTO.getCategoryId(),
                postListDTO.getSortType(), postListDTO.getKeyword()
        );

        // 设置标签和点赞状态
        Long currentUserId = SecurityUtils.getUserId();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if (currentUserId == null && user.getUserId() == null) {
            throw new ServiceException("请先登录");
        }
        postList.forEach(post -> {
            ForumPostCategory category = categoryMapper.selectCategoryById(post.getCategoryId());
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyProperties(category,categoryVO);
            post.setCategory(categoryVO);
            post.setTags(postTagMapper.selectTagsByPostId(langId, post.getPostId()));
            post.setAttachments(forumPostAttachmentMapper.selectAttachmentsByPostId(post.getPostId()));
            post.setIsLiked(loginUser.getUserId() != null &&
                    likeService.checkIsLiked(loginUser.getUserId(), "1", post.getPostId()));
        });

        return postList;
    }
    @Override
    public UserPostDetailVO getUserPostDetail(Long postId) {
        Long langId = LangUtils.getCurrentLangId();
        UserPostDetailVO postDetail = postMapper.selectUserPostDetail(langId, postId);
        if (postDetail == null) {
            throw new ServiceException("帖子不存在或已被删除");
        }

        // 增加浏览数
        postMapper.incrementViewCount(postId);
        ForumPostCategory category = categoryMapper.selectCategoryById(postDetail.getCategoryId());
        CategoryVO categoryVO = new CategoryVO();
        BeanUtils.copyProperties(category,categoryVO);
        postDetail.setCategory(categoryVO);
        // 设置标签、附件和点赞状态
        postDetail.setTags(postTagMapper.selectTagsByPostId(langId, postId));
        postDetail.setAttachments(forumPostAttachmentMapper.selectAttachmentsByPostId(postId));
        Long currentUserId = SecurityUtils.getUserId();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if (currentUserId == null && user.getUserId() == null) {
            throw new ServiceException("请先登录");
        }
        postDetail.setIsLiked(loginUser.getUserId() != null &&
                likeService.checkIsLiked(loginUser.getUserId(), "1", postId));

        return postDetail;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult submitUserPost(UserPostDTO userPostDTO) {
        Long currentUserId = SecurityUtils.getUserId();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if (currentUserId == null && user.getUserId() == null) {
            throw new ServiceException("请先登录");
        }

        // 检查用户是否被禁止发帖
        ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(user.getUserId());
        if (userExtend != null && "1".equals(userExtend.getIsBannedPost())) {
            throw new ServiceException("您已被禁止发帖");
        }

        ForumPost post;
        if (userPostDTO.getPostId() == null) {
            // 新增帖子
            post = new ForumPost();
            post.setSysUserId(user.getUserId());
            post.setIsUserPost("0");
            post.setTitle(userPostDTO.getTitle());
            post.setContent(userPostDTO.getContent());
            post.setLangId(LangUtils.getCurrentLangId());
            post.setIsUserPost("1");
            post.setStatus("0");
            post.setPostStatus("1"); // 待审核
            post.setCreateBy(String.valueOf(user.getUserId()));
            post.setCreateTime(new Date());
            postMapper.insertForumPost(post);
        } else {
            // 更新帖子
            post = postMapper.selectForumPostByPostId(userPostDTO.getPostId());
            if (post == null || !post.getSysUserId().equals(user.getUserId())) {
                throw new ServiceException("无权操作此帖子");
            }
        }

        // 更新帖子基本信息
        post.setTitle(userPostDTO.getTitle());
        post.setCategoryId(userPostDTO.getCategoryId());
        post.setCoverImage(userPostDTO.getCoverImage());
        post.setContent(userPostDTO.getContent());
        post.setSummary(userPostDTO.getSummary());
        post.setUpdateBy(String.valueOf(currentUserId));
        post.setUpdateTime(new Date());
        postMapper.updateForumPost(post);

        // 处理标签
        postTagMapper.deleteByPostId(post.getPostId());
        if (!CollectionUtils.isEmpty(userPostDTO.getTagIds())) {
            List<ForumPostTag> postTags = userPostDTO.getTagIds().stream()
                    .map(tagId -> {
                        ForumPostTag tag = new ForumPostTag();
                        tag.setPostId(post.getPostId());
                        tag.setTagId(tagId);
                        return tag;
                    })
                    .collect(Collectors.toList());
            postTagMapper.batchInsert(postTags);
        }

        // 处理附件
        if (!CollectionUtils.isEmpty(userPostDTO.getAttachments())) {
            List<ForumPostAttachment> attachments = userPostDTO.getAttachments().stream()
                    .map(att -> {
                        ForumPostAttachment attachment = new ForumPostAttachment();
                        attachment.setPostId(post.getPostId());
                        attachment.setContentType("1"); // 用户帖子内容
                        attachment.setFileName(att.getFileName());
                        attachment.setFileUrl(att.getFileUrl());
                        attachment.setFileSize(att.getFileSize());
                        attachment.setFileType(att.getFileType());
                        return attachment;
                    })
                    .collect(Collectors.toList());
            forumPostAttachmentMapper.batchInsert(attachments);
        }

        // 处理国际化内容
        saveI18nContent(post.getPostId(), userPostDTO);

        // 如果是新增帖子，累加用户发帖数量
        if (userPostDTO.getPostId() == null) {
            // 查询用户扩展信息
            if (userExtend != null) {
                // 累加发帖数量
                forumUserExtendMapper.incrementPostCount(user.getUserId());
            }
        }

        return AjaxResult.success(post.getPostId());
    }

    private void saveI18nContent(Long postId, UserPostDTO dto) {
        // 获取当前支持的语言
        List<ForumLanguage> languages = languageMapper.selectEnabledLanguageList();

        for (ForumLanguage lang : languages) {
            ForumPostI18nContent content = new ForumPostI18nContent();
            content.setPostId(postId);
            content.setLangId(lang.getLangId());
            content.setTitle(dto.getTitle());
            content.setSummary(dto.getSummary());
            content.setContent(dto.getContent());
//            content.setStatus("0"); // 正常
            content.setCreateTime(new Date());
            Long id = i18nContentMapper.exists(postId, lang.getLangId());
            id = id == null?0:id;
            if (id > 0) {
                content.setContentId(id);
                i18nContentMapper.updateForumPostI18nContent(content);
            } else {
                i18nContentMapper.insertForumPostI18nContent(content);
            }
        }
    }
}
