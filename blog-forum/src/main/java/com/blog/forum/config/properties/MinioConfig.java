package com.blog.forum.config.properties;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
@Data
@Component
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
    private String endpoint;
    private String url;
    private String accessKey;
    private String secretKey;
    private String bucketName;

    @Bean
    public MinioClient minioClient() {
        // 创建 MinioClient 实例
        return MinioClient.builder()
                .endpoint("127.0.0.1", 9000, false)
                .credentials(accessKey, secretKey)
                .build();
    }
}
