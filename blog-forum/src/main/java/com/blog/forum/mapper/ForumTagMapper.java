package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumTag;
import org.apache.ibatis.annotations.Param;

/**
 * 帖子标签Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ForumTagMapper
{
    /**
     * 查询帖子标签
     *
     * @param tagId 帖子标签主键
     * @return 帖子标签
     */
    public ForumTag selectForumTagByTagId(Long tagId);

    /**
     * 查询帖子标签列表
     *
     * @param forumTag 帖子标签
     * @return 帖子标签集合
     */
    public List<ForumTag> selectForumTagList(ForumTag forumTag);

    /**
     * 新增帖子标签
     *
     * @param forumTag 帖子标签
     * @return 结果
     */
    public int insertForumTag(ForumTag forumTag);

    /**
     * 修改帖子标签
     *
     * @param forumTag 帖子标签
     * @return 结果
     */
    public int updateForumTag(ForumTag forumTag);

    /**
     * 删除帖子标签
     *
     * @param tagId 帖子标签主键
     * @return 结果
     */
    public int deleteForumTagByTagId(Long tagId);

    /**
     * 批量删除帖子标签
     *
     * @param tagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumTagByTagIds(Long[] tagIds);

    List<ForumTag> selectTagList(@Param("forumTag") ForumTag forumTag, @Param("langId") Long langId);
}
