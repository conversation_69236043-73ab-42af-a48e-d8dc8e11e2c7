package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumCategoryI18n;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * 分类多语言内容Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ForumCategoryI18nMapper
{
    /**
     * 查询分类多语言内容
     *
     * @param i18nId 分类多语言内容主键
     * @return 分类多语言内容
     */
    public ForumCategoryI18n selectForumCategoryI18nByI18nId(Long i18nId);

    /**
     * 查询分类多语言内容列表
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 分类多语言内容集合
     */
    public List<ForumCategoryI18n> selectForumCategoryI18nList(ForumCategoryI18n forumCategoryI18n);

    /**
     * 新增分类多语言内容
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    public int insertForumCategoryI18n(ForumCategoryI18n forumCategoryI18n);

    /**
     * 修改分类多语言内容
     *
     * @param forumCategoryI18n 分类多语言内容
     * @return 结果
     */
    public int updateForumCategoryI18n(ForumCategoryI18n forumCategoryI18n);

    /**
     * 删除分类多语言内容
     *
     * @param i18nId 分类多语言内容主键
     * @return 结果
     */
    public int deleteForumCategoryI18nByI18nId(Long i18nId);

    /**
     * 批量删除分类多语言内容
     *
     * @param i18nIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumCategoryI18nByI18nIds(Long[] i18nIds);

    /**
     * 根据分类ID列表和语言ID查询多语言信息
     *
     * @param categoryIds 分类ID列表
     * @param langId 语言ID
     * @return 多语言信息列表
     */
    public List<ForumCategoryI18n> selectByCategoryIdsAndLangId(@Param("categoryIds") List<Long> categoryIds, @Param("langId") Long langId);
}
