package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumPostCategory;
import com.blog.forum.domain.vo.CategoryVO;
import org.apache.ibatis.annotations.Param;

/**
 * 帖子分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumPostCategoryMapper
{
    /**
     * 查询帖子分类
     *
     * @param categoryId 帖子分类主键
     * @param langId 语言ID
     * @return 帖子分类
     */
    public ForumPostCategory selectForumPostCategoryByCategoryId(@Param("categoryId") Long categoryId, @Param("langId") Long langId);

    /**
     * 查询帖子分类列表
     *
     * @param forumPostCategory 帖子分类
     * @param langId 语言ID
     * @return 帖子分类集合
     */
    public List<ForumPostCategory> selectForumPostCategoryList(@Param("forumPostCategory") ForumPostCategory forumPostCategory, @Param("langId") Long langId);

    /**
     * 新增帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    public int insertForumPostCategory(ForumPostCategory forumPostCategory);

    /**
     * 修改帖子分类
     *
     * @param forumPostCategory 帖子分类
     * @return 结果
     */
    public int updateForumPostCategory(ForumPostCategory forumPostCategory);

    /**
     * 删除帖子分类
     *
     * @param categoryId 帖子分类主键
     * @return 结果
     */
    public int deleteForumPostCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除帖子分类
     *
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumPostCategoryByCategoryIds(Long[] categoryIds);

    List<CategoryVO> selectCategoryList(@Param("langId") Long langId);

    List<ForumPostCategory> selectCategoryListAll(ForumPostCategory forumPostCategory);

    ForumPostCategory selectCategoryById(Long categoryId);

    List<CategoryVO> selectCategoryListVo(Long langId);
}
