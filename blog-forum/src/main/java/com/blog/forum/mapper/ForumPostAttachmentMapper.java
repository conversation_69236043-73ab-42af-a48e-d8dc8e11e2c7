package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumPostAttachment;
import com.blog.forum.domain.vo.PostAttachmentVO;
import org.apache.ibatis.annotations.Param;

/**
 * 帖子附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumPostAttachmentMapper
{
    /**
     * 查询帖子附件
     *
     * @param attachmentId 帖子附件主键
     * @return 帖子附件
     */
    public ForumPostAttachment selectForumPostAttachmentByAttachmentId(Long attachmentId);

    /**
     * 查询帖子附件列表
     *
     * @param forumPostAttachment 帖子附件
     * @return 帖子附件集合
     */
    public List<ForumPostAttachment> selectForumPostAttachmentList(ForumPostAttachment forumPostAttachment);

    /**
     * 新增帖子附件
     *
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    public int insertForumPostAttachment(ForumPostAttachment forumPostAttachment);

    /**
     * 修改帖子附件
     *
     * @param forumPostAttachment 帖子附件
     * @return 结果
     */
    public int updateForumPostAttachment(ForumPostAttachment forumPostAttachment);

    /**
     * 删除帖子附件
     *
     * @param attachmentId 帖子附件主键
     * @return 结果
     */
    public int deleteForumPostAttachmentByAttachmentId(Long attachmentId);

    /**
     * 批量删除帖子附件
     *
     * @param attachmentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumPostAttachmentByAttachmentIds(Long[] attachmentIds);

    int batchInsert(List<ForumPostAttachment> attachments);

    List<PostAttachmentVO> selectAttachmentsByPostId(@Param("postId") Long postId);

    /**
     * 根据评论ID查询附件列表
     *
     * @param commentId 评论ID
     * @return 附件列表
     */
    List<PostAttachmentVO> selectAttachmentsByCommentId(@Param("commentId") Long commentId);

    /**
     * 根据评论ID删除附件
     *
     * @param commentId 评论ID
     * @return 结果
     */
    int deleteByCommentId(@Param("commentId") Long commentId);
}
