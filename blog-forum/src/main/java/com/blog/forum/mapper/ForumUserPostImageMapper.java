package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumUserPostImage;
import org.apache.ibatis.annotations.Param;

/**
 * 用户发帖图片Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ForumUserPostImageMapper
{
    /**
     * 查询用户发帖图片
     *
     * @param imageId 用户发帖图片主键
     * @return 用户发帖图片
     */
    public ForumUserPostImage selectForumUserPostImageByImageId(Long imageId);

    /**
     * 查询用户发帖图片列表
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 用户发帖图片集合
     */
    public List<ForumUserPostImage> selectForumUserPostImageList(ForumUserPostImage forumUserPostImage);

    /**
     * 新增用户发帖图片
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 结果
     */
    public int insertForumUserPostImage(ForumUserPostImage forumUserPostImage);

    /**
     * 修改用户发帖图片
     *
     * @param forumUserPostImage 用户发帖图片
     * @return 结果
     */
    public int updateForumUserPostImage(ForumUserPostImage forumUserPostImage);

    /**
     * 删除用户发帖图片
     *
     * @param imageId 用户发帖图片主键
     * @return 结果
     */
    public int deleteForumUserPostImageByImageId(Long imageId);

    /**
     * 批量删除用户发帖图片
     *
     * @param imageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumUserPostImageByImageIds(Long[] imageIds);

    void batchInsert(@Param("list") List<ForumUserPostImage> images);
}
