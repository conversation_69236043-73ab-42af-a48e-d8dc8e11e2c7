package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumPost;
import com.blog.forum.domain.dto.PostListDTO;
import com.blog.forum.domain.dto.PostListQueryDTO;
import com.blog.forum.domain.dto.PostQueryDTO;
import com.blog.forum.domain.vo.PostDetailVO;
import com.blog.forum.domain.vo.PostVO;
import com.blog.forum.domain.vo.UserPostDetailVO;
import org.apache.ibatis.annotations.Param;


/**
 * 帖子Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ForumPostMapper
{
    /**
     * 查询帖子
     *
     * @param postId 帖子主键
     * @return 帖子
     */
    public ForumPost selectForumPostByPostId(Long postId);

    /**
     * 查询帖子列表
     *
     * @param forumPost 帖子
     * @return 帖子集合
     */
    public List<ForumPost> selectForumPostList(ForumPost forumPost);

    /**
     * 新增帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    public int insertForumPost(ForumPost forumPost);

    /**
     * 修改帖子
     *
     * @param forumPost 帖子
     * @return 结果
     */
    public int updateForumPost(ForumPost forumPost);

    /**
     * 删除帖子
     *
     * @param postId 帖子主键
     * @return 结果
     */
    public int deleteForumPostByPostId(Long postId);

    /**
     * 批量删除帖子
     *
     * @param postIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumPostByPostIds(Long[] postIds);

    List<PostVO> selectPostList(@Param("query") PostQueryDTO query,
                                @Param("langId") Long langId);

    PostVO selectPostById(@Param("postId") Long postId,
                          @Param("langId") Long langId);

    int incrementViewCount(@Param("postId") Long postId);

    int incrementLikeCount(@Param("postId") Long postId, @Param("increment") int increment);
    int incrementCollectCount(@Param("postId") Long postId, @Param("increment") int increment);
    int incrementShareCount(@Param("postId") Long postId);
    int incrementCommentCount(@Param("postId") Long postId, @Param("increment") int increment);

    PostDetailVO selectPostDetail(@Param("langId") Long langId, @Param("postId") Long postId);

    List<PostDetailVO> selectPostQuery(@Param("langId") Long langId, @Param("params") PostListDTO postListDTO);

    List<UserPostDetailVO> selectUserPostList(@Param("langId") Long langId,
                                              @Param("userId") Long userId,
                                              @Param("categoryId") Long categoryId,
                                              @Param("sortType") String sortType,
                                              @Param("keyword") String keyword);

    UserPostDetailVO selectUserPostDetail(@Param("langId") Long langId,
                                          @Param("postId") Long postId);

    /**
     * 查询帖子列表（高级查询）
     *
     * @param query 查询条件
     * @param langId 语言ID
     * @return 帖子列表
     */
    List<PostDetailVO> selectPostListAdvanced(@Param("query") PostListQueryDTO query,
                                           @Param("langId") Long langId);

    /**
     * 查询帖子作者ID
     *
     * @param postId 帖子ID
     * @return 作者ID
     */
    Long selectPostAuthorId(@Param("postId") Long postId);

//    int insertUserPost(ForumPost post);
//
//    int updateUserPost(ForumPost post);

//    int incrementViewCount(@Param("postId") Long postId);

//    int insertUserPost(ForumPost post);
//
//    int updateUserPost(ForumPost post);

    /**
     * 更新帖子置顶状态
     *
     * @param postId 帖子ID
     * @param isTop 是否置顶（0否 1是）
     * @return 结果
     */
    int updatePostTopStatus(@Param("postId") Long postId, @Param("isTop") String isTop);
}
