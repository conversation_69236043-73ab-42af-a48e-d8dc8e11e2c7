package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumUserPost;
import com.blog.forum.domain.dto.UserPostQueryDTO;
import com.blog.forum.domain.vo.PostTagVO;
import com.blog.forum.domain.vo.UserPostDetailVO;
import org.apache.ibatis.annotations.Param;


/**
 * 用户发帖Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumUserPostMapper
{
    /**
     * 查询用户发帖
     *
     * @param userPostId 用户发帖主键
     * @return 用户发帖
     */
    public ForumUserPost selectForumUserPostByUserPostId(Long userPostId);

    /**
     * 查询用户发帖列表
     *
     * @param forumUserPost 用户发帖
     * @return 用户发帖集合
     */
    public List<ForumUserPost> selectForumUserPostList(ForumUserPost forumUserPost);

    /**
     * 新增用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    public int insertForumUserPost(ForumUserPost forumUserPost);

    /**
     * 修改用户发帖
     *
     * @param forumUserPost 用户发帖
     * @return 结果
     */
    public int updateForumUserPost(ForumUserPost forumUserPost);

    /**
     * 删除用户发帖
     *
     * @param userPostId 用户发帖主键
     * @return 结果
     */
    public int deleteForumUserPostByUserPostId(Long userPostId);

    /**
     * 批量删除用户发帖
     *
     * @param userPostIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumUserPostByUserPostIds(Long[] userPostIds);

    /**
     * 查询用户发帖列表
     */
    List<UserPostDetailVO> selectUserPostList(@Param("query") UserPostQueryDTO query);

    /**
     * 查询用户发帖详情
     */
    UserPostDetailVO selectUserPostDetail(@Param("userPostId") Long userPostId);

    /**
     * 新增用户发帖
     */
    int insertUserPost(ForumUserPost forumUserPost);

    /**
     * 更新浏览量
     */
    int updateViewCount(@Param("userPostId") Long userPostId);

    /**
     * 更新评论数
     */
    int incrementCommentCount(@Param("userPostId") Long userPostId, @Param("increment") int increment);

    // 原有方法不变，增加两个查询方法：
    List<PostTagVO> selectPostTags(@Param("postId") Long postId);

    String selectCategoryName(@Param("categoryId") Long categoryId);
}
