package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumLike;
import com.blog.forum.domain.vo.MyLikeVO;
import org.apache.ibatis.annotations.Param;

/**
 * 点赞Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumLikeMapper
{
    /**
     * 查询点赞
     *
     * @param likeId 点赞主键
     * @return 点赞
     */
    public ForumLike selectForumLikeByLikeId(Long likeId);

    /**
     * 查询点赞列表
     *
     * @param forumLike 点赞
     * @return 点赞集合
     */
    public List<ForumLike> selectForumLikeList(ForumLike forumLike);

    /**
     * 新增点赞
     *
     * @param forumLike 点赞
     * @return 结果
     */
    public int insertForumLike(ForumLike forumLike);

    /**
     * 修改点赞
     *
     * @param forumLike 点赞
     * @return 结果
     */
    public int updateForumLike(ForumLike forumLike);

    /**
     * 删除点赞
     *
     * @param likeId 点赞主键
     * @return 结果
     */
    public int deleteForumLikeByLikeId(Long likeId);

    /**
     * 批量删除点赞
     *
     * @param likeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumLikeByLikeIds(Long[] likeIds);

    int insertLike(ForumLike forumLike);
    int deleteLike(@Param("contentType") String contentType,
                   @Param("contentId") Long contentId,
                   @Param("userId") Long userId);
    boolean checkExists(@Param("contentType") String contentType,
                        @Param("contentId") Long contentId,
                        @Param("userId") Long userId);
    int selectLikeCount(@Param("contentType") String contentType,
                        @Param("contentId") Long contentId);

    List<MyLikeVO> selectUserLikeList(@Param("userId") Long userId, @Param("langId") Long langId);

    int selectUserLikeCount(@Param("userId") Long userId);
}
