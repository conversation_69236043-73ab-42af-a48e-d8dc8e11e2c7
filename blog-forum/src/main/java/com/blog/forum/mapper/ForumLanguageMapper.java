package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumLanguage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 语言类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumLanguageMapper
{
    /**
     * 查询语言类型
     *
     * @param langId 语言类型主键
     * @return 语言类型
     */
    public ForumLanguage selectForumLanguageByLangId(Long langId);

    /**
     * 查询语言类型列表
     *
     * @param forumLanguage 语言类型
     * @return 语言类型集合
     */
    public List<ForumLanguage> selectForumLanguageList(ForumLanguage forumLanguage);

    /**
     * 新增语言类型
     *
     * @param forumLanguage 语言类型
     * @return 结果
     */
    public int insertForumLanguage(ForumLanguage forumLanguage);

    /**
     * 修改语言类型
     *
     * @param forumLanguage 语言类型
     * @return 结果
     */
    public int updateForumLanguage(ForumLanguage forumLanguage);

    /**
     * 删除语言类型
     *
     * @param langId 语言类型主键
     * @return 结果
     */
    public int deleteForumLanguageByLangId(Long langId);

    /**
     * 批量删除语言类型
     *
     * @param langIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumLanguageByLangIds(Long[] langIds);

    @Select("select lang_id from forum_language where lang_code = #{langCode}")
    ForumLanguage selectByLangCode(@Param("langCode") String langCode);


    List<ForumLanguage> selectEnabledLanguageList();
}
