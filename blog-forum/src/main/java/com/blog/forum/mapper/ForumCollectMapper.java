package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumCollect;
import com.blog.forum.domain.vo.MyCollectVO;
import org.apache.ibatis.annotations.Param;

/**
 * 收藏Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumCollectMapper
{
    /**
     * 查询收藏
     *
     * @param collectId 收藏主键
     * @return 收藏
     */
    public ForumCollect selectForumCollectByCollectId(Long collectId);

    /**
     * 查询收藏列表
     *
     * @param forumCollect 收藏
     * @return 收藏集合
     */
    public List<ForumCollect> selectForumCollectList(ForumCollect forumCollect);

    /**
     * 新增收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    public int insertForumCollect(ForumCollect forumCollect);

    /**
     * 修改收藏
     *
     * @param forumCollect 收藏
     * @return 结果
     */
    public int updateForumCollect(ForumCollect forumCollect);

    /**
     * 删除收藏
     *
     * @param collectId 收藏主键
     * @return 结果
     */
    public int deleteForumCollectByCollectId(Long collectId);

    /**
     * 批量删除收藏
     *
     * @param collectIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumCollectByCollectIds(Long[] collectIds);

    int insertCollect(ForumCollect forumCollect);
    int deleteCollect(@Param("contentType") String contentType,
                      @Param("contentId") Long contentId,
                      @Param("userId") Long userId);
    boolean checkExists(@Param("contentType") String contentType,
                        @Param("contentId") Long contentId,
                        @Param("userId") Long userId);
    int selectCollectCount(@Param("contentType") String contentType,
                           @Param("contentId") Long contentId);

    List<MyCollectVO> selectUserCollectList(@Param("userId") Long userId, @Param("langId") Long langId);

    int selectUserCollectCount(@Param("userId") Long userId);

    /**
     * 查询用户对指定内容的收藏记录
     */
    ForumCollect selectCollectByUserAndContent(
            @Param("contentType") String contentType,
            @Param("contentId") Long contentId,
            @Param("userId") Long userId);

    /**
     * 检查用户是否已收藏指定内容
     */
    boolean checkCollectExists(
            @Param("contentType") String contentType,
            @Param("contentId") Long contentId,
            @Param("userId") Long userId);
}
