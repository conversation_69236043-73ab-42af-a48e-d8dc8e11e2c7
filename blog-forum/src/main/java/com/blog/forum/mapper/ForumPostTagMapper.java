package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumPostTag;
import com.blog.forum.domain.vo.TagVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 帖子标签关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ForumPostTagMapper
{
    /**
     * 查询帖子标签关联
     *
     * @param id 帖子标签关联主键
     * @return 帖子标签关联
     */
    public ForumPostTag selectForumPostTagById(Long id);

    /**
     * 查询帖子标签关联列表
     *
     * @param forumPostTag 帖子标签关联
     * @return 帖子标签关联集合
     */
    public List<ForumPostTag> selectForumPostTagList(ForumPostTag forumPostTag);

    /**
     * 新增帖子标签关联
     *
     * @param forumPostTag 帖子标签关联
     * @return 结果
     */
    public int insertForumPostTag(ForumPostTag forumPostTag);

    /**
     * 修改帖子标签关联
     *
     * @param forumPostTag 帖子标签关联
     * @return 结果
     */
    public int updateForumPostTag(ForumPostTag forumPostTag);

    /**
     * 删除帖子标签关联
     *
     * @param id 帖子标签关联主键
     * @return 结果
     */
    public int deleteForumPostTagById(Long id);

    /**
     * 批量删除帖子标签关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumPostTagByIds(Long[] ids);

    @Select("select tag_id from forum_post_tag where post_id = #{postId}")
    List<Long> selectTagIdsByPostId(@Param("postId") Long postId);

    void batchInsert(List<ForumPostTag> postTags);

    List<TagVO> selectTagsByPostId(@Param("langId") Long langId, @Param("postId") Long postId);

    @Delete("delete from forum_post_tag where post_id = #{postId}")
    Integer deleteByPostId(@Param("postId") Long postId);
}
