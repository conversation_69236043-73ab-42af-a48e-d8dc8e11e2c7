package com.blog.forum.mapper;

import java.util.List;
import com.blog.forum.domain.ForumComment;
import com.blog.forum.domain.dto.AdminCommentQueryDTO;
import com.blog.forum.domain.vo.MyCommentVO;
import org.apache.ibatis.annotations.Param;
import com.blog.forum.domain.vo.CommentDetailVO;

/**
 * 评论Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface ForumCommentMapper
{
    /**
     * 查询评论
     *
     * @param commentId 评论主键
     * @return 评论
     */
    public ForumComment selectForumCommentByCommentId(Long commentId);

    /**
     * 查询评论列表
     *
     * @param forumComment 评论
     * @return 评论集合
     */
    public List<ForumComment> selectForumCommentList(ForumComment forumComment);

    /**
     * 新增评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    public int insertForumComment(ForumComment forumComment);

    /**
     * 修改评论
     *
     * @param forumComment 评论
     * @return 结果
     */
    public int updateForumComment(ForumComment forumComment);

    /**
     * 删除评论
     *
     * @param commentId 评论主键
     * @return 结果
     */
    public int deleteForumCommentByCommentId(Long commentId);

    /**
     * 批量删除评论
     *
     * @param commentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForumCommentByCommentIds(Long[] commentIds);


    /**
     * 查询评论列表（一级评论）
     */
    List<CommentDetailVO> selectCommentList(
            @Param("contentType") String contentType,
            @Param("contentId") Long contentId,
            @Param("userId") Long userId);

    List<CommentDetailVO> selectReplies(@Param("contentType") String contentType,
                                        @Param("contentId") Long contentId,
                                        @Param("userId") Long userId,
                                        @Param("commentId") Long commentId);
    /**
     * 新增评论
     */
    int insertComment(ForumComment forumComment);

    /**
     * 更新评论状态（逻辑删除）
     */
    int updateCommentStatus(
            @Param("commentId") Long commentId,
            @Param("userId") Long userId,
            @Param("status") String status);

    /**
     * 根据ID查询评论
     */
    ForumComment selectCommentById(Long commentId);

    /**
     * 增加/减少点赞数
     */
    int incrementLikeCount(
            @Param("commentId") Long commentId,
            @Param("increment") int increment);

    List<MyCommentVO> selectUserCommentList(@Param("userId") Long userId, @Param("langId") Long langId);

    int selectUserCommentCount(@Param("userId") Long userId);

    /**
     * 查询后台评论列表
     *
     * @param queryDTO 查询条件
     * @return 评论列表
     */
    List<CommentDetailVO> selectAdminCommentList(AdminCommentQueryDTO queryDTO);

    /**
     * 查询评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    List<CommentDetailVO> selectCommentReplies(Long parentId);
}
