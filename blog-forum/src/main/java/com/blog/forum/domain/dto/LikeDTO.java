package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
@Data
@ApiModel("点赞DTO")
public class LikeDTO {
    @ApiModelProperty(value = "内容类型（0帖子 1用户发帖 2评论）", required = true)
    @NotBlank(message = "内容类型不能为空")
    private String contentType;

    @ApiModelProperty(value = "内容ID", required = true)
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    @ApiModelProperty(value = "是否点赞", required = true)
    @NotNull(message = "点赞状态不能为空")
    private Boolean isLike;
}
