package com.blog.forum.domain.dto;

import lombok.Data;

/**
 * 分片上传数据传输对象
 */
@Data
public class ChunkUploadDTO {
    /**
     * 当前分片号
     */
    private Integer chunkNumber;
    
    /**
     * 分片总数
     */
    private Integer totalChunks;
    
    /**
     * 分片大小
     */
    private Long chunkSize;
    
    /**
     * 当前分片大小
     */
    private Long currentChunkSize;
    
    /**
     * 文件总大小
     */
    private Long totalSize;
    
    /**
     * 文件标识（MD5）
     */
    private String identifier;
    
    /**
     * 文件名
     */
    private String filename;
    
    /**
     * 业务类型
     */
    private String bizType;
}
