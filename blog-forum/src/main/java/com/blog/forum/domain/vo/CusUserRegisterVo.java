package com.blog.forum.domain.vo;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
public class CusUserRegisterVo {
    @NotBlank(message = "�ǳƲ���Ϊ��")
    private String nickname;

    @NotBlank(message = "���䲻��Ϊ��")
    @Email(message = "�����ʽ����ȷ")
    private String email;

    @NotBlank(message = "���벻��Ϊ��")
    private String password;

    private String avatar; // ͷ��URL

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }


    // getters and setters
}
