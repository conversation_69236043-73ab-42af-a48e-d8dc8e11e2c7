package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("用户发帖参数")
public class UserPostDTO {
    @ApiModelProperty(value = "帖子ID(更新时必填)", example = "1")
    private Long postId;

    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    @NotNull(message = "分类不能为空")
    private Long categoryId;

    @ApiModelProperty(value = "标签ID列表", required = true)
    @NotEmpty(message = "至少需要一个标签")
    private List<Long> tagIds;

    @ApiModelProperty(value = "标题", required = true, example = "我的第一篇帖子")
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "摘要", example = "这是帖子的摘要")
    private String summary;

    @ApiModelProperty(value = "封面图URL", example = "http://example.com/cover.jpg")
    private String coverImage;

    @ApiModelProperty(value = "内容(富文本)", required = true, example = "<p>这是帖子内容</p>")
    @NotBlank(message = "内容不能为空")
    private String content;

    @ApiModelProperty("附件列表")
    private List<PostAttachmentDTO> attachments;
}
