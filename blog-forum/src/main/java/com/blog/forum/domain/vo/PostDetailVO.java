package com.blog.forum.domain.vo;

import com.blog.common.annotation.Sensitive;
import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("帖子详情信息")
public class PostDetailVO {
    @ApiModelProperty("帖子ID")
    private Long postId;

    @ApiModelProperty("标题")
    @SensitiveFilter
    private String title;

    @ApiModelProperty("摘要")
    @SensitiveFilter
    private String summary;

    @ApiModelProperty("封面图URL")
    private String coverImage;

    @ApiModelProperty("内容")
    @SensitiveFilter
    private String content;

    @ApiModelProperty("浏览数")
    private Integer viewCount;

    @ApiModelProperty("评论数")
    private Integer commentCount;

    @ApiModelProperty("点赞数")
    private Integer likeCount;

    @ApiModelProperty("收藏数")
    private Integer collectCount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("分类信息")
    private Long categoryId;

    private CategoryVO category;

    @ApiModelProperty("标签列表")
    private List<TagVO> tags;

    @ApiModelProperty("是否已点赞")
    private Boolean isLiked;

    private Long userId;

    private String nickname;

    private String avatar;

    @ApiModelProperty("是否已收藏")
    private boolean collected;

    @ApiModelProperty("状态")
    private Integer postStatus;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("上架状态 0-正常 1-下线 2-下架")
    private Integer status;

    @ApiModelProperty("是否置顶")
    private String isTop;

    private List<PostAttachmentVO> attachments;
//    @ApiModelProperty("用户信息")
//    private SimpleUserVO userInfo;
}
