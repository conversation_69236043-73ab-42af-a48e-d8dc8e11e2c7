package com.blog.forum.domain.dto;

import com.blog.common.core.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("帖子列表查询参数")
public class PostListDTO extends PageDomain {
    @ApiModelProperty("分类ID")
    private Long categoryId;

    @ApiModelProperty("排序方式：latest-最新 hot-最热")
    private String sortType;

    @ApiModelProperty("搜索关键字")
    private String keyword;

    private String status;
}
