package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("帖子附件信息")
public class PostAttachmentDTO {
    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件URL")
    private String fileUrl;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件类型")
    private String fileType;
}
