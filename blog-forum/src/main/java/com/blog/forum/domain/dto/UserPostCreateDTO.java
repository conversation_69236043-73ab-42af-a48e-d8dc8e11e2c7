package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
@ApiModel("用户创建帖子dDTO")
public class UserPostCreateDTO {
    @ApiModelProperty(value = "内容", required = true)
    private String content;

    @ApiModelProperty(value = "分类id", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "标签集合")
    private List<Long> tagIds;

    @ApiModelProperty("图片列表")
    private List<String> images;

    @ApiModelProperty("用户id")
    private Long sysUserId;
}
