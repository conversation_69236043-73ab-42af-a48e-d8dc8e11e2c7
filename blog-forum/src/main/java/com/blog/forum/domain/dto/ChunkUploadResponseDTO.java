package com.blog.forum.domain.dto;

import lombok.Data;

/**
 * 分片上传响应数据传输对象
 */
@Data
public class ChunkUploadResponseDTO {
    /**
     * 是否跳过上传
     */
    private Boolean skipUpload;
    
    /**
     * 已上传的分片
     */
    private Integer[] uploadedChunks;
    
    /**
     * 文件访问URL（合并完成后）
     */
    private String fileUrl;
    
    /**
     * 上传进度（0-100）
     */
    private Integer progress;
    
    public ChunkUploadResponseDTO() {
        this.skipUpload = false;
        this.progress = 0;
    }
}
