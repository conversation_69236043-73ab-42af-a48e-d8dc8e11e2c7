package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 收藏对象 forum_collect
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumCollect extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 收藏ID */
    private Long collectId;

    /** 内容类型（0帖子 1用户发帖） */
    @Excel(name = "内容类型", readConverterExp = "0=帖子,1=用户发帖")
    private String contentType;

    /** 内容ID */
    @Excel(name = "内容ID")
    private Long contentId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    public void setCollectId(Long collectId)
    {
        this.collectId = collectId;
    }

    public Long getCollectId()
    {
        return collectId;
    }

    public void setContentType(String contentType)
    {
        this.contentType = contentType;
    }

    public String getContentType()
    {
        return contentType;
    }

    public void setContentId(Long contentId)
    {
        this.contentId = contentId;
    }

    public Long getContentId()
    {
        return contentId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("collectId", getCollectId())
            .append("contentType", getContentType())
            .append("contentId", getContentId())
            .append("userId", getUserId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
