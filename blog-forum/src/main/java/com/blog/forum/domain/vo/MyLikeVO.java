package com.blog.forum.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("�ҵĵ���VO")
public class MyLikeVO {
    @ApiModelProperty("����ID")
    private Long likeId;

    @ApiModelProperty("��������(0���� 1�û����� 2����)")
    private String contentType;

    @ApiModelProperty("����ID")
    private Long contentId;

    @ApiModelProperty("����/��������")
    private String content;

    @ApiModelProperty("����ʱ��")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("�û���Ϣ")
    private SimpleUserVO userInfo;
}
