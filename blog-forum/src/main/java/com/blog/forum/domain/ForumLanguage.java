package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 语言类型对象 forum_language
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumLanguage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 语言ID */
    private Long langId;

    /** 语言代码(如zh-CN, en-US) */
    @Excel(name = "语言代码(如zh-CN, en-US)")
    private String langCode;

    /** 语言名称 */
    @Excel(name = "语言名称")
    private String langName;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 状态（0启用 1停用） */
    @Excel(name = "状态", readConverterExp = "0=启用,1=停用")
    private String status;

    /** 是否默认语言（0否 1是） */
    @Excel(name = "是否默认语言", readConverterExp = "0=否,1=是")
    private String isDefault;

    public void setLangId(Long langId)
    {
        this.langId = langId;
    }

    public Long getLangId()
    {
        return langId;
    }

    public void setLangCode(String langCode)
    {
        this.langCode = langCode;
    }

    public String getLangCode()
    {
        return langCode;
    }

    public void setLangName(String langName)
    {
        this.langName = langName;
    }

    public String getLangName()
    {
        return langName;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setIsDefault(String isDefault)
    {
        this.isDefault = isDefault;
    }

    public String getIsDefault()
    {
        return isDefault;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("langId", getLangId())
            .append("langCode", getLangCode())
            .append("langName", getLangName())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("isDefault", getIsDefault())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
