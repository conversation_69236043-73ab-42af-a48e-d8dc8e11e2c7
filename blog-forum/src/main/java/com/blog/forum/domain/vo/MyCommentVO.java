package com.blog.forum.domain.vo;

import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("�ҵ�����VO")
public class MyCommentVO {
    @ApiModelProperty("����ID")
    private Long commentId;

    @ApiModelProperty("��������(0���� 1�û�����)")
    private String contentType;

    @ApiModelProperty("����ID")
    private Long contentId;

    @ApiModelProperty("����")
    private String title;

    @ApiModelProperty("��������")
    private String commentContent;

    @ApiModelProperty("������")
    private Integer likeCount;

    @ApiModelProperty("����ʱ��")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("�û���Ϣ")
    private SimpleUserVO userInfo;
}

