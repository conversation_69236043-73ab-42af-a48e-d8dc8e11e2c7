package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 标签多语言内容对象 forum_tag_i18n
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public class ForumTagI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 多语言ID */
    private Long i18nId;

    /** 标签ID */
    @Excel(name = "标签ID")
    private Long tagId;

    /** 语言ID */
    @Excel(name = "语言ID")
    private Long langId;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String tagName;

    public void setI18nId(Long i18nId)
    {
        this.i18nId = i18nId;
    }

    public Long getI18nId()
    {
        return i18nId;
    }

    public void setTagId(Long tagId)
    {
        this.tagId = tagId;
    }

    public Long getTagId()
    {
        return tagId;
    }

    public void setLangId(Long langId)
    {
        this.langId = langId;
    }

    public Long getLangId()
    {
        return langId;
    }

    public void setTagName(String tagName)
    {
        this.tagName = tagName;
    }

    public String getTagName()
    {
        return tagName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("i18nId", getI18nId())
            .append("tagId", getTagId())
            .append("langId", getLangId())
            .append("tagName", getTagName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
