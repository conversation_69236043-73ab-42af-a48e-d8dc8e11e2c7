package com.blog.forum.domain.vo;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

public class CusUserLoginVo {
    @NotBlank(message = "���䲻��Ϊ��")
    @Email(message = "�����ʽ����ȷ")
    private String email;

    @NotBlank(message = "���벻��Ϊ��")
    private String password;

    private String code;  // ��֤��
    private String uuid;  // ��֤��Ψһ��ʶ

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    // getters and setters
}
