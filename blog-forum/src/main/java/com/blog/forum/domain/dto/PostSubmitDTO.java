package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("帖子提交参数")
public class PostSubmitDTO {
    @ApiModelProperty("帖子ID，新增时为null")
    private Long postId;

    @ApiModelProperty("分类ID")
    @NotNull(message = "分类不能为空")
    private Long categoryId;

    @ApiModelProperty("标签ID列表")
    @NotEmpty(message = "至少需要一个标签")
    private List<Long> tagIds;

    @ApiModelProperty("标题")
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty("摘要")
    private String summary;

    @ApiModelProperty("封面图URL")
    private String coverImage;

    @ApiModelProperty("内容")
    @NotBlank(message = "内容不能为空")
    private String content;

    @ApiModelProperty("附件列表")
    private List<PostAttachmentDTO> attachments;
}
