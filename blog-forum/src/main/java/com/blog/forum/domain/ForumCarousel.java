package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 轮播图对象 forum_carousel
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumCarousel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 轮播图ID */
    private Long carouselId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 图片URL */
    @Excel(name = "图片URL")
    private String imageUrl;

    /** 点击跳转URL */
    @Excel(name = "点击跳转URL")
    private String clickUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 状态（0显示 1隐藏） */
    @Excel(name = "状态", readConverterExp = "0=显示,1=隐藏")
    private String status;

    public void setCarouselId(Long carouselId)
    {
        this.carouselId = carouselId;
    }

    public Long getCarouselId()
    {
        return carouselId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }

    public void setClickUrl(String clickUrl)
    {
        this.clickUrl = clickUrl;
    }

    public String getClickUrl()
    {
        return clickUrl;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("carouselId", getCarouselId())
            .append("title", getTitle())
            .append("imageUrl", getImageUrl())
            .append("clickUrl", getClickUrl())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
