package com.blog.forum.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("帖子附件信息")
public class PostAttachmentVO {
    @ApiModelProperty("附件ID")
    private Long attachmentId;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件URL")
    private String fileUrl;

    @ApiModelProperty("文件大小(字节)")
    private Long fileSize;

    @ApiModelProperty("文件类型")
    private String fileType;
}
