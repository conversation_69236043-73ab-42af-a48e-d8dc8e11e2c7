package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel("�û���Ϣ����DTO")
public class UserInfoUpdateDTO {
    @ApiModelProperty(value = "�û��ǳ�", required = true)
    @NotBlank(message = "�ǳƲ���Ϊ��")
    @Size(min = 1, max = 20, message = "�ǳƳ��ȱ�����1-20���ַ�֮��")
    private String nickname;

    @ApiModelProperty(value = "�û�ͷ��URL")
    private String avatar;

    @ApiModelProperty(value = "���˼��")
    @Size(max = 200, message = "���˼�鲻�ܳ���200���ַ�")
    private String bio;
}

