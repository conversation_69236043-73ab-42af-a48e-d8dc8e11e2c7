package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ��̨���۲�ѯDTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("��̨���۲�ѯDTO")
public class AdminCommentQueryDTO {
    
    @ApiModelProperty("�û��ǳ�")
    private String nickname;
    
    @ApiModelProperty("��������")
    private String content;
    
    @ApiModelProperty("������ID��0��ʾ��ѯһ�����ۣ�")
    private Long parentId = 0L;
    
    @ApiModelProperty("�������ͣ�0���� 1�û�������")
    private String contentType;
    
    @ApiModelProperty("����ID")
    private Long contentId;
}
