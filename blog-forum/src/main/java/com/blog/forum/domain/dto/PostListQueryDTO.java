package com.blog.forum.domain.dto;

import java.util.List;
import lombok.Data;

/**
 * �����б��ѯ����DTO
 *
 * <AUTHOR>
 */
@Data
public class PostListQueryDTO {
    
    /** ҳ�� */
    private Integer pageNum;
    
    /** ÿҳ���� */
    private Integer pageSize;
    
    /** ��ǩIDs */
    private List<Long> tagIds;
    
    /** �û��ǳƻ��û��� */
    private String userName;
    
    /** ����ID */
    private Long categoryId;
    
    /** ����״̬��0�ݸ� 1�ѷ��� 2����� 3��˲�ͨ���� */
    private String postStatus;
    
    /** �Ƿ��û����� */
    private Boolean isUserPost;
    
    /** ���ݹؼ��� */
    private String keyword;
    
    /** ϵͳ�û�ID */
    private Long sysUserId;

    private Integer status;

    /** */
    private String excludeStatus;
}

