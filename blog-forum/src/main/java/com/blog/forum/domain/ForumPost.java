package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.annotation.SensitiveFilter;
import com.blog.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 帖子对象 forum_post
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public class ForumPost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 帖子ID */
    private Long postId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 标题 */
    @Excel(name = "标题")
    @SensitiveFilter
    private String title;

    /** 摘要 */
    @Excel(name = "摘要")
    @SensitiveFilter
    private String summary;

    /** 封面图 */
    @Excel(name = "封面图")
    private String coverImage;

    /** 内容（富文本） */
    @Excel(name = "内容", readConverterExp = "富=文本")
    @SensitiveFilter
    private String content;

    /** 浏览数 */
    @Excel(name = "浏览数")
    private Long viewCount;

    /** 评论数 */
    @Excel(name = "评论数")
    private Long commentCount;

    /** 点赞数 */
    @Excel(name = "点赞数")
    private Long likeCount;

    /** 收藏数 */
    @Excel(name = "收藏数")
    private Long collectCount;

    /** 状态（0正常 1下线） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=下线")
    private String status;

    /** 是否置顶（0否 1是） */
    @Excel(name = "是否置顶", readConverterExp = "0=否,1=是")
    private String isTop;

    private Integer forumPost;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    private Long langId;

    private Long sysUserId;

    private String isUserPost;

    private String postStatus;

    /** 标签列表（非数据库字段） */
    private List<Long> tagIds;

    /** 附件列表（非数据库字段） */
    private List<ForumPostAttachment> attachments;

    public List<Long> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    public List<ForumPostAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<ForumPostAttachment> attachments) {
        this.attachments = attachments;
    }

    public Long getLangId() {
        return langId;
    }

    public void setLangId(Long langId) {
        this.langId = langId;
    }

    public Long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(Long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getIsUserPost() {
        return isUserPost;
    }

    public void setIsUserPost(String isUserPost) {
        this.isUserPost = isUserPost;
    }

    public String getPostStatus() {
        return postStatus;
    }

    public void setPostStatus(String postStatus) {
        this.postStatus = postStatus;
    }

    public Integer getForumPost() {
        return forumPost;
    }

    public void setForumPost(Integer forumPost) {
        this.forumPost = forumPost;
    }

    private List<ForumTagI18n> forumTagI18nList;


    public List<ForumTagI18n> getForumTagI18nList() {
        return forumTagI18nList;
    }

    public void setForumTagI18nList(List<ForumTagI18n> forumTagI18nList) {
        this.forumTagI18nList = forumTagI18nList;
    }

    public void setPostId(Long postId)
    {
        this.postId = postId;
    }

    public Long getPostId()
    {
        return postId;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public void setSummary(String summary)
    {
        this.summary = summary;
    }

    public String getSummary()
    {
        return summary;
    }

    public void setCoverImage(String coverImage)
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage()
    {
        return coverImage;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setViewCount(Long viewCount)
    {
        this.viewCount = viewCount;
    }

    public Long getViewCount()
    {
        return viewCount;
    }

    public void setCommentCount(Long commentCount)
    {
        this.commentCount = commentCount;
    }

    public Long getCommentCount()
    {
        return commentCount;
    }

    public void setLikeCount(Long likeCount)
    {
        this.likeCount = likeCount;
    }

    public Long getLikeCount()
    {
        return likeCount;
    }

    public void setCollectCount(Long collectCount)
    {
        this.collectCount = collectCount;
    }

    public Long getCollectCount()
    {
        return collectCount;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setIsTop(String isTop)
    {
        this.isTop = isTop;
    }

    public String getIsTop()
    {
        return isTop;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("postId", getPostId())
            .append("categoryId", getCategoryId())
            .append("title", getTitle())
            .append("summary", getSummary())
            .append("coverImage", getCoverImage())
            .append("content", getContent())
            .append("viewCount", getViewCount())
            .append("commentCount", getCommentCount())
            .append("likeCount", getLikeCount())
            .append("collectCount", getCollectCount())
            .append("status", getStatus())
            .append("isTop", getIsTop())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
