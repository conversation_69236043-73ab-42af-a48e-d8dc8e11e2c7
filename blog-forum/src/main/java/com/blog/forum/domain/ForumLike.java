package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 点赞对象 forum_like
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumLike extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 点赞ID */
    private Long likeId;

    /** 内容类型（0帖子 1用户发帖 2评论） */
    @Excel(name = "内容类型", readConverterExp = "0=帖子,1=用户发帖,2=评论")
    private String contentType;

    /** 内容ID */
    @Excel(name = "内容ID")
    private Long contentId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    public void setLikeId(Long likeId)
    {
        this.likeId = likeId;
    }

    public Long getLikeId()
    {
        return likeId;
    }

    public void setContentType(String contentType)
    {
        this.contentType = contentType;
    }

    public String getContentType()
    {
        return contentType;
    }

    public void setContentId(Long contentId)
    {
        this.contentId = contentId;
    }

    public Long getContentId()
    {
        return contentId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("likeId", getLikeId())
            .append("contentType", getContentType())
            .append("contentId", getContentId())
            .append("userId", getUserId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
