package com.blog.forum.domain.vo;

import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
@Data
@ApiModel("评论详情VO")
public class CommentDetailVO {
    @ApiModelProperty("评论ID")
    private Long commentId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户昵称")
    private String nickname;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty("父评论ID")
    private Long parentId;

    @ApiModelProperty("评论内容")
    @SensitiveFilter
    private String content;

    @ApiModelProperty("点赞数")
    private Integer likeCount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("是否已点赞")
    private Boolean liked;

    @ApiModelProperty("回复数量")
    private Integer replyCount;

    @ApiModelProperty("帖子标题")
    @SensitiveFilter
    private String postTitle;

    @ApiModelProperty("帖子内容")
    @SensitiveFilter
    private String postContent;

    @ApiModelProperty("是否用户发帖(0-否 1-是)")
    private String isUserPost;

    @ApiModelProperty("帖子ID")
    private Long postId;

    @ApiModelProperty("回复列表")
    private List<CommentDetailVO> replies;

    @ApiModelProperty("附件列表")
    private List<PostAttachmentVO> attachments;
}
