package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 用户发帖图片对象 forum_user_post_image
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public class ForumUserPostImage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 图片ID */
    private Long imageId;

    /** 用户发帖ID */
    @Excel(name = "用户发帖ID")
    private Long userPostId;

    /** 图片URL */
    @Excel(name = "图片URL")
    private String imageUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    public void setImageId(Long imageId)
    {
        this.imageId = imageId;
    }

    public Long getImageId()
    {
        return imageId;
    }

    public void setUserPostId(Long userPostId)
    {
        this.userPostId = userPostId;
    }

    public Long getUserPostId()
    {
        return userPostId;
    }

    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("imageId", getImageId())
            .append("userPostId", getUserPostId())
            .append("imageUrl", getImageUrl())
            .append("sort", getSort())
            .append("createTime", getCreateTime())
            .toString();
    }
}
