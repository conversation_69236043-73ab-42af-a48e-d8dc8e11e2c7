package com.blog.forum.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("评论DTO")
public class CommentDTO {
    @ApiModelProperty(value = "内容类型（0帖子 1用户发帖）", required = true)
    @NotNull(message = "内容类型不能为空")
    private String contentType;

    @ApiModelProperty(value = "内容ID", required = true)
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    @ApiModelProperty(value = "父评论ID（0表示一级评论）", required = true)
    @NotNull(message = "父评论ID不能为空")
    private Long parentId;

    @ApiModelProperty(value = "评论内容", required = true)
    @NotBlank(message = "评论内容不能为空")
    private String content;

    @ApiModelProperty(value = "附件列表")
    private List<PostAttachmentDTO> attachments;
}
