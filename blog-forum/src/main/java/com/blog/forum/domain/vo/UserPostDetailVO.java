package com.blog.forum.domain.vo;

import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class UserPostDetailVO {
    private Long postId;

    private Long userId;

    private String nickname;

    private String avatar;

    @SensitiveFilter
    private String title;

    @SensitiveFilter
    private String summary;

    private String coverImage;

    @SensitiveFilter
    private String content;

    private Integer viewCount;

    private Integer commentCount;

    private Integer likeCount;

    private Integer collectCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Long categoryId;

    private CategoryVO category;

    private List<TagVO> tags;

    private Boolean isLiked;

    private Boolean collected;

    private List<PostAttachmentVO> attachments;
}
