package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 多语言帖子内容对象 forum_post_i18n_content
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumPostI18nContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 内容ID */
    private Long contentId;

    /** 帖子ID */
    @Excel(name = "帖子ID")
    private Long postId;

    /** 语言ID */
    @Excel(name = "语言ID")
    private Long langId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 摘要 */
    @Excel(name = "摘要")
    private String summary;

    /** 内容（富文本） */
    @Excel(name = "内容", readConverterExp = "富=文本")
    private String content;

    public void setContentId(Long contentId)
    {
        this.contentId = contentId;
    }

    public Long getContentId()
    {
        return contentId;
    }

    public void setPostId(Long postId)
    {
        this.postId = postId;
    }

    public Long getPostId()
    {
        return postId;
    }

    public void setLangId(Long langId)
    {
        this.langId = langId;
    }

    public Long getLangId()
    {
        return langId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public void setSummary(String summary)
    {
        this.summary = summary;
    }

    public String getSummary()
    {
        return summary;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("contentId", getContentId())
            .append("postId", getPostId())
            .append("langId", getLangId())
            .append("title", getTitle())
            .append("summary", getSummary())
            .append("content", getContent())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
