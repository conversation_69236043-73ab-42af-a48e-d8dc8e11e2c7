package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 用户发帖对象 forum_user_post
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public class ForumUserPost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long userPostId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;


    private Long categoryId;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 浏览数 */
    @Excel(name = "浏览数")
    private Long viewCount;

    /** 评论数 */
    @Excel(name = "评论数")
    private Long commentCount;

    /** 点赞数 */
    @Excel(name = "点赞数")
    private Long likeCount;

    /** 状态（0显示 1隐藏） */
    @Excel(name = "状态", readConverterExp = "0=显示,1=隐藏")
    private String status;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public void setUserPostId(Long userPostId)
    {
        this.userPostId = userPostId;
    }

    public Long getUserPostId()
    {
        return userPostId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setViewCount(Long viewCount)
    {
        this.viewCount = viewCount;
    }

    public Long getViewCount()
    {
        return viewCount;
    }

    public void setCommentCount(Long commentCount)
    {
        this.commentCount = commentCount;
    }

    public Long getCommentCount()
    {
        return commentCount;
    }

    public void setLikeCount(Long likeCount)
    {
        this.likeCount = likeCount;
    }

    public Long getLikeCount()
    {
        return likeCount;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userPostId", getUserPostId())
            .append("userId", getUserId())
            .append("content", getContent())
            .append("viewCount", getViewCount())
            .append("commentCount", getCommentCount())
            .append("likeCount", getLikeCount())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
