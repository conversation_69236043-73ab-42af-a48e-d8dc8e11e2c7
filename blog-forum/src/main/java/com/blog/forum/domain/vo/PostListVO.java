package com.blog.forum.domain.vo;

import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("帖子列表信息")
public class PostListVO {
    @ApiModelProperty("帖子ID")
    private Long postId;

    @ApiModelProperty("标题")
    @SensitiveFilter
    private String title;

    @ApiModelProperty("摘要")
    @SensitiveFilter
    private String summary;

    @ApiModelProperty("封面图URL")
    private String coverImage;

    @ApiModelProperty("浏览数")
    private Integer viewCount;

    @ApiModelProperty("评论数")
    private Integer commentCount;

    @ApiModelProperty("点赞数")
    private Integer likeCount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("分类信息")
    private CategoryVO category;

    @ApiModelProperty("标签列表")
    private List<TagVO> tags;

    @ApiModelProperty("是否已点赞")
    private Boolean isLiked;
}
