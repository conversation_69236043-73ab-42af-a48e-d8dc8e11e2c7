package com.blog.forum.domain.vo;

import com.blog.common.annotation.SensitiveFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostVO {
    private Long postId;
    private Long categoryId;
    private String categoryName;
    @SensitiveFilter
    private String title;
    @SensitiveFilter
    private String summary;
    private String coverImage;
    private Integer viewCount;
    private Integer commentCount;
    private Integer likeCount;
    private String status;
    private String postStatus;
    private String isTop;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private List<TagVO> tags;
    private List<AttachmentVO> attachments;
}

