package com.blog.forum.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.blog.common.annotation.Excel;
import com.blog.common.core.domain.BaseEntity;

/**
 * 分类多语言内容对象 forum_category_i18n
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public class ForumCategoryI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 多语言ID */
    private Long i18nId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 语言ID */
    @Excel(name = "语言ID")
    private Long langId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String categoryName;

    public void setI18nId(Long i18nId)
    {
        this.i18nId = i18nId;
    }

    public Long getI18nId()
    {
        return i18nId;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setLangId(Long langId)
    {
        this.langId = langId;
    }

    public Long getLangId()
    {
        return langId;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("i18nId", getI18nId())
            .append("categoryId", getCategoryId())
            .append("langId", getLangId())
            .append("categoryName", getCategoryName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
