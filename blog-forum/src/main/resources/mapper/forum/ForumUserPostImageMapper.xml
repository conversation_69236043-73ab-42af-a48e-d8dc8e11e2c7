<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumUserPostImageMapper">

    <resultMap type="ForumUserPostImage" id="ForumUserPostImageResult">
        <result property="imageId"    column="image_id"    />
        <result property="userPostId"    column="user_post_id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectForumUserPostImageVo">
        select image_id, user_post_id, image_url, sort, create_time from forum_user_post_image
    </sql>

    <select id="selectForumUserPostImageList" parameterType="ForumUserPostImage" resultMap="ForumUserPostImageResult">
        <include refid="selectForumUserPostImageVo"/>
        <where>
            <if test="userPostId != null "> and user_post_id = #{userPostId}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectForumUserPostImageByImageId" parameterType="Long" resultMap="ForumUserPostImageResult">
        <include refid="selectForumUserPostImageVo"/>
        where image_id = #{imageId}
    </select>

    <insert id="insertForumUserPostImage" parameterType="ForumUserPostImage" useGeneratedKeys="true" keyProperty="imageId">
        insert into forum_user_post_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userPostId != null">user_post_id,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userPostId != null">#{userPostId},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateForumUserPostImage" parameterType="ForumUserPostImage">
        update forum_user_post_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="userPostId != null">user_post_id = #{userPostId},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deleteForumUserPostImageByImageId" parameterType="Long">
        delete from forum_user_post_image where image_id = #{imageId}
    </delete>

    <delete id="deleteForumUserPostImageByImageIds" parameterType="String">
        delete from forum_user_post_image where image_id in
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <insert id="batchInsert">
        INSERT INTO forum_user_post_image (
        user_post_id,
        oss_id,
        image_url,
        sort,
        create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userPostId},
            #{item.ossId},
            #{item.imageUrl},
            #{item.sort},
            #{item.createTime}
            )
        </foreach>
    </insert>
</mapper>
