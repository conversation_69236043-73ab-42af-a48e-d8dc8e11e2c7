<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumTagI18nMapper">

    <resultMap type="ForumTagI18n" id="ForumTagI18nResult">
        <result property="i18nId"    column="i18n_id"    />
        <result property="tagId"    column="tag_id"    />
        <result property="langId"    column="lang_id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumTagI18nVo">
        select i18n_id, tag_id, lang_id, tag_name, create_time, update_time from forum_tag_i18n
    </sql>

    <select id="selectForumTagI18nList" parameterType="ForumTagI18n" resultMap="ForumTagI18nResult">
        <include refid="selectForumTagI18nVo"/>
        <where>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
            <if test="langId != null "> and lang_id = #{langId}</if>
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
        </where>
    </select>

    <select id="selectForumTagI18nByI18nId" parameterType="Long" resultMap="ForumTagI18nResult">
        <include refid="selectForumTagI18nVo"/>
        where i18n_id = #{i18nId}
    </select>
    <select id="selectTagsByIds" resultType="com.blog.forum.domain.ForumTagI18n">
        select
            *
        from
            forum_tag_i18n
        where
            lang_id = #{langId}
        and tag_id
        <foreach item="i18nId" collection="array" open="(" separator="," close=")">
            #{i18nId}
        </foreach>
        and status = 0
    </select>

    <insert id="insertForumTagI18n" parameterType="ForumTagI18n" useGeneratedKeys="true" keyProperty="i18nId">
        insert into forum_tag_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagId != null">tag_id,</if>
            <if test="langId != null">lang_id,</if>
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagId != null">#{tagId},</if>
            <if test="langId != null">#{langId},</if>
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumTagI18n" parameterType="ForumTagI18n">
        update forum_tag_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where i18n_id = #{i18nId}
    </update>

    <delete id="deleteForumTagI18nByI18nId" parameterType="Long">
        delete from forum_tag_i18n where i18n_id = #{i18nId}
    </delete>

    <delete id="deleteForumTagI18nByI18nIds" parameterType="String">
        delete from forum_tag_i18n where i18n_id in
        <foreach item="i18nId" collection="array" open="(" separator="," close=")">
            #{i18nId}
        </foreach>
    </delete>
</mapper>
