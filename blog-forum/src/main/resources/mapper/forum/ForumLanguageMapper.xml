<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumLanguageMapper">

    <resultMap type="ForumLanguage" id="ForumLanguageResult">
        <result property="langId"    column="lang_id"    />
        <result property="langCode"    column="lang_code"    />
        <result property="langName"    column="lang_name"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumLanguageVo">
        select lang_id, lang_code, lang_name, sort, status, is_default, create_time, update_time from forum_language
    </sql>

    <select id="selectForumLanguageList" parameterType="ForumLanguage" resultMap="ForumLanguageResult">
        <include refid="selectForumLanguageVo"/>
        <where>
            <if test="langCode != null  and langCode != ''"> and lang_code = #{langCode}</if>
            <if test="langName != null  and langName != ''"> and lang_name like concat('%', #{langName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isDefault != null  and isDefault != ''"> and is_default = #{isDefault}</if>
        </where>
    </select>

    <select id="selectForumLanguageByLangId" parameterType="Long" resultMap="ForumLanguageResult">
        <include refid="selectForumLanguageVo"/>
        where lang_id = #{langId}
    </select>
    <select id="selectEnabledLanguageList" resultMap="ForumLanguageResult">
        select * from forum_language where status = '0'
    </select>

    <insert id="insertForumLanguage" parameterType="ForumLanguage" useGeneratedKeys="true" keyProperty="langId">
        insert into forum_language
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="langCode != null and langCode != ''">lang_code,</if>
            <if test="langName != null and langName != ''">lang_name,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="langCode != null and langCode != ''">#{langCode},</if>
            <if test="langName != null and langName != ''">#{langName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumLanguage" parameterType="ForumLanguage">
        update forum_language
        <trim prefix="SET" suffixOverrides=",">
            <if test="langCode != null and langCode != ''">lang_code = #{langCode},</if>
            <if test="langName != null and langName != ''">lang_name = #{langName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where lang_id = #{langId}
    </update>

    <delete id="deleteForumLanguageByLangId" parameterType="Long">
        delete from forum_language where lang_id = #{langId}
    </delete>

    <delete id="deleteForumLanguageByLangIds" parameterType="String">
        delete from forum_language where lang_id in
        <foreach item="langId" collection="array" open="(" separator="," close=")">
            #{langId}
        </foreach>
    </delete>
</mapper>
