<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumPostAttachmentMapper">

    <resultMap type="ForumPostAttachment" id="ForumPostAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="postId"    column="post_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="contentType"    column="content_type"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectForumPostAttachmentVo">
        select attachment_id, post_id, file_name, file_url, file_size, file_type, content_type, create_time from forum_post_attachment
    </sql>

    <select id="selectForumPostAttachmentList" parameterType="ForumPostAttachment" resultMap="ForumPostAttachmentResult">
        <include refid="selectForumPostAttachmentVo"/>
        <where>
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="contentType != null  and contentType != ''"> and content_type = #{contentType}</if>
        </where>
    </select>

    <select id="selectForumPostAttachmentByAttachmentId" parameterType="Long" resultMap="ForumPostAttachmentResult">
        <include refid="selectForumPostAttachmentVo"/>
        where attachment_id = #{attachmentId}
    </select>

    <insert id="insertForumPostAttachment" parameterType="ForumPostAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into forum_post_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="contentType != null">content_type,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateForumPostAttachment" parameterType="ForumPostAttachment">
        update forum_post_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where attachment_id = #{attachmentId}
    </update>

    <delete id="deleteForumPostAttachmentByAttachmentId" parameterType="Long">
        delete from forum_post_attachment where attachment_id = #{attachmentId}
    </delete>

    <delete id="deleteForumPostAttachmentByAttachmentIds" parameterType="String">
        delete from forum_post_attachment where attachment_id in
        <foreach item="attachmentId" collection="array" open="(" separator="," close=")">
            #{attachmentId}
        </foreach>
    </delete>

    <resultMap id="PostAttachmentResult" type="PostAttachmentVO">
        <id property="attachmentId" column="attachment_id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
    </resultMap>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="attachmentId">
        INSERT INTO forum_post_attachment (
        post_id, content_type, file_name,
        file_url, file_size, file_type, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.postId}, #{item.contentType}, #{item.fileName},
            #{item.fileUrl}, #{item.fileSize}, #{item.fileType}, #{item.createTime})
        </foreach>
    </insert>
    <select id="selectAttachmentsByPostId" resultMap="PostAttachmentResult">
        SELECT
            attachment_id, file_name, file_url,
            file_size, file_type
        FROM forum_post_attachment
        WHERE post_id = #{postId} AND content_type = '1'
    </select>

    <select id="selectAttachmentsByCommentId" parameterType="Long" resultMap="PostAttachmentResult">
        SELECT
            attachment_id,
            file_name,
            file_url,
            file_size,
            file_type
        FROM forum_post_attachment
        WHERE post_id = #{commentId} AND content_type = '2'
        ORDER BY create_time ASC
    </select>

    <delete id="deleteByCommentId" parameterType="Long">
        DELETE FROM forum_post_attachment WHERE post_id = #{commentId} AND content_type = '2'
    </delete>

</mapper>
