<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumPostCategoryMapper">

    <resultMap type="ForumPostCategory" id="ForumPostCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectForumPostCategoryVo">
        select c.category_id,
               IFNULL(i18n.category_name, c.category_name) as category_name,
               c.sort, c.status, c.create_by, c.create_time, c.update_by, c.update_time, c.remark
        from forum_post_category c
        left join forum_category_i18n i18n on c.category_id = i18n.category_id and i18n.lang_id = #{langId}
    </sql>

    <select id="selectForumPostCategoryList" resultMap="ForumPostCategoryResult">
        <include refid="selectForumPostCategoryVo"/>
        <where>
            <if test="forumPostCategory.categoryName != null  and forumPostCategory.categoryName != ''"> and c.category_name like concat('%', #{forumPostCategory.categoryName}, '%')</if>
            <if test="forumPostCategory.sort != null "> and c.sort = #{forumPostCategory.sort}</if>
            <if test="forumPostCategory.status != null  and forumPostCategory.status != ''"> and c.status = #{forumPostCategory.status}</if>
        </where>
    </select>

    <select id="selectForumPostCategoryByCategoryId" resultMap="ForumPostCategoryResult">
        <include refid="selectForumPostCategoryVo"/>
        where c.status = '0' and c.category_id = #{categoryId}
    </select>

    <insert id="insertForumPostCategory" parameterType="ForumPostCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into forum_post_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateForumPostCategory" parameterType="ForumPostCategory">
        update forum_post_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteForumPostCategoryByCategoryId" parameterType="Long">
        delete from forum_post_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteForumPostCategoryByCategoryIds" parameterType="String">
        delete from forum_post_category where category_id in
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <resultMap id="CategoryVOResult" type="com.blog.forum.domain.vo.CategoryVO">
        <result property="categoryId" column="category_id" ></result>
        <result property="categoryName" column="category_name" ></result>
        <result property="sort" column="sort" ></result>
        <result property="status" column="status" ></result>
        <result property="langId" column="lang_id" ></result>
        <result property="postCount" column="post_count" ></result>
    </resultMap>

    <resultMap id="ForumPostCategoryResult1" type="ForumPostCategory">
        <id property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectForumPostCategoryVo1">
        SELECT
            category_id,
            category_name,
            sort,
            status,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        FROM forum_post_category
    </sql>
    <select id="selectCategoryListAll" parameterType="ForumPostCategory" resultMap="ForumPostCategoryResult1">
        <include refid="selectForumPostCategoryVo1"/>
        <where>
            status = '0'
            <if test="categoryName != null and categoryName != ''">
                and c.category_name like CONCAT('%', #{categoryName}, '%')
            </if>
            <if test="status != null and status != ''">
                and c.status = #{status}
            </if>
        </where>
        ORDER BY sort ASC
    </select>
    <select id="selectCategoryById" parameterType="Long" resultMap="ForumPostCategoryResult1">
        <include refid="selectForumPostCategoryVo1"/>
        WHERE category_id = #{categoryId}
    </select>

    <select id="selectCategoryListVo" resultType="CategoryVO">
        select c.category_id, ci.category_name
        from forum_post_category c
                 join forum_category_i18n ci on c.category_id = ci.category_id and ci.lang_id = #{langId}
        where c.status = '0'
        order by c.sort
    </select>

    <select id="selectCategoryList" resultMap="CategoryVOResult">
        SELECT
            c.category_id,
            COALESCE(ci.category_name, c.category_name) AS category_name,
            c.sort,
            c.status,
            #{langId} AS lang_id,
            COALESCE(pc.post_count, 0) AS post_count
        FROM forum_post_category c
                 LEFT JOIN forum_category_i18n ci ON c.category_id = ci.category_id AND ci.lang_id = #{langId}
                 LEFT JOIN (
                     SELECT category_id, COUNT(*) as post_count
                     FROM forum_post
                     WHERE status != '1'
                     GROUP BY category_id
                 ) pc ON c.category_id = pc.category_id
        WHERE c.status = '0'
        ORDER BY c.sort ASC
    </select>
</mapper>
