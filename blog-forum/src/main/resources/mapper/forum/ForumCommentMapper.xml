<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumCommentMapper">

    <resultMap type="ForumComment" id="ForumCommentResult">
        <result property="commentId"    column="comment_id"    />
        <result property="contentType"    column="content_type"    />
        <result property="contentId"    column="content_id"    />
        <result property="userId"    column="user_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="content"    column="content"    />
        <result property="likeCount"    column="like_count"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumCommentVo">
        select comment_id, content_type, content_id, user_id, parent_id, content, like_count, status, create_time, update_time from forum_comment
    </sql>

    <select id="selectForumCommentList" parameterType="ForumComment" resultMap="ForumCommentResult">
        <include refid="selectForumCommentVo"/>
        <where>
            <if test="contentType != null  and contentType != ''"> and content_type = #{contentType}</if>
            <if test="contentId != null "> and content_id = #{contentId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectForumCommentByCommentId" parameterType="Long" resultMap="ForumCommentResult">
        <include refid="selectForumCommentVo"/>
        where comment_id = #{commentId}
    </select>

    <insert id="insertForumComment" parameterType="ForumComment" useGeneratedKeys="true" keyProperty="commentId">
        insert into forum_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type,</if>
            <if test="contentId != null">content_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">#{contentType},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumComment" parameterType="ForumComment">
        update forum_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type = #{contentType},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where comment_id = #{commentId}
    </update>

    <delete id="deleteForumCommentByCommentId" parameterType="Long">
        delete from forum_comment where comment_id = #{commentId}
    </delete>

    <delete id="deleteForumCommentByCommentIds" parameterType="String">
        delete from forum_comment where comment_id in
        <foreach item="commentId" collection="array" open="(" separator="," close=")">
            #{commentId}
        </foreach>
    </delete>

    <resultMap id="CommentDetailVOResult" type="CommentDetailVO">
        <id property="commentId" column="comment_id"/>
        <result property="userId" column="user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="parentId" column="parent_id"/>
        <result property="content" column="content"/>
        <result property="likeCount" column="like_count"/>
        <result property="createTime" column="create_time"/>
        <result property="liked" column="liked"/>
        <result property="replyCount" column="reply_count"/>
        <result property="postTitle" column="post_title"/>
        <result property="postContent" column="post_content"/>
        <result property="postId" column="post_id"/>
        <result property="isUserPost" column="is_user_post"/>
<!--        <collection property="replies" ofType="CommentDetailVO" select="selectReplies"-->
<!--                    column="{contentType=content_type,contentId=content_id,commentId=comment_id,userId=current_user_id,createTime=create_time,parentId=parent_id}"/>-->
    </resultMap>
    <sql id="selectCommentDetailVo">
        SELECT
            c.comment_id,
            c.user_id,
            u.nickname,
            u.avatar,
            c.parent_id,
            c.content,
            c.like_count,
            c.create_time,
            c.content_type,
            c.content_id,
            #{userId} as current_user_id,
            EXISTS(
                SELECT 1 FROM forum_like l
                WHERE l.content_type = '2'
                  AND l.content_id = c.comment_id
                  AND l.user_id = #{userId}
            ) as liked
        FROM forum_comment c
                 LEFT JOIN forum_user_extend u ON c.user_id = u.user_id
    </sql>
    <select id="selectCommentList" resultMap="CommentDetailVOResult">
        <include refid="selectCommentDetailVo"/>
        WHERE c.content_type = #{contentType}
        AND c.content_id = #{contentId}
        AND c.status = '0'
        AND c.parent_id = 0
        ORDER BY c.create_time DESC
    </select>
    <select id="selectReplies" resultMap="CommentDetailVOResult">
        <include refid="selectCommentDetailVo"/>
        WHERE c.content_type = #{contentType}
        AND c.content_id = #{contentId}
        AND c.status = '0'
        AND (c.parent_id = #{commentId} OR (
        SELECT parent_id FROM forum_comment
        WHERE comment_id = c.parent_id
        ) = #{commentId})
        ORDER BY c.create_time desc
    </select>
    <insert id="insertComment" parameterType="ForumComment" useGeneratedKeys="true" keyProperty="commentId">
        INSERT INTO forum_comment (
            content_type,
            content_id,
            user_id,
            parent_id,
            content,
            like_count,
            status,
            create_time,
            update_time
        ) VALUES (
                     #{contentType},
                     #{contentId},
                     #{userId},
                     #{parentId},
                     #{content},
                     #{likeCount},
                     #{status},
                     #{createTime},
                     #{updateTime}
                 )
    </insert>
    <update id="updateCommentStatus">
        UPDATE forum_comment
        SET status = #{status},
            update_time = now()
        WHERE comment_id = #{commentId}
          AND user_id = #{userId}
    </update>
    <select id="selectCommentById" parameterType="Long" resultType="ForumComment">
        SELECT * FROM forum_comment WHERE comment_id = #{commentId}
    </select>
    <update id="incrementLikeCount">
        UPDATE forum_comment
        SET like_count = like_count + #{increment}
        WHERE comment_id = #{commentId}
    </update>

    <resultMap id="MyCommentResult" type="MyCommentVO">
        <id property="commentId" column="comment_id"/>
        <result property="contentType" column="content_type"/>
        <result property="contentId" column="content_id"/>
        <result property="title" column="title"/>
        <result property="commentContent" column="content"/>
        <result property="likeCount" column="like_count"/>
        <result property="createTime" column="create_time"/>
        <association property="userInfo" javaType="SimpleUserVO">
            <result property="userId" column="user_id"/>
            <result property="nickname" column="nickname"/>
            <result property="avatar" column="avatar"/>
        </association>
    </resultMap>
    <select id="selectUserCommentList" resultMap="MyCommentResult">
        SELECT
            c.comment_id, c.content_type, c.content_id,
            c.content, c.like_count, c.create_time,
            CASE
                WHEN c.content_type = '0' THEN pi.title
                WHEN c.content_type = '1' THEN ipi.title
                END AS title,
            u.user_id, u.nickname, u.avatar
        FROM forum_comment c
                 LEFT JOIN forum_post p ON c.content_type = '0' AND c.content_id = p.post_id
                 LEFT JOIN forum_post ip ON c.content_type = '1' AND c.content_id = ip.post_id
                 LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id AND pi.lang_id = #{langId} AND pi.status = '0'
                 LEFT JOIN forum_post_i18n_content ipi ON ip.post_id = ipi.post_id AND ipi.lang_id = #{langId} AND ipi.status = '0'
                 LEFT JOIN forum_user_extend u ON u.user_id = c.user_id
        WHERE c.user_id = #{userId} AND c.status = '0'
        ORDER BY c.create_time DESC
    </select>
    <select id="selectUserCommentCount" resultType="int">
        SELECT COUNT(*) FROM forum_comment
        WHERE user_id = #{userId} AND status = '0'
    </select>

    <!-- 查询后台评论列表 -->
    <select id="selectAdminCommentList" parameterType="com.blog.forum.domain.dto.AdminCommentQueryDTO" resultMap="CommentDetailVOResult">
        SELECT
            c.comment_id,
            c.user_id,
            u.nickname,
            u.avatar,
            c.parent_id,
            c.content,
            c.like_count,
            c.create_time,
            c.content_type,
            c.content_id,
            0 as current_user_id,
            0 as liked,
            p.title as post_title,
            p.content as post_content,
            p.post_id as post_id,
            p.is_user_post as is_user_post,
            (SELECT COUNT(*) FROM forum_comment rc WHERE rc.parent_id = c.comment_id AND rc.status = '0') as reply_count
        FROM forum_comment c
        LEFT JOIN forum_user_extend u ON c.user_id = u.user_id
        LEFT JOIN forum_post p ON (c.content_type = '0' AND c.content_id = p.post_id)
        <where>
            c.status = '0'
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
            </if>
            <if test="contentType != null and contentType != ''">
                AND c.content_type = #{contentType}
            </if>
            <if test="contentId != null">
                AND c.content_id = #{contentId}
            </if>
            <if test="nickname != null and nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <if test="content != null and content != ''">
                AND c.content LIKE CONCAT('%', #{content}, '%')
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 查询评论的回复列表 -->
    <select id="selectCommentReplies" parameterType="Long" resultMap="CommentDetailVOResult">
        SELECT
            c.comment_id,
            c.user_id,
            u.nickname,
            u.avatar,
            c.parent_id,
            c.content,
            c.like_count,
            c.create_time,
            c.content_type,
            c.content_id,
            0 as current_user_id,
            0 as liked,
            pc.content as parent_content
        FROM forum_comment c
        LEFT JOIN forum_user_extend u ON c.user_id = u.user_id
        LEFT JOIN forum_comment pc ON c.parent_id = pc.comment_id
        WHERE c.parent_id = #{parentId} AND c.status = '0'
        ORDER BY c.create_time DESC
    </select>
</mapper>
