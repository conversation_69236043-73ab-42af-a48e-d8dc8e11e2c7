<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumLikeMapper">

    <resultMap type="ForumLike" id="ForumLikeResult">
        <result property="likeId"    column="like_id"    />
        <result property="contentType"    column="content_type"    />
        <result property="contentId"    column="content_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectForumLikeVo">
        select like_id, content_type, content_id, user_id, create_time from forum_like
    </sql>

    <select id="selectForumLikeList" parameterType="ForumLike" resultMap="ForumLikeResult">
        <include refid="selectForumLikeVo"/>
        <where>
            <if test="contentType != null  and contentType != ''"> and content_type = #{contentType}</if>
            <if test="contentId != null "> and content_id = #{contentId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectForumLikeByLikeId" parameterType="Long" resultMap="ForumLikeResult">
        <include refid="selectForumLikeVo"/>
        where like_id = #{likeId}
    </select>

    <insert id="insertForumLike" parameterType="ForumLike" useGeneratedKeys="true" keyProperty="likeId">
        insert into forum_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type,</if>
            <if test="contentId != null">content_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">#{contentType},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateForumLike" parameterType="ForumLike">
        update forum_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type = #{contentType},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where like_id = #{likeId}
    </update>

    <delete id="deleteForumLikeByLikeId" parameterType="Long">
        delete from forum_like where like_id = #{likeId}
    </delete>

    <delete id="deleteForumLikeByLikeIds" parameterType="String">
        delete from forum_like where like_id in
        <foreach item="likeId" collection="array" open="(" separator="," close=")">
            #{likeId}
        </foreach>
    </delete>

    <insert id="insertLike" parameterType="ForumLike" useGeneratedKeys="true" keyProperty="likeId">
        INSERT INTO forum_like (
            content_type,
            content_id,
            user_id,
            create_time
        ) VALUES (
                     #{contentType},
                     #{contentId},
                     #{userId},
                     #{createTime}
                 )
    </insert>
    <delete id="deleteLike">
        DELETE FROM forum_like
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
    </delete>
    <select id="checkExists" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM forum_like
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
    </select>
    <select id="selectLikeCount" resultType="int">
        SELECT COUNT(1)
        FROM forum_like
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
    </select>
    <resultMap id="MyLikeResult" type="MyLikeVO">
        <id property="likeId" column="like_id"/>
        <result property="contentType" column="content_type"/>
        <result property="contentId" column="content_id"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <association property="userInfo" javaType="SimpleUserVO">
            <result property="userId" column="user_id"/>
            <result property="nickname" column="nickname"/>
            <result property="avatar" column="avatar"/>
        </association>
    </resultMap>
    <select id="selectUserLikeList" resultMap="MyLikeResult">
        SELECT
            l.like_id, l.content_type, l.content_id, l.create_time,
            CASE
                WHEN l.content_type = '0' THEN pi.title
                WHEN l.content_type = '1' THEN ipi.title
                WHEN l.content_type = '2' THEN c.content
                END AS content,
            u.user_id, u.nickname, u.avatar
        FROM forum_like l
                 LEFT JOIN forum_post p ON l.content_type = '0' AND l.content_id = p.post_id
                 LEFT JOIN forum_post ip ON l.content_type = '1' AND l.content_id = ip.post_id
                 LEFT JOIN forum_comment c ON l.content_type = '2' AND l.content_id = c.comment_id
                 LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id AND pi.lang_id = #{langId} AND pi.status = '0'
                 LEFT JOIN forum_post_i18n_content ipi ON ip.post_id = ipi.post_id AND ipi.lang_id = #{langId} AND ipi.status = '0'
                 LEFT JOIN forum_user_extend u ON
            CASE
                WHEN l.content_type IN ('0','1') THEN u.user_id = COALESCE(p.sys_user_id, ip.sys_user_id)
                WHEN l.content_type = '2' THEN u.user_id = c.user_id
                END
        WHERE l.user_id = #{userId}
        ORDER BY l.create_time DESC
    </select>
    <select id="selectUserLikeCount" resultType="int">
        SELECT COUNT(*) FROM forum_like WHERE user_id = #{userId}
    </select>
</mapper>
