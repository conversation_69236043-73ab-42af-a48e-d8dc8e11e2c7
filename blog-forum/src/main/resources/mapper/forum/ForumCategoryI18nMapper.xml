<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumCategoryI18nMapper">

    <resultMap type="ForumCategoryI18n" id="ForumCategoryI18nResult">
        <result property="i18nId"    column="i18n_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="langId"    column="lang_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumCategoryI18nVo">
        select i18n_id, category_id, lang_id, category_name, create_time, update_time from forum_category_i18n
    </sql>

    <select id="selectForumCategoryI18nList" parameterType="ForumCategoryI18n" resultMap="ForumCategoryI18nResult">
        <include refid="selectForumCategoryI18nVo"/>
        <where>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="langId != null "> and lang_id = #{langId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
        </where>
    </select>

    <select id="selectForumCategoryI18nByI18nId" parameterType="Long" resultMap="ForumCategoryI18nResult">
        <include refid="selectForumCategoryI18nVo"/>
        where i18n_id = #{i18nId}
    </select>

    <insert id="insertForumCategoryI18n" parameterType="ForumCategoryI18n" useGeneratedKeys="true" keyProperty="i18nId">
        insert into forum_category_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="langId != null">lang_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="langId != null">#{langId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumCategoryI18n" parameterType="ForumCategoryI18n">
        update forum_category_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where i18n_id = #{i18nId}
    </update>

    <delete id="deleteForumCategoryI18nByI18nId" parameterType="Long">
        delete from forum_category_i18n where i18n_id = #{i18nId}
    </delete>

    <delete id="deleteForumCategoryI18nByI18nIds" parameterType="String">
        delete from forum_category_i18n where i18n_id in
        <foreach item="i18nId" collection="array" open="(" separator="," close=")">
            #{i18nId}
        </foreach>
    </delete>

    <!-- 根据分类ID列表和语言ID查询多语言信息 -->
    <select id="selectByCategoryIdsAndLangId" resultMap="ForumCategoryI18nResult">
        <include refid="selectForumCategoryI18nVo"/>
        where category_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        and lang_id = #{langId}
    </select>
</mapper>