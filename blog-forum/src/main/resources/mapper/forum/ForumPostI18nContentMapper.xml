<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumPostI18nContentMapper">
    
    <resultMap type="ForumPostI18nContent" id="ForumPostI18nContentResult">
        <result property="contentId"    column="content_id"    />
        <result property="postId"    column="post_id"    />
        <result property="langId"    column="lang_id"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumPostI18nContentVo">
        select content_id, post_id, lang_id, title, summary, content, create_time, update_time from forum_post_i18n_content
    </sql>

    <select id="selectForumPostI18nContentList" parameterType="ForumPostI18nContent" resultMap="ForumPostI18nContentResult">
        <include refid="selectForumPostI18nContentVo"/>
        <where>  
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="langId != null "> and lang_id = #{langId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectForumPostI18nContentByContentId" parameterType="Long" resultMap="ForumPostI18nContentResult">
        <include refid="selectForumPostI18nContentVo"/>
        where content_id = #{contentId}
    </select>

    <insert id="insertForumPostI18nContent" parameterType="ForumPostI18nContent" useGeneratedKeys="true" keyProperty="contentId">
        insert into forum_post_i18n_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="langId != null">lang_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="summary != null">summary,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="langId != null">#{langId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="summary != null">#{summary},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumPostI18nContent" parameterType="ForumPostI18nContent">
        update forum_post_i18n_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where content_id = #{contentId}
    </update>

    <delete id="deleteForumPostI18nContentByContentId" parameterType="Long">
        delete from forum_post_i18n_content where content_id = #{contentId}
    </delete>

    <delete id="deleteForumPostI18nContentByContentIds" parameterType="String">
        delete from forum_post_i18n_content where content_id in 
        <foreach item="contentId" collection="array" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </delete>
</mapper>