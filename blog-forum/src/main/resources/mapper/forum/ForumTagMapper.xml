<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumTagMapper">

    <resultMap type="ForumTag" id="ForumTagResult">
        <result property="tagId"    column="tag_id"    />
        <result property="sort"    column="sort"    />
        <result property="tagName" column="tag_name"/>
        <result property="color"    column="color"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectForumTagVo">
        select tag_id, sort, tag_name, color, status, create_by, create_time, update_by, update_time, remark from forum_tag
    </sql>

    <select id="selectForumTagList" parameterType="ForumTag" resultMap="ForumTagResult">
        <include refid="selectForumTagVo"/>
        <where>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectForumTagByTagId" parameterType="Long" resultMap="ForumTagResult">
        <include refid="selectForumTagVo"/>
        where status = '0' and tag_id = #{tagId}
    </select>

    <insert id="insertForumTag" parameterType="ForumTag" useGeneratedKeys="true" keyProperty="tagId">
        insert into forum_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null">tag_name,</if>
            <if test="color != null">color,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null">#{tagName},</if>
            <if test="color != null">#{color},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateForumTag" parameterType="ForumTag">
        update forum_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null">tag_name = #{tagName},</if>
            <if test="color != null">color = #{color},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteForumTagByTagId" parameterType="Long">
        delete from forum_tag where tag_id = #{tagId}
    </delete>

    <delete id="deleteForumTagByTagIds" parameterType="String">
        delete from forum_tag where tag_id in
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>

    <resultMap id="ForumTagResult1" type="ForumTag">
        <id property="tagId" column="tag_id"/>
        <result property="sort" column="sort"/>
        <result property="color" column="color"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="postCount" column="post_count"/>
        <association property="tagName" column="{tagId=tag_id,langId=langId}"
                     select="selectTagNameByLang"/>
    </resultMap>
    <select id="selectTagList" parameterType="ForumTag" resultMap="ForumTagResult1">
        SELECT
            t.tag_id,
            t.sort,
            t.color,
            t.status,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.remark,
            #{langId} as langId,
            COALESCE(tc.post_count, 0) AS post_count
        FROM forum_tag t
                 LEFT JOIN (
                     SELECT pt.tag_id, COUNT(*) as post_count
                     FROM forum_post_tag pt
                              JOIN forum_post p ON pt.post_id = p.post_id
                     WHERE p.status != '1'
                     GROUP BY pt.tag_id
                 ) tc ON t.tag_id = tc.tag_id
        WHERE t.status = '0'
        ORDER BY t.sort ASC
    </select>
    <select id="selectTagNameByLang" resultType="String">
        SELECT tag_name
        FROM forum_tag_i18n
        WHERE tag_id = #{tagId} AND lang_id = #{langId}
    </select>
</mapper>
