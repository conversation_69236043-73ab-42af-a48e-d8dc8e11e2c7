<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumPostMapper">

    <resultMap type="ForumPost" id="ForumPostResult">
        <result property="postId"    column="post_id"    />
        <result property="sysUserId" column="sys_user_id"/>
        <result property="isUserPost" column="is_user_post"/>
        <result property="categoryId"    column="category_id"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="content"    column="content"    />
        <result property="viewCount"    column="view_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="collectCount"    column="collect_count"    />
        <result property="status"    column="status"    />
        <result property="isTop"    column="is_top"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectForumPostVo">
        select
            post_id,
            category_id,
            sys_user_id,
            is_user_post,
            title,
            summary,
            cover_image,
            content,
            view_count,
            comment_count,
            like_count,
            collect_count,
            status,
            is_top,
            sort,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        from forum_post
    </sql>

    <select id="selectForumPostList" parameterType="ForumPost" resultMap="ForumPostResult">
        <include refid="selectForumPostVo"/>
        <where>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="coverImage != null  and coverImage != ''"> and cover_image = #{coverImage}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
            <if test="commentCount != null "> and comment_count = #{commentCount}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="collectCount != null "> and collect_count = #{collectCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isTop != null  and isTop != ''"> and is_top = #{isTop}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectForumPostByPostId" parameterType="Long" resultMap="ForumPostResult">
        <include refid="selectForumPostVo"/>
        where post_id = #{postId}
    </select>

    <insert id="insertForumPost" parameterType="ForumPost" useGeneratedKeys="true" keyProperty="postId">
        insert into forum_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="sysUserId != null and sysUserId != ''">sys_user_id,</if>
            <if test="summary != null">summary,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="content != null">content,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="collectCount != null">collect_count,</if>
            <if test="status != null">status,</if>
            <if test="isTop != null">is_top,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="langId != null">lang_id,</if>
            <if test="isUserPost != null and isUserPost !=''">is_user_post,</if>
            <if test="postStatus != null and postStatus != '' ">post_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="sysUserId != null and sysUserId != ''">#{sysUserId},</if>
            <if test="summary != null">#{summary},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="content != null">#{content},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="collectCount != null">#{collectCount},</if>
            <if test="status != null">#{status},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="langId != null">#{langId},</if>
            <if test="isUserPost != null and isUserPost !=''">#{isUserPost},</if>
            <if test="postStatus != null and postStatus != '' ">#{postStatus},</if>
         </trim>
    </insert>

    <update id="updateForumPost" parameterType="ForumPost">
        update forum_post
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="content != null">content = #{content},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="collectCount != null">collect_count = #{collectCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="postStatus != null">post_status = #{postStatus},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where post_id = #{postId}
    </update>

    <delete id="deleteForumPostByPostId" parameterType="Long">
        delete from forum_post where post_id = #{postId}
    </delete>

    <delete id="deleteForumPostByPostIds" parameterType="String">
        delete from forum_post where post_id in
        <foreach item="postId" collection="array" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <resultMap id="PostVOResult" type="com.blog.forum.domain.vo.PostVO">
        <id property="postId" column="post_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="coverImage" column="cover_image"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="status" column="status"/>
        <result property="postStatus" column="post_status"/>
        <result property="isTop" column="is_top"/>
        <result property="createTime" column="create_time"/>
        <collection property="tags" ofType="com.blog.forum.domain.vo.TagVO"
                    select="selectTagsByPostId" column="post_id"/>
<!--        <collection property="attachments" ofType="com.blog.forum.domain.vo.AttachmentVO"-->
<!--                    select="selectAttachmentsByPostId" column="post_id"/>-->
    </resultMap>
    <select id="selectPostList" resultMap="PostVOResult">
        SELECT
        p.post_id,
        p.category_id,
        COALESCE(pi.title, p.title) AS title,
        COALESCE(pi.summary, p.summary) AS summary,
        p.cover_image,
        p.view_count,
        p.comment_count,
        p.like_count,
        p.status,
        p.post_status,
        p.is_top,
        p.create_time
        FROM forum_post p
        LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id AND pi.lang_id = #{langId}
        WHERE p.status = '0'
        <if test="query.categoryId != null">
            AND p.category_id = #{query.categoryId}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (COALESCE(pi.title, p.title) LIKE CONCAT('%', #{query.keyword}, '%')
            OR COALESCE(pi.summary, p.summary) LIKE CONCAT('%', #{query.keyword}, '%'))
        </if>
        <if test="query.isTop != null">
            AND p.is_top = #{query.isTop}
        </if>
        <if test="query.postStatus != null">
            AND p.post_status = #{query.postStatus}
        </if>
        ORDER BY
        <choose>
            <when test="query.sort == 'hot'">
                p.view_count DESC, p.like_count DESC
            </when>
            <otherwise>
                p.is_top DESC, p.create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectPostById" resultMap="PostVOResult">
        SELECT
            p.post_id,
            p.category_id,
            COALESCE(pi.title, p.title) AS title,
            COALESCE(pi.summary, p.summary) AS summary,
            p.cover_image,
            p.view_count,
            p.comment_count,
            p.like_count,
            COALESCE(pi.content, p.content) AS content,
            p.status,
            p.post_status,
            p.is_top,
            p.create_time
        FROM forum_post p
                 LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id AND pi.lang_id = #{langId}
        WHERE p.post_id = #{postId} AND p.status = '0'
    </select>
    <select id="selectTagsByPostId" resultType="com.blog.forum.domain.vo.TagVO">
        SELECT
            t.tag_id,
            COALESCE(ti.tag_name, t.tag_name) AS tag_name
        FROM forum_post_tag pt
                 JOIN forum_tag t ON pt.tag_id = t.tag_id
                 LEFT JOIN forum_tag_i18n ti ON t.tag_id = ti.tag_id AND ti.lang_id = #{langId}
        WHERE pt.post_id = #{postId} AND t.status = '0'
    </select>
<!--    <select id="selectAttachmentsByPostId" resultType="com.blog.forum.domain.vo.AttachmentVO">-->
<!--        SELECT-->
<!--            a.attachment_id,-->
<!--            a.oss_id,-->
<!--            a.file_name,-->
<!--            a.file_url,-->
<!--            a.file_type,-->
<!--            a.file_size-->
<!--        FROM forum_post_attachment a-->
<!--        WHERE a.post_id = #{postId}-->
<!--    </select>-->
<!--    <update id="incrementViewCount">-->
<!--        UPDATE forum_post-->
<!--        SET view_count = view_count + 1-->
<!--        WHERE post_id = #{postId}-->
<!--    </update>-->

    <update id="incrementLikeCount">
        UPDATE forum_post
        SET like_count = like_count + #{increment}
        WHERE post_id = #{postId}
    </update>
    <update id="incrementCollectCount">
        UPDATE forum_post
        SET collect_count = collect_count + #{increment}
        WHERE post_id = #{postId}
    </update>
    <update id="incrementShareCount">
        UPDATE forum_post
        SET share_count = share_count + 1
        WHERE post_id = #{postId}
    </update>

    <update id="incrementCommentCount">
        UPDATE forum_post
        SET comment_count = comment_count + #{increment}
        WHERE post_id = #{postId}
    </update>

    <resultMap id="PostDetailResultMap" type="PostDetailVO">
        <!-- 基础字段映射 -->
        <result property="postId" column="post_id"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="collectCount" column="collect_count"/>
        <result property="summary" column="summary"/>
        <result property="createTime" column="create_time"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="coverImage" column="cover_image"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="userId" column="sys_user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="postStatus" column="post_status"/>
        <result property="status" column="status"/>
        <result property="isTop" column="is_top"/>
        <!-- 国际化字段从forum_post_i18n_content表获取 -->
    </resultMap>
    <sql id="selectPostListVO">
        select p.post_id,
               p.view_count,
               p.comment_count,
               p.like_count,
               p.collect_count,
               p.create_time,
               p.title,
               pi.summary as summary,
               pi.content,
               p.cover_image,
               p.category_id,
               c.category_name,
               u.sys_user_id,
               u.nickname,
               u.avatar,
               p.post_status,
               p.is_top
        from forum_post p
                 left join forum_post_i18n_content pi on p.post_id = pi.post_id
                and pi.lang_id = #{langId} and pi.status = '0'
                 LEFT JOIN forum_user_extend u ON u.user_id = p.sys_user_id
                 LEFT JOIN forum_category_i18n c ON p.category_id = c.category_id AND c.lang_id = #{langId}

        where p.status = '0' and p.post_status = '1'
    </sql>

    <select id="selectPostDetail" parameterType="map" resultMap="PostDetailResultMap">
        <include refid="selectPostListVO"/>
        and p.post_id = #{postId}
    </select>
    <update id="incrementViewCount" parameterType="long">
        update forum_post set view_count = view_count + 1 where post_id = #{postId}
    </update>

    <select id="selectPostQuery" parameterType="PostListDTO" resultMap="PostDetailResultMap">
        <include refid="selectPostListVO"/>
        <if test="params.categoryId != null">
            and p.category_id = #{params.categoryId}
        </if>
        <if test="params.keyword != null and params.keyword != '' ">
            and (
            pi.title LIKE CONCAT('%', #{params.keyword}, '%')
            or pi.summary LIKE CONCAT('%', #{params.keyword}, '%')
            or pi.content LIKE CONCAT('%', #{params.keyword}, '%')
            )
        </if>
        <choose>
            <when test="params.sortType == 'hot'">
                order by is_top desc, p.view_count desc, p.like_count desc
            </when>
            <otherwise>
                order by is_top desc, p.create_time desc
            </otherwise>
        </choose>
    </select>

    <resultMap id="UserPostDetailResult" type="UserPostDetailVO">
        <id property="postId" column="post_id"/>
        <result property="userId" column="sys_user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="coverImage" column="cover_image"/>
        <result property="content" column="content"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="collectCount" column="collect_count"/>
        <result property="createTime" column="create_time"/>
        <result property="categoryId" column="category_id"/>
    </resultMap>
    <sql id="selectUserPostSql">
        SELECT
            p.post_id, p.sys_user_id, u.nickname, u.avatar,
            pi.title, pi.summary as summary, p.cover_image, pi.content,
            p.view_count, p.comment_count, p.like_count, p.collect_count,
            p.create_time,p.category_id
        FROM forum_post p
                 JOIN forum_user_extend u ON p.sys_user_id = u.user_id
                 LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id
            AND pi.lang_id = #{langId} AND pi.status = '0'
        WHERE p.status = '0' AND p.post_status = '1' AND p.is_user_post = '1'
    </sql>
    <select id="selectUserPostList" resultMap="UserPostDetailResult">
        <include refid="selectUserPostSql"/>
        <if test="userId != null">
            AND p.sys_user_id = #{userId}
        </if>
        <if test="categoryId != null">
            AND p.category_id = #{categoryId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND pi.title LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <choose>
            <when test="sortType == 'hot'">
                ORDER BY p.view_count DESC, p.like_count DESC
            </when>
            <otherwise>
                ORDER BY p.create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectUserPostDetail" resultMap="UserPostDetailResult">
        <include refid="selectUserPostSql"/>
        AND p.post_id = #{postId}
    </select>

    <!-- 帖子列表高级查询 -->
    <select id="selectPostListAdvanced" resultType="com.blog.forum.domain.vo.PostDetailVO">
        SELECT
            p.post_id as postId,
            p.view_count as viewConut,
            p.comment_count as commentCount,
            p.like_count as likeConut,
            p.collect_count as collectCount,
            p.create_time as createTime,
            COALESCE(pi.title, p.title) AS title,
            COALESCE(pi.summary, p.summary) AS summary,
            COALESCE(pi.content, p.content) AS content,
            p.cover_image as coverImage,
            p.category_id as categoryId,
            p.sys_user_id as sysUserId,
            u.nickname as nickname,
            u.avatar as avatar,
            p.post_status postStatus,
            p.status,
            c.category_name as categoryName,
            p.is_top as isTop
        FROM forum_post p
        LEFT JOIN forum_post_i18n_content pi ON p.post_id = pi.post_id AND pi.lang_id = #{langId}
        LEFT JOIN forum_user_extend u ON p.sys_user_id = u.user_id
        LEFT JOIN forum_category_i18n c ON p.category_id = c.category_id AND c.lang_id = #{langId}
        <where>
            <if test="query.status != null and query.status != ''">
                p.status = #{query.status}
            </if>
            <if test="query.status == null or query.status == ''">
                (p.status = '0' or p.status = '2')
            </if>
        <if test="query.tagIds != null and query.tagIds.size() > 0">
            AND p.post_id IN (
                SELECT pt.post_id FROM forum_post_tag pt
                WHERE pt.tag_id IN
                <foreach item="tagId" collection="query.tagIds" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            )
        </if>
            <if test="query.userName != null and query.userName != ''">
                AND (u.nickname LIKE CONCAT('%', #{query.userName}, '%') OR u.user_name LIKE CONCAT('%', #{query.userName}, '%'))
            </if>
            <if test="query.categoryId != null">
                AND p.category_id = #{query.categoryId}
            </if>
            <if test="query.postStatus != null and query.postStatus != ''">
                AND p.post_status = #{query.postStatus}
            </if>
            <if test="query.isUserPost != null">
                AND p.is_user_post = #{query.isUserPost}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (COALESCE(pi.title, p.title) LIKE CONCAT('%', #{query.keyword}, '%')
                OR COALESCE(pi.content, p.content) LIKE CONCAT('%', #{query.keyword}, '%')
                OR COALESCE(pi.summary, p.summary) LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.sysUserId != null">
                AND p.sys_user_id = #{query.sysUserId}
            </if>
        </where>
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询帖子作者ID -->
    <select id="selectPostAuthorId" resultType="java.lang.Long">
        SELECT sys_user_id FROM forum_post WHERE post_id = #{postId}
    </select>

    <!-- 更新帖子置顶状态 -->
    <update id="updatePostTopStatus">
        UPDATE forum_post
        SET is_top = #{isTop},
            update_time = SYSDATE()
        WHERE post_id = #{postId}
    </update>
</mapper>
