<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumUserPostMapper">

    <resultMap type="ForumUserPost" id="ForumUserPostResult">
        <result property="userPostId"    column="user_post_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="viewCount"    column="view_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectForumUserPostVo">
        select user_post_id, user_id, content, view_count, comment_count, like_count, status, create_time, update_time from forum_user_post
    </sql>

    <select id="selectForumUserPostList" parameterType="ForumUserPost" resultMap="ForumUserPostResult">
        <include refid="selectForumUserPostVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
            <if test="commentCount != null "> and comment_count = #{commentCount}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectForumUserPostByUserPostId" parameterType="Long" resultMap="ForumUserPostResult">
        <include refid="selectForumUserPostVo"/>
        where user_post_id = #{userPostId}
    </select>

    <insert id="insertForumUserPost" parameterType="ForumUserPost" useGeneratedKeys="true" keyProperty="userPostId">
        insert into forum_user_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateForumUserPost" parameterType="ForumUserPost">
        update forum_user_post
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_post_id = #{userPostId}
    </update>

    <delete id="deleteForumUserPostByUserPostId" parameterType="Long">
        delete from forum_user_post where user_post_id = #{userPostId}
    </delete>

    <delete id="deleteForumUserPostByUserPostIds" parameterType="String">
        delete from forum_user_post where user_post_id in
        <foreach item="userPostId" collection="array" open="(" separator="," close=")">
            #{userPostId}
        </foreach>
    </delete>

    <resultMap id="UserPostDetailVOResult1" type="UserPostDetailVO">
        <id property="userPostId" column="user_post_id"/>
        <result property="userId" column="user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="content" column="content"/>
        <result property="viewCount" column="view_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 所有 result 必须在前 -->
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <!-- 之后才是 collection/association -->
        <collection property="images" ofType="UserPostImageVO" select="selectUserPostImages" column="user_post_id"/>
        <collection property="tags" ofType="PostTagVO" select="selectPostTags" column="user_post_id"/>
    </resultMap>
<!--    <select id="selectUserPostList" resultMap="UserPostDetailVOResult">-->
<!--        SELECT-->
<!--        p.user_post_id,-->
<!--        p.user_id,-->
<!--        e.nickname,-->
<!--        e.avatar,-->
<!--        p.content,-->
<!--        p.view_count,-->
<!--        p.comment_count,-->
<!--        p.like_count,-->
<!--        p.status,-->
<!--        p.create_time,-->
<!--        p.update_time-->
<!--        FROM forum_user_post p-->
<!--        LEFT JOIN forum_user_extend e ON p.user_id = e.user_id-->
<!--        <where>-->
<!--            <if test="query.userId != null">AND p.user_id = #{query.userId}</if>-->
<!--            <if test="query.status != null and query.status != ''">AND p.status = #{query.status}</if>-->
<!--        </where>-->
<!--        ORDER BY p.create_time DESC-->
<!--    </select>-->
    <select id="selectUserPostDetail" resultMap="UserPostDetailVOResult1">
        SELECT
            p.user_post_id,
            p.user_id,
            e.nickname,
            e.avatar,
            p.content,
            p.view_count,
            p.comment_count,
            p.like_count,
            p.status,
            p.create_time,
            p.update_time
        FROM forum_user_post p
                 LEFT JOIN forum_user_extend e ON p.user_id = e.user_id
        WHERE p.user_post_id = #{userPostId}
    </select>

    <select id="selectUserPostImages" parameterType="Long" resultType="UserPostImageVO">
        SELECT
            image_id,
            image_url,
            sort
        FROM forum_user_post_image
        WHERE user_post_id = #{userPostId}
        ORDER BY sort ASC
    </select>

    <insert id="insertUserPost" parameterType="ForumUserPost" useGeneratedKeys="true" keyProperty="userPostId">
        INSERT INTO forum_user_post (
            user_id,
            content,
            view_count,
            comment_count,
            like_count,
            status,
            create_time,
            update_time
        ) VALUES (
                     #{userId},
                     #{content},
                     #{viewCount},
                     #{commentCount},
                     #{likeCount},
                     #{status},
                     #{createTime},
                     #{updateTime}
                 )
    </insert>

    <update id="updateViewCount">
        UPDATE forum_user_post
        SET view_count = view_count + 1
        WHERE user_post_id = #{userPostId}
    </update>

    <update id="incrementCommentCount">
        UPDATE forum_user_post
        SET comment_count = comment_count + #{increment}
        WHERE user_post_id = #{userPostId}
    </update>

    <!-- 更新查询SQL -->
    <select id="selectUserPostList" resultMap="UserPostDetailVOResult1">
        SELECT
        p.user_post_id,
        p.user_id,
        e.nickname,
        e.avatar,
        p.content,
        p.view_count,
        p.comment_count,
        p.like_count,
        p.status,
        p.create_time,
        p.update_time,
        p.category_id,
        c.category_name
        FROM forum_user_post p
        LEFT JOIN forum_user_extend e ON p.user_id = e.user_id
        LEFT JOIN forum_post_category c ON p.category_id = c.category_id
        <where>
            p.status = '0'
            <if test="query.userId != null">AND p.user_id = #{query.userId}</if>
            <if test="query.status != null and query.status != ''">AND p.status = #{query.status}</if>
        </where>
        ORDER BY p.create_time DESC
    </select>

    <!-- 新增查询方法 -->
    <select id="selectPostTags" resultType="PostTagVO">
        SELECT
            t.tag_id,
            ti.tag_name
        FROM forum_post_tag pt
                 JOIN forum_tag t ON pt.tag_id = t.tag_id
                 JOIN forum_tag_i18n ti ON t.tag_id = ti.tag_id
        WHERE pt.post_id = #{postId} AND ti.lang_id = #{langId}
        ORDER BY t.sort ASC
    </select>
    <select id="selectCategoryName" resultType="String">
        SELECT category_name
        FROM forum_post_category
        WHERE category_id = #{categoryId}
    </select>
</mapper>
