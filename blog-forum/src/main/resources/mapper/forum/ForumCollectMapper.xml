<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumCollectMapper">

    <resultMap type="ForumCollect" id="ForumCollectResult">
        <result property="collectId"    column="collect_id"    />
        <result property="contentType"    column="content_type"    />
        <result property="contentId"    column="content_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectForumCollectVo">
        select collect_id, content_type, content_id, user_id, create_time from forum_collect
    </sql>

    <select id="selectForumCollectList" parameterType="ForumCollect" resultMap="ForumCollectResult">
        <include refid="selectForumCollectVo"/>
        <where>
            <if test="contentType != null  and contentType != ''"> and content_type = #{contentType}</if>
            <if test="contentId != null "> and content_id = #{contentId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectForumCollectByCollectId" parameterType="Long" resultMap="ForumCollectResult">
        <include refid="selectForumCollectVo"/>
        where collect_id = #{collectId}
    </select>

    <insert id="insertForumCollect" parameterType="ForumCollect" useGeneratedKeys="true" keyProperty="collectId">
        insert into forum_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type,</if>
            <if test="contentId != null">content_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">#{contentType},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateForumCollect" parameterType="ForumCollect">
        update forum_collect
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentType != null and contentType != ''">content_type = #{contentType},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where collect_id = #{collectId}
    </update>

    <delete id="deleteForumCollectByCollectId" parameterType="Long">
        delete from forum_collect where collect_id = #{collectId}
    </delete>

    <delete id="deleteForumCollectByCollectIds" parameterType="String">
        delete from forum_collect where collect_id in
        <foreach item="collectId" collection="array" open="(" separator="," close=")">
            #{collectId}
        </foreach>
    </delete>

    <insert id="insertCollect" parameterType="ForumCollect" useGeneratedKeys="true" keyProperty="collectId">
        INSERT INTO forum_collect (
            content_type,
            content_id,
            user_id,
            create_time
        ) VALUES (
                     #{contentType},
                     #{contentId},
                     #{userId},
                     #{createTime}
                 )
    </insert>
    <delete id="deleteCollect">
        DELETE FROM forum_collect
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
    </delete>
    <select id="checkExists" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM forum_collect
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
    </select>
    <select id="selectCollectCount" resultType="int">
        SELECT COUNT(1)
        FROM forum_collect
        WHERE content_type = #{contentType}
          AND content_id = #{contentId}
    </select>

    <resultMap id="MyCollectResult" type="MyCollectVO">
        <id property="collectId" column="collect_id"/>
        <result property="contentType" column="content_type"/>
        <result property="contentId" column="content_id"/>
        <result property="title" column="title"/>
        <result property="coverImage" column="cover_image"/>
        <result property="createTime" column="create_time"/>
        <association property="userInfo" javaType="SimpleUserVO">
            <result property="userId" column="user_id"/>
            <result property="nickname" column="nickname"/>
            <result property="avatar" column="avatar"/>
        </association>
    </resultMap>
    <select id="selectUserCollectList" resultMap="MyCollectResult">
        SELECT
            c.collect_id, c.content_type, c.content_id, c.create_time,
            CASE
                WHEN c.content_type = '0' THEN p.title
                WHEN c.content_type = '1' THEN ip.title
                END AS title,
            p.cover_image,
            u.user_id, u.nickname, u.avatar
        FROM forum_collect c
                 LEFT JOIN forum_post p ON c.content_type = '0' AND c.content_id = p.post_id
                 LEFT JOIN forum_post ip ON c.content_type = '1' AND c.content_id = ip.post_id
                 LEFT JOIN forum_post_i18n_content pi ON pi.post_id = p.post_id AND pi.lang_id = #{langId} AND pi.status = '0'
                 LEFT JOIN forum_post_i18n_content ipi ON ipi.post_id = ip.post_id AND ipi.lang_id = #{langId} AND ipi.status = '0'
                 LEFT JOIN forum_user_extend u ON u.user_id = COALESCE(p.sys_user_id, ip.sys_user_id)
        WHERE c.user_id = #{userId}
        ORDER BY c.create_time DESC
    </select>
    <select id="selectUserCollectCount" resultType="int">
        SELECT COUNT(*) FROM forum_collect WHERE user_id = #{userId}
    </select>

    <select id="selectCollectByUserAndContent" resultType="ForumCollect">
        SELECT
            collect_id,
            content_type,
            content_id,
            user_id,
            create_time
        FROM forum_collect
        WHERE
            content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
            LIMIT 1
    </select>

    <select id="checkCollectExists" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM forum_collect
        WHERE
            content_type = #{contentType}
          AND content_id = #{contentId}
          AND user_id = #{userId}
    </select>

</mapper>
