<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.forum.mapper.ForumCarouselMapper">

    <resultMap type="ForumCarousel" id="ForumCarouselResult">
        <result property="carouselId"    column="carousel_id"    />
        <result property="title"    column="title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="clickUrl"    column="click_url"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectForumCarouselVo">
        select carousel_id, title, image_url, click_url, sort, status, create_by, create_time, update_by, update_time, remark from forum_carousel
    </sql>

    <select id="selectForumCarouselList" parameterType="ForumCarousel" resultMap="ForumCarouselResult">
        <include refid="selectForumCarouselVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectForumCarouselByCarouselId" parameterType="Long" resultMap="ForumCarouselResult">
        <include refid="selectForumCarouselVo"/>
        where carousel_id = #{carouselId}
    </select>

    <insert id="insertForumCarousel" parameterType="ForumCarousel" useGeneratedKeys="true" keyProperty="carouselId">
        insert into forum_carousel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="clickUrl != null">click_url,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="clickUrl != null">#{clickUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateForumCarousel" parameterType="ForumCarousel">
        update forum_carousel
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="clickUrl != null">click_url = #{clickUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where carousel_id = #{carouselId}
    </update>

    <delete id="deleteForumCarouselByCarouselId" parameterType="Long">
        delete from forum_carousel where carousel_id = #{carouselId}
    </delete>

    <delete id="deleteForumCarouselByCarouselIds" parameterType="String">
        delete from forum_carousel where carousel_id in
        <foreach item="carouselId" collection="array" open="(" separator="," close=")">
            #{carouselId}
        </foreach>
    </delete>

    <resultMap id="CarouselVOResult" type="com.blog.forum.domain.vo.CarouselVO">
        <id property="carouselId" column="carousel_id"/>
        <result property="title" column="title"/>
        <result property="imageUrl" column="image_url"/>
        <result property="clickUrl" column="click_url"/>
        <result property="sort" column="sort"/>
    </resultMap>
    <select id="selectCarouselList" resultMap="CarouselVOResult">
        SELECT
            carousel_id,
            title,
            image_url,
            click_url,
            sort
        FROM forum_carousel
        WHERE status = '0'
        ORDER BY sort ASC
    </select>
</mapper>
