import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  plugins: [vue()],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`
      }
    }
  },
  server: {
    open:true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080', // Your backend
        // target:'http://blog.frp.85me.cn',
        // target: 'https://blog.mdiyer.cn/prod-api', // Your backend
        
        changeOrigin: true,
        secure: false,
        // rewrite: path => path.replace(/^\/api/, '')
        // cookieDomainRewrite: 'localhost'
      }
    },
    allowedHosts:[
      'http://blog.frp.85me.cn',
      'skyblock.frp.85me.cn',
      '127.0.0.1'
    ],
    port: 8081,
    host: '0.0.0.0'
  }
}) 