#!/bin/bash

# 输出系统编码信息
echo "=== 系统编码信息 ==="
echo "LANG: $LANG"
echo "LC_ALL: $LC_ALL"
echo "LC_CTYPE: $LC_CTYPE"
echo ""

# 输出Java编码信息
echo "=== Java编码信息 ==="
java -XshowSettings:properties -version 2>&1 | grep -E "file.encoding|sun.jnu.encoding|user.language|user.region"
echo ""

# 检查日志文件编码
echo "=== 日志文件编码 ==="
LOG_DIR=/data/blog-admin/logs
if [ -d "$LOG_DIR" ]; then
    echo "日志目录: $LOG_DIR"
    echo "文件列表:"
    ls -la $LOG_DIR
    
    # 检查最新的日志文件
    LATEST_LOG=$(ls -t $LOG_DIR/*.log 2>/dev/null | head -1)
    if [ -n "$LATEST_LOG" ]; then
        echo ""
        echo "最新日志文件: $LATEST_LOG"
        echo "文件编码:"
        file -i $LATEST_LOG
        echo ""
        echo "日志文件前10行:"
        head -10 $LATEST_LOG
    else
        echo "未找到日志文件"
    fi
else
    echo "日志目录不存在: $LOG_DIR"
fi
echo ""

# 检查中文字体
echo "=== 中文字体 ==="
fc-list :lang=zh
echo ""

# 检查Java进程
echo "=== Java进程 ==="
ps -ef | grep java
echo ""

# 输出完成信息
echo "编码检查完成。如果日志中有乱码，请确保所有编码设置为UTF-8。"
