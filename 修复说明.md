# B端分片上传修复说明

## 问题描述
B端文件分片上传时出现"合并分片失败: Object does not exist"错误。

## 问题分析

通过对比C端和B端的分片上传逻辑，发现了以下关键问题：

### 1. 文件标识生成方式不一致
- **C端**: 使用 `${file.name}-${file.size}-${lastModified}` 生成文件标识
- **B端**: 使用 SparkMD5 计算文件MD5作为标识

### 2. 上传逻辑不符合后端设计
- 后端已经实现了自动合并功能，最后一个分片上传成功后会自动返回合并后的文件URL
- 前端不需要手动调用合并接口
- 需要先检查文件是否已存在，避免重复上传

### 3. bizType设置不正确
- 应该使用 `forum` 而不是 `editor-image` 或 `editor-file`

## 修复方案

### 1. 统一文件标识生成方式

**修改文件**: `blog-ui/src/utils/chunkUpload.js`

```javascript
// 原来的MD5计算方式
async calculateMD5(file) {
  // SparkMD5计算逻辑...
}

// 修改为与C端一致的方式
generateFileIdentifier(file) {
  const lastModified = file.lastModified ? file.lastModified.toString() : Date.now().toString()
  return `${file.name}-${file.size}-${lastModified}`
}
```

### 2. 重构上传逻辑

**修改文件**: `blog-ui/src/utils/chunkUpload.js`

```javascript
// 新的上传逻辑
async upload(file) {
  // 1. 生成文件标识
  const identifier = this.generateFileIdentifier(file)

  // 2. 检查文件是否已存在
  const fileCheck = await this.checkFileExists(identifier)
  if (fileCheck.exists && fileCheck.fileUrl) {
    return fileCheck.fileUrl // 直接返回已存在的文件URL
  }

  // 3. 逐个上传分片
  for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
    const uploadResult = await this.uploadChunk(file, chunk, chunkInfo)

    // 4. 检查后端是否返回了合并后的文件URL
    if (uploadResult && uploadResult.fileUrl) {
      return uploadResult.fileUrl // 后端自动合并完成
    }
  }
}
```

### 3. 移除手动合并逻辑

- 删除了 `mergeChunks` 方法
- 后端会在最后一个分片上传成功后自动合并并返回文件URL

### 4. 修正bizType设置

**修改文件**: `blog-ui/src/components/TiptapEditor/index.vue`

```javascript
// 统一使用 'forum' 作为bizType
const uploader = new ChunkUploader({
  bizType: 'forum', // 原来是 'editor-image' 或 'editor-file'
  // ...
})
```

### 5. 优化后端分片验证

**修改文件**: `blog-forum/src/main/java/com/blog/forum/service/impl/MinioFileServiceImpl.java`

在合并分片前增加MinIO对象存在性验证：

```java
// 验证分片在MinIO中是否真的存在
try {
    minioClient.statObject(
        StatObjectArgs.builder()
            .bucket(minioConfig.getBucketName())
            .object(chunk.getChunkPath())
            .build()
    );
} catch (Exception e) {
    log.error("分片 {} 在MinIO中不存在: {}", i, chunk.getChunkPath(), e);
    throw new ServiceException("分片 " + i + " 在存储中不存在");
}
```

## 修复效果

1. **智能化**: 实现了文件存在性检查，避免重复上传相同文件
2. **自动化**: 后端自动合并分片，前端无需手动调用合并接口
3. **统一性**: B端和C端使用相同的文件标识生成方式
4. **可靠性**: 后端增加了分片对象存在性验证，避免合并不存在的分片
5. **性能**: 移除MD5计算，提高了文件标识生成速度
6. **正确性**: 修正了bizType设置，确保业务类型正确

## 核心改进

### 上传流程优化
```
旧流程: 生成MD5 → 上传所有分片 → 手动调用合并接口
新流程: 生成标识 → 检查文件存在 → 上传分片(后端自动合并) → 获取URL
```

### 错误处理改进
- 文件已存在时直接返回URL，避免重复上传
- 后端自动合并失败时有明确的错误提示
- 分片对象不存在时有详细的错误信息

## 测试建议

1. **基础功能测试**
   - 测试大于10MB的文件分片上传
   - 测试小于10MB的文件普通上传
   - 测试相同文件重复上传（应该直接返回已存在的URL）

2. **进度显示测试**
   - 验证上传进度显示正常
   - 验证文件已存在时的提示信息

3. **错误处理测试**
   - 测试网络中断时的错误处理
   - 测试MinIO服务异常时的错误处理
   - 测试不同文件类型的上传

4. **业务场景测试**
   - 在富文本编辑器中测试图片上传
   - 在富文本编辑器中测试文件上传
   - 测试上传取消功能

## 注意事项

1. 确保MinIO服务正常运行
2. 确保数据库中的分片表结构正确
3. 确保Redis缓存服务正常
4. bizType统一使用'forum'，确保与后端业务逻辑匹配
5. 建议在测试环境充分验证后再部署到生产环境
