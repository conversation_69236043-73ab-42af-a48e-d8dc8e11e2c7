# 四个问题修复总结

## 1. 后台将token过期时间设置为7天

### 修改文件
- `blog-admin/src/main/resources/application.yml`

### 修改内容
```yaml
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（7天 = 7 * 24 * 60 = 10080分钟）
  expireTime: 10080
```

### 说明
- 原来是30分钟，现在改为10080分钟（7天）
- 7天 = 7 × 24小时 × 60分钟 = 10080分钟

## 2. 修复评论时不增加帖子评论次数的问题

### 修改文件
1. `blog-forum/src/main/java/com/blog/forum/mapper/ForumPostMapper.java`
2. `blog-forum/src/main/resources/mapper/forum/ForumPostMapper.xml`
3. `blog-forum/src/main/java/com/blog/forum/mapper/ForumUserPostMapper.java`
4. `blog-forum/src/main/resources/mapper/forum/ForumUserPostMapper.xml`
5. `blog-forum/src/main/java/com/blog/forum/service/impl/ForumCommentServiceImpl.java`

### 修改内容

#### 添加增加评论数的方法
**ForumPostMapper.java**：
```java
int incrementCommentCount(@Param("postId") Long postId, @Param("increment") int increment);
```

**ForumPostMapper.xml**：
```xml
<update id="incrementCommentCount">
    UPDATE forum_post
    SET comment_count = comment_count + #{increment}
    WHERE post_id = #{postId}
</update>
```

**ForumUserPostMapper.java**：
```java
int incrementCommentCount(@Param("userPostId") Long userPostId, @Param("increment") int increment);
```

**ForumUserPostMapper.xml**：
```xml
<update id="incrementCommentCount">
    UPDATE forum_user_post
    SET comment_count = comment_count + #{increment}
    WHERE user_post_id = #{userPostId}
</update>
```

#### 在评论服务中调用增加评论数
**ForumCommentServiceImpl.java**：
```java
// 增加帖子的评论数
if ("0".equals(commentDTO.getContentType())) {
    // 帖子评论，增加forum_post的评论数
    forumPostMapper.incrementCommentCount(commentDTO.getContentId(), 1);
} else if ("1".equals(commentDTO.getContentType())) {
    // 用户发帖评论，增加forum_user_post的评论数
    forumUserPostMapper.incrementCommentCount(commentDTO.getContentId(), 1);
}
```

### 说明
- contentType="0"：帖子评论，更新forum_post表的comment_count
- contentType="1"：用户发帖评论，更新forum_user_post表的comment_count
- 每次添加评论时，对应的帖子评论数+1

## 3. 修复禁止发帖状态时用户依旧能发帖的问题

### 修改文件
- `blog-forum/src/main/java/com/blog/forum/service/impl/PostServiceImpl.java`

### 修改内容
在`submitUserPost`方法中添加发帖权限检查：
```java
// 检查用户是否被禁止发帖
ForumUserExtend userExtend = forumUserExtendMapper.selectForumUserExtendByUserId(user.getUserId());
if (userExtend != null && "1".equals(userExtend.getIsBannedPost())) {
    throw new ServiceException("您已被禁止发帖");
}
```

### 说明
- 在用户发帖前检查`ForumUserExtend`表中的`isBannedPost`字段
- 如果`isBannedPost="1"`，则抛出异常阻止发帖
- 确保被禁止发帖的用户无法通过API发帖

## 4. 修复禁用用户时没有清除token和禁止登录的问题

### 修改文件
- `blog-forum/src/main/java/com/blog/forum/service/impl/ForumUserExtendServiceImpl.java`

### 修改内容

#### 添加依赖注入
```java
import com.blog.framework.web.service.TokenService;
import com.blog.common.core.redis.RedisCache;

@Autowired
private TokenService tokenService;

@Autowired
private RedisCache redisCache;
```

#### 修改updateUserStatus方法
```java
int rows = sysUserMapper.updateUser(sysUser);
if (rows > 0) {
    // 如果是禁用用户，清除该用户的所有token
    if (StringUtils.equals(status, "1")) {
        clearUserTokens(sysUserId);
    }
    return AjaxResult.success(StringUtils.equals(status, "0") ? "启用成功" : "停用成功");
}
```

#### 添加清除token的方法
```java
/**
 * 清除用户的所有token
 *
 * @param sysUserId 系统用户ID
 */
private void clearUserTokens(Long sysUserId) {
    try {
        // 获取Redis中所有的登录token键
        String pattern = "login_tokens:*";
        java.util.Set<String> keys = redisCache.keys(pattern);
        
        for (String key : keys) {
            Object loginUser = redisCache.getCacheObject(key);
            if (loginUser != null && loginUser instanceof com.blog.common.core.domain.model.LoginUser) {
                com.blog.common.core.domain.model.LoginUser user = (com.blog.common.core.domain.model.LoginUser) loginUser;
                if (user.getUser() != null && sysUserId.equals(user.getUser().getUserId())) {
                    // 删除该用户的token
                    redisCache.deleteObject(key);
                }
            }
        }
    } catch (Exception e) {
        // 记录日志，但不影响主流程
        System.err.println("清除用户token失败: " + e.getMessage());
    }
}
```

### 登录时的用户状态检查
现有的`UserDetailsServiceImpl.loadUserByUsername`方法已经包含了用户状态检查：
```java
else if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
{
    log.info("登录用户：{} 已被停用.", username);
    throw new ServiceException(MessageUtils.message("user.blocked"));
}
```

### 说明
- **禁用用户时**：自动清除该用户在Redis中的所有token，强制下线
- **登录时检查**：系统已有机制检查用户状态，禁用用户无法登录
- **安全性**：确保禁用用户立即失去系统访问权限

## 总结

### 修复效果
1. ✅ **Token有效期**：从30分钟延长到7天，提升用户体验
2. ✅ **评论计数**：评论时正确增加帖子的评论数统计
3. ✅ **发帖权限**：被禁止发帖的用户无法发帖
4. ✅ **用户禁用**：禁用用户时立即清除token并阻止登录

### 涉及的主要功能模块
- **认证授权**：Token管理、用户状态检查
- **评论系统**：评论计数统计
- **发帖系统**：发帖权限控制
- **用户管理**：用户状态管理、权限控制

### 安全性提升
- 禁用用户立即失去系统访问权限
- 发帖权限得到有效控制
- 用户状态变更有完整的权限检查机制

所有修复都已完成，系统的安全性和功能完整性得到了显著提升。
