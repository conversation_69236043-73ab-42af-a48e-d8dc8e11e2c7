#!/bin/bash

# 设置Java环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 设置字符编码环境变量
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 设置应用目录
APP_DIR=/data/blog-admin
JAR_FILE=$APP_DIR/blog-admin.jar
LOG_FILE=$APP_DIR/logs/application.log

# 设置JVM参数
JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
# 设置字符编码相关参数
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.language=zh -Duser.region=CN"
# 设置日志编码
JAVA_OPTS="$JAVA_OPTS -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Dlogback.charset=UTF-8"

# 确保日志目录存在
mkdir -p $APP_DIR/logs

# 输出环境信息
echo "=== 环境信息 ===" > $LOG_FILE
echo "JAVA_HOME: $JAVA_HOME" >> $LOG_FILE
echo "LANG: $LANG" >> $LOG_FILE
echo "LC_ALL: $LC_ALL" >> $LOG_FILE
echo "Default Charset: $(java -XshowSettings:properties -version 2>&1 | grep file.encoding)" >> $LOG_FILE
echo "==================" >> $LOG_FILE

# 启动应用
echo "Starting application with UTF-8 encoding..."
nohup java $JAVA_OPTS -jar $JAR_FILE >> $LOG_FILE 2>&1 &

# 输出进程ID
PID=$!
echo $PID > $APP_DIR/application.pid
echo "Application started with PID: $PID"
echo "Check logs at: $LOG_FILE"
