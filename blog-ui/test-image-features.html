<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .success {
            color: #67C23A;
            font-weight: bold;
        }
        .error {
            color: #F56C6C;
            font-weight: bold;
        }
        .info {
            color: #409EFF;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>TiptapEditor 图片功能修复测试</h1>

        <div class="test-section">
            <div class="test-title">🔥 最新修复：双击预览和拖拽冲突问题</div>
            <div class="test-description">
                <p><span class="error">核心问题：</span></p>
                <ul>
                    <li>图片缩放到很小后双击预览失效</li>
                    <li>拖拽和双击事件冲突导致图片丢失</li>
                    <li>小图片的双击区域太小难以触发</li>
                </ul>

                <p><span class="success">修复措施：</span></p>
                <ul>
                    <li>✅ 改进双击检测：使用自定义点击计数器替代原生 <code>dblclick</code> 事件</li>
                    <li>✅ 扩大双击区域：在图片容器上也绑定点击事件，确保小图片也能响应</li>
                    <li>✅ 智能拖拽判断：根据图片大小动态调整拖拽区域阈值</li>
                    <li>✅ 拖拽条件限制：只有移动距离超过5px且持续时间超过100ms才触发拖拽</li>
                    <li>✅ 事件冲突避免：短距离移动或快速点击不会触发拖拽移动</li>
                </ul>

                <p><span class="info">技术细节：</span></p>
                <ul>
                    <li>拖拽区域阈值：最小20px，最大50px，根据图片尺寸的30%动态调整</li>
                    <li>双击检测：300ms内的两次点击识别为双击</li>
                    <li>拖拽触发：移动距离>5px且持续时间>100ms</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 完整修复内容总结</div>
            <div class="test-description">
                <p><span class="success">问题1 - 保存后重新进入页面图片不能操作：</span></p>
                <ul>
                    <li>✅ 改进了 <code>makeImageResizable</code> 方法，对已有功能的图片重新绑定事件</li>
                    <li>✅ 完善了 <code>rebindImageEvents</code> 方法，通过克隆节点清除旧事件</li>
                    <li>✅ 改进了 <code>setupImageFeatures</code> 方法，避免重复处理</li>
                </ul>

                <p><span class="success">问题2 - 重新上传图片后之前图片恢复初始状态：</span></p>
                <ul>
                    <li>✅ 在 <code>setupImageFeatures</code> 中先收集图片信息，避免DOM变化影响</li>
                    <li>✅ 对已包装的图片重新绑定事件而不是重新创建</li>
                </ul>

                <p><span class="success">问题3 - 拖拽后出现不能拖拽：</span></p>
                <ul>
                    <li>✅ 在拖拽结束后调用 <code>rebindImageEvents</code> 重新绑定事件</li>
                    <li>✅ 通过克隆节点的方式彻底清除旧的事件监听器</li>
                </ul>

                <p><span class="success">问题4 - 操作图片错位问题：</span></p>
                <ul>
                    <li>✅ 在事件绑定时添加图片src日志，便于调试</li>
                    <li>✅ 改进事件绑定逻辑，确保事件绑定到正确的图片</li>
                </ul>

                <p><span class="success">问题5 - 双击预览有时失效：</span></p>
                <ul>
                    <li>✅ 在所有事件重新绑定时都包含双击预览事件</li>
                    <li>✅ 通过克隆节点避免事件冲突</li>
                </ul>

                <p><span class="success">问题6&7 - 拖拽到空白区域和精确位置：</span></p>
                <ul>
                    <li>✅ 重写了 <code>findBestInsertionPoint</code> 方法</li>
                    <li>✅ 添加了 <code>getCaretPositionFromPoint</code> 和 <code>insertAtRange</code> 方法</li>
                    <li>✅ 支持精确拖拽到文字间和空白行</li>
                    <li>✅ 改进拖拽结束逻辑，确保正确插入</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 主要修改的方法</div>
            <div class="test-description">
                <ol>
                    <li><strong>setupImageDrag():</strong> 改进拖拽逻辑，支持拖拽到任意位置</li>
                    <li><strong>findBestInsertionPoint():</strong> 新增方法，智能计算最佳插入位置</li>
                    <li><strong>setupImageResize():</strong> 添加调试日志，确保控制点正确显示</li>
                    <li><strong>makeImageResizable():</strong> 将点击事件改为双击事件</li>
                    <li><strong>setupImageFeatures():</strong> 改进已有图片的功能绑定</li>
                    <li><strong>模板和样式:</strong> 替换弹窗预览为覆盖层预览</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 完整测试步骤</div>
            <div class="test-description">
                <p><span class="info">请按以下步骤测试修复后的功能：</span></p>
                <ol>
                    <li><strong>双击预览功能测试：</strong>
                        <ul>
                            <li>上传一张图片，保持原始大小，双击验证预览功能</li>
                            <li>将图片缩放到很小（如50px宽度），再次双击验证预览</li>
                            <li>在图片边缘区域双击，验证是否能正常预览</li>
                            <li>快速单击图片，验证不会误触发预览</li>
                        </ul>
                    </li>
                    <li><strong>拖拽与双击冲突测试：</strong>
                        <ul>
                            <li>在图片中心区域快速点击两次，验证触发双击预览而非拖拽</li>
                            <li>在图片中心区域按住并轻微移动（<5px），验证不会触发拖拽</li>
                            <li>在图片中心区域按住并明显移动（>5px），验证触发拖拽</li>
                            <li>拖拽过程中快速松开鼠标，验证图片不会丢失</li>
                        </ul>
                    </li>
                    <li><strong>精确拖拽测试：</strong>
                        <ul>
                            <li>在编辑器中输入一些文字，如"这是第一行文字"</li>
                            <li>按回车创建几个空白行</li>
                            <li>拖拽图片到空白行，验证能正确插入</li>
                            <li>拖拽图片到文字中间，验证能精确插入到字符间</li>
                        </ul>
                    </li>
                    <li><strong>保存重载测试：</strong>
                        <ul>
                            <li>操作图片（拖拽、缩放）后保存内容</li>
                            <li>重新进入页面加载保存的内容</li>
                            <li>验证所有图片功能（缩放、拖拽、预览）仍然正常</li>
                        </ul>
                    </li>
                    <li><strong>混合操作测试：</strong>
                        <ul>
                            <li>先上传几张图片并操作</li>
                            <li>再上传新图片</li>
                            <li>验证之前的图片功能没有丢失</li>
                            <li>验证新图片也有完整功能</li>
                        </ul>
                    </li>
                    <li><strong>连续拖拽测试：</strong>
                        <ul>
                            <li>拖拽同一张图片多次到不同位置</li>
                            <li>验证每次拖拽后图片功能都正常</li>
                            <li>验证不会出现"拖拽一次后无法再拖拽"的问题</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🐛 调试信息</div>
            <div class="test-description">
                <p>如果功能仍有问题，请检查浏览器控制台的以下关键日志：</p>

                <p><span class="info">正常操作日志：</span></p>
                <ul>
                    <li><code>[图片功能] 找到图片数量: X</code> - 确认图片被正确识别</li>
                    <li><code>[图片功能] 开始为图片添加功能: [图片URL]</code> - 确认功能绑定</li>
                    <li><code>[图片功能] 重新绑定事件到图片: [图片URL]</code> - 确认事件重新绑定</li>
                    <li><code>[图片缩放] 鼠标进入，显示控制点</code> - 确认缩放功能触发</li>
                    <li><code>[图片功能] 双击预览图片</code> - 确认双击预览功能触发</li>
                    <li><code>[拖拽移动] 开始拖拽，图片: [图片URL]</code> - 确认拖拽功能触发</li>
                    <li><code>[拖拽移动] 未达到拖拽条件，取消移动</code> - 短距离移动被正确过滤</li>
                    <li><code>[拖拽定位] 目标位置: {x: X, y: Y}</code> - 确认拖拽目标位置</li>
                    <li><code>[拖拽移动] 已从原位置移除容器</code> - 确认容器移除</li>
                    <li><code>[拖拽移动] 插入到指定位置之前/末尾</code> - 确认插入成功</li>
                </ul>

                <p><span class="error">错误和恢复日志：</span></p>
                <ul>
                    <li><code>[拖拽移动] 容器或图片不在DOM中，取消拖拽</code> - 拖拽前安全检查</li>
                    <li><code>[拖拽移动] 移动容器失败: [错误信息]</code> - 移动失败</li>
                    <li><code>[拖拽移动] 紧急恢复：图片已重新添加到编辑器</code> - 自动恢复</li>
                    <li><code>[图片检查] 发现X张孤立图片，重新包装</code> - 自动修复孤立图片</li>
                    <li><code>[图片检查] 发现空容器，移除</code> - 清理空容器</li>
                </ul>

                <p><span class="error">问题排查指南：</span></p>
                <ul>
                    <li><strong>图片消失：</strong> 查看是否有"紧急恢复"或"孤立图片"的日志</li>
                    <li><strong>拖拽失效：</strong> 检查是否有"取消拖拽"或"移动容器失败"的日志</li>
                    <li><strong>控制点不显示：</strong> 检查是否有"鼠标进入，显示控制点"的日志</li>
                    <li><strong>拖拽位置错误：</strong> 检查"拖拽定位"和"插入到"的日志</li>
                    <li><strong>图片重复：</strong> 查看是否有"发现空容器"的清理日志</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 注意事项</div>
            <div class="test-description">
                <ul>
                    <li>确保在 Vue 项目中正确引入了修改后的 TiptapEditor 组件</li>
                    <li>如果使用了缓存，请清除浏览器缓存或强制刷新</li>
                    <li>图片拖拽功能只在图片中心30px范围内有效，避免与缩放功能冲突</li>
                    <li>预览功能现在是双击触发，单击不会有任何反应</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
