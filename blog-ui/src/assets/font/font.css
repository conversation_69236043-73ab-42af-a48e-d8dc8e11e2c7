@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Caliste";
    /* 引用字体包 */
    src: url('/fonts/Caliste.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Billstone";
    /* 引用字体包 */
    src: url('/fonts/Billstone.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Howdy";
    /* 引用字体包 */
    src: url('/fonts/Howdy.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Miskan";
    /* 引用字体包 */
    src: url('/fonts/Miskan.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "FandolSong-Bold";
    /* 引用字体包 */
    src: url('/fonts/FandolSong-Bold.otf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "SourceHanSansCN";
    /* 引用字体包 */
    src: url('/fonts/_思源黑体SourceHanSansCN-Bold.otf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "kaiti";
    /* 引用字体包 */
    src: url('/fonts/王汉宗粗楷体简.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "fangsong";
    /* 引用字体包 */
    src: url('/fonts/王汉宗中仿宋简.ttf');
    font-weight: normal;
    font-style: normal;
}
