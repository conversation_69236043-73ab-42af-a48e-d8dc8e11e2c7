<template>
  <div>
    <el-upload
      :action="uploadUrl"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      name="file"
      :show-file-list="false"
      :headers="headers"
      style="display: none"
      ref="upload"
      v-if="this.type == 'url'"
    >
    </el-upload>
    <div class="editor-toolbar">
      <el-button-group>
        <el-button :class="{ 'is-active': isActive('bold') }" @click="toggleFormat('bold')">
          <i class="el-icon-bold"></i>
        </el-button>
        <el-button :class="{ 'is-active': isActive('italic') }" @click="toggleFormat('italic')">
          <i class="el-icon-italic"></i>
        </el-button>
        <el-button :class="{ 'is-active': isActive('underline') }" @click="toggleFormat('underline')">
          <i class="el-icon-text-underline"></i>
        </el-button>
        <el-button :class="{ 'is-active': isActive('strike') }" @click="toggleFormat('strike')">
          <i class="el-icon-delete"></i>
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button :class="{ 'is-active': isActive('blockquote') }" @click="toggleFormat('blockquote')">
          <i class="el-icon-quote-right"></i>
        </el-button>
        <el-button :class="{ 'is-active': isActive('code-block') }" @click="toggleFormat('code-block')">
          <i class="el-icon-s-operation"></i>
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button :class="{ 'is-active': isActive('list', 'ordered') }" @click="toggleList('ordered')">
          <i class="el-icon-tickets"></i>
        </el-button>
        <el-button :class="{ 'is-active': isActive('list', 'bullet') }" @click="toggleList('bullet')">
          <i class="el-icon-menu"></i>
        </el-button>
      </el-button-group>

      <!-- 字体选择器 -->
      <el-dropdown trigger="click" @command="handleFont">
        <el-button>
          <i class="el-icon-edit"></i> 字体
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="sans-serif">默认字体</el-dropdown-item>
          <el-dropdown-item command="serif">衣线字体</el-dropdown-item>
          <el-dropdown-item command="monospace">等宽字体</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- 字号选择器 -->
      <el-dropdown trigger="click" @command="handleFontSize">
        <el-button>
          <i class="el-icon-document"></i> 字号
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="small">小号</el-dropdown-item>
          <el-dropdown-item command="normal">正常</el-dropdown-item>
          <el-dropdown-item command="large">大号</el-dropdown-item>
          <el-dropdown-item command="huge">超大</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- 文字颜色选择器 -->
      <el-popover placement="bottom" width="200" trigger="click">
        <div class="color-picker">
          <div class="color-row">
            <div class="color-item" style="background-color: #000000" @click="setColor('#000000')"></div>
            <div class="color-item" style="background-color: #e60000" @click="setColor('#e60000')"></div>
            <div class="color-item" style="background-color: #ff9900" @click="setColor('#ff9900')"></div>
            <div class="color-item" style="background-color: #ffff00" @click="setColor('#ffff00')"></div>
            <div class="color-item" style="background-color: #008a00" @click="setColor('#008a00')"></div>
          </div>
          <div class="color-row">
            <div class="color-item" style="background-color: #0066cc" @click="setColor('#0066cc')"></div>
            <div class="color-item" style="background-color: #9933ff" @click="setColor('#9933ff')"></div>
            <div class="color-item" style="background-color: #ffffff; border: 1px solid #ccc" @click="setColor('#ffffff')"></div>
            <div class="color-item" style="background-color: #facccc" @click="setColor('#facccc')"></div>
            <div class="color-item" style="background-color: #ffebcc" @click="setColor('#ffebcc')"></div>
          </div>
          <div class="color-row">
            <div class="color-item" style="background-color: #ffffcc" @click="setColor('#ffffcc')"></div>
            <div class="color-item" style="background-color: #ccffcc" @click="setColor('#ccffcc')"></div>
            <div class="color-item" style="background-color: #ccffff" @click="setColor('#ccffff')"></div>
            <div class="color-item" style="background-color: #cce5ff" @click="setColor('#cce5ff')"></div>
            <div class="color-item" style="background-color: #eeccff" @click="setColor('#eeccff')"></div>
          </div>
        </div>
        <el-button slot="reference">
          <i class="el-icon-brush" style="color: red;"></i> 文字颜色
        </el-button>
      </el-popover>

      <!-- 背景颜色选择器 -->
      <el-popover placement="bottom" width="200" trigger="click">
        <div class="color-picker">
          <div class="color-row">
            <div class="color-item" style="background-color: #000000" @click="setBackground('#000000')"></div>
            <div class="color-item" style="background-color: #e60000" @click="setBackground('#e60000')"></div>
            <div class="color-item" style="background-color: #ff9900" @click="setBackground('#ff9900')"></div>
            <div class="color-item" style="background-color: #ffff00" @click="setBackground('#ffff00')"></div>
            <div class="color-item" style="background-color: #008a00" @click="setBackground('#008a00')"></div>
          </div>
          <div class="color-row">
            <div class="color-item" style="background-color: #0066cc" @click="setBackground('#0066cc')"></div>
            <div class="color-item" style="background-color: #9933ff" @click="setBackground('#9933ff')"></div>
            <div class="color-item" style="background-color: #ffffff; border: 1px solid #ccc" @click="setBackground('#ffffff')"></div>
            <div class="color-item" style="background-color: #facccc" @click="setBackground('#facccc')"></div>
            <div class="color-item" style="background-color: #ffebcc" @click="setBackground('#ffebcc')"></div>
          </div>
          <div class="color-row">
            <div class="color-item" style="background-color: #ffffcc" @click="setBackground('#ffffcc')"></div>
            <div class="color-item" style="background-color: #ccffcc" @click="setBackground('#ccffcc')"></div>
            <div class="color-item" style="background-color: #ccffff" @click="setBackground('#ccffff')"></div>
            <div class="color-item" style="background-color: #cce5ff" @click="setBackground('#cce5ff')"></div>
            <div class="color-item" style="background-color: #eeccff" @click="setBackground('#eeccff')"></div>
          </div>
        </div>
        <el-button slot="reference">
          <i class="el-icon-brush" style="background-color: yellow; padding: 0 5px;"></i> 背景颜色
        </el-button>
      </el-popover>

      <el-dropdown trigger="click" @command="handleHeader">
        <el-button>
          <i class="el-icon-s-grid"></i> 标题
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">标题 1</el-dropdown-item>
          <el-dropdown-item command="2">标题 2</el-dropdown-item>
          <el-dropdown-item command="3">标题 3</el-dropdown-item>
          <el-dropdown-item command="4">标题 4</el-dropdown-item>
          <el-dropdown-item command="5">标题 5</el-dropdown-item>
          <el-dropdown-item command="6">标题 6</el-dropdown-item>
          <el-dropdown-item command="normal">正文</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-popover placement="bottom" width="300" trigger="click">
        <div class="emoji-container">
          <span
            v-for="(item, index) in emoji"
            :key="index"
            class="emoji-item"
            @click="insertEmoji(item)"
          >
            {{ item }}
          </span>
        </div>
        <el-button slot="reference">
          <i class="el-icon-magic-stick"></i> 表情
        </el-button>
      </el-popover>

      <el-button @click="handleImageUpload">
        <i class="el-icon-picture"></i> 图片
      </el-button>
    </div>
    <div class="editor" ref="editor" :style="styles"></div>
  </div>
</template>

<script>
import Quill from "quill";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { getToken } from "@/utils/auth";
import emoji from "@/utils/emoji";

export default {
  name: "EnhancedEditor",
  props: {
    /* 编辑器的内容 */
    value: {
      type: String,
      default: "",
    },
    /* 高度 */
    height: {
      type: Number,
      default: null,
    },
    /* 最小高度 */
    minHeight: {
      type: Number,
      default: null,
    },
    /* 只读 */
    readOnly: {
      type: Boolean,
      default: false,
    },
    /* 上传文件大小限制(MB) */
    fileSize: {
      type: Number,
      default: 5,
    },
    /* 类型（base64格式、url格式） */
    type: {
      type: String,
      default: "url",
    }
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken()
      },
      Quill: null,
      currentValue: "",
      emoji: emoji,
      options: {
        theme: "snow",
        bounds: document.body,
        debug: "warn",
        modules: {
          // 工具栏配置
          toolbar: false, // 禁用默认工具栏，使用自定义工具栏
        },
        placeholder: "请输入内容",
        readOnly: this.readOnly,
      },
    };
  },
  computed: {
    styles() {
      let style = {};
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`;
      }
      if (this.height) {
        style.height = `${this.height}px`;
      }
      return style;
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.currentValue) {
          this.currentValue = val === null ? "" : val;
          if (this.Quill) {
            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.Quill = null;
  },
  methods: {
    init() {
      const editor = this.$refs.editor;

      // 注册字体
      const fonts = ['sans-serif', 'serif', 'monospace'];
      const FontAttributor = Quill.import('attributors/class/font');
      FontAttributor.whitelist = fonts;
      Quill.register(FontAttributor, true);

      // 注册字体大小
      const Size = Quill.import('attributors/style/size');
      Size.whitelist = ['small', false, 'large', 'huge'];
      Quill.register(Size, true);

      // 注册颜色和背景色
      const ColorAttributor = Quill.import('attributors/style/color');
      const BackgroundAttributor = Quill.import('attributors/style/background');
      Quill.register(ColorAttributor, true);
      Quill.register(BackgroundAttributor, true);

      // 创建Quill实例
      this.Quill = new Quill(editor, this.options);
      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);

      // 监听事件
      this.Quill.on("text-change", (delta, oldDelta, source) => {
        const html = this.$refs.editor.children[0].innerHTML;
        const text = this.Quill.getText();
        const quill = this.Quill;
        this.currentValue = html;
        this.$emit("input", html);
        this.$emit("on-change", { html, text, quill });
      });
      this.Quill.on("text-change", (delta, oldDelta, source) => {
        this.$emit("on-text-change", delta, oldDelta, source);
      });
      this.Quill.on("selection-change", (range, oldRange, source) => {
        this.$emit("on-selection-change", range, oldRange, source);
      });
      this.Quill.on("editor-change", (eventName, ...args) => {
        this.$emit("on-editor-change", eventName, ...args);
      });
    },
    // 检查格式是否激活
    isActive(format, value) {
      if (!this.Quill) return false;
      if (value) {
        return this.Quill.getFormat()[format] === value;
      }
      return this.Quill.getFormat()[format];
    },
    // 切换格式
    toggleFormat(format) {
      if (!this.Quill) return;
      this.Quill.focus();
      this.Quill.format(format, !this.isActive(format));
    },
    // 切换列表
    toggleList(type) {
      if (!this.Quill) return;
      this.Quill.focus();
      const isActive = this.isActive('list', type);
      this.Quill.format('list', isActive ? false : type);
    },
    // 设置字体
    handleFont(font) {
      if (!this.Quill) return;
      this.Quill.focus();
      this.Quill.format('font', font === 'sans-serif' ? false : font);
    },
    // 设置字体大小
    handleFontSize(size) {
      if (!this.Quill) return;
      this.Quill.focus();
      switch (size) {
        case 'small':
          this.Quill.format('size', 'small');
          break;
        case 'normal':
          this.Quill.format('size', false);
          break;
        case 'large':
          this.Quill.format('size', 'large');
          break;
        case 'huge':
          this.Quill.format('size', 'huge');
          break;
      }
    },
    // 设置文字颜色
    setColor(color) {
      if (!this.Quill) return;
      this.Quill.focus();
      this.Quill.format('color', color);
    },
    // 设置背景颜色
    setBackground(color) {
      if (!this.Quill) return;
      this.Quill.focus();
      this.Quill.format('background', color);
    },
    // 设置标题
    handleHeader(level) {
      if (!this.Quill) return;
      this.Quill.focus();
      if (level === 'normal') {
        this.Quill.format('header', false);
      } else {
        this.Quill.format('header', parseInt(level));
      }
    },
    // 插入表情
    insertEmoji(emoji) {
      if (!this.Quill) return;
      this.Quill.focus();
      const range = this.Quill.getSelection();
      if (range) {
        this.Quill.insertText(range.index, emoji);
      }
    },
    // 处理图片上传
    handleImageUpload() {
      if (this.type == 'url') {
        this.$refs.upload.$children[0].$refs.input.click();
      }
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/svg+xml"];
      const isImage = type.includes(file.type);
      // 检验文件格式
      if (!isImage) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      return true;
    },
    handleUploadSuccess(res, file) {
      // 如果上传成功
      if (res.code == 200) {
        // 获取富文本组件实例
        let quill = this.Quill;
        // 获取光标所在位置
        let length = quill.getSelection().index;
        // 插入图片  res.url为服务器返回的图片地址
        quill.insertEmbed(length, "image", process.env.VUE_APP_BASE_API + res.fileName);
        // 调整光标到最后
        quill.setSelection(length + 1);
      } else {
        this.$message.error("图片插入失败");
      }
    },
    handleUploadError() {
      this.$message.error("图片插入失败");
    },
  },
};
</script>

<style>
.editor, .ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}
.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
  content: "衬线字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
  content: "等宽字体";
}

/* 自定义工具栏样式 */
.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  border: 1px solid #ccc;
  border-bottom: none;
  background-color: #f8f8f8;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.editor-toolbar .el-button-group {
  margin-right: 10px;
  margin-bottom: 5px;
}

.editor-toolbar .el-button {
  padding: 5px 10px;
}

.editor-toolbar .el-button.is-active {
  background-color: #ecf5ff;
  color: #409EFF;
}

.editor {
  border: 1px solid #ccc;
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 表情样式 */
.emoji-container {
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  font-size: 20px;
  padding: 5px;
  cursor: pointer;
  transition: all 0.2s;
}

.emoji-item:hover {
  transform: scale(1.2);
  background-color: #f0f0f0;
  border-radius: 4px;
}

/* 颜色选择器样式 */
.color-picker {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.color-row {
  display: flex;
  gap: 5px;
}

.color-item {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
}

.color-item:hover {
  transform: scale(1.2);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
</style>
