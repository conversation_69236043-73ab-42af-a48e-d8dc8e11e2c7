<template>
  <div class="test-editor">
    <h3>测试编辑器组件</h3>
    <div class="editor-toolbar">
      <button @click="makeBold">粗体</button>
      <button @click="makeItalic">斜体</button>
    </div>
    <textarea v-model="content" @input="updateContent" class="editor-content"></textarea>
  </div>
</template>

<script>
export default {
  name: 'TestEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      content: this.value
    };
  },
  watch: {
    value(newVal) {
      this.content = newVal;
    }
  },
  methods: {
    updateContent() {
      this.$emit('input', this.content);
    },
    makeBold() {
      this.content = `<strong>${this.content}</strong>`;
      this.updateContent();
    },
    makeItalic() {
      this.content = `<em>${this.content}</em>`;
      this.updateContent();
    }
  }
};
</script>

<style scoped>
.test-editor {
  border: 3px solid red;
  padding: 10px;
  margin: 10px 0;
  background-color: #f8f8f8;
}

.editor-toolbar {
  margin-bottom: 10px;
}

.editor-toolbar button {
  margin-right: 5px;
  padding: 5px 10px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.editor-content {
  width: 100%;
  min-height: 200px;
  border: 1px solid #dcdfe6;
  padding: 10px;
  box-sizing: border-box;
}
</style>
