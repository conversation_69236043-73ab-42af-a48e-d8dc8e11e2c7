<template>
  <el-dialog
    title="文件上传"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false">
    
    <div class="upload-progress">
      <div class="file-info">
        <i class="el-icon-document"></i>
        <div class="file-details">
          <div class="file-name">{{ fileName }}</div>
          <div class="file-size">{{ formatFileSize(fileSize) }}</div>
        </div>
      </div>
      
      <div class="progress-section">
        <div class="stage-info">
          <span class="stage-text">{{ stageText }}</span>
          <span class="progress-text">{{ progressText }}</span>
        </div>
        
        <el-progress
          :percentage="percentage"
          :status="progressStatus"
          :stroke-width="8"
          :show-text="false">
        </el-progress>
        
        <div class="progress-details">
          <span>{{ message }}</span>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer" v-if="showCancelButton">
      <el-button @click="handleCancel">取消上传</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'UploadProgress',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fileName: {
      type: String,
      default: ''
    },
    fileSize: {
      type: Number,
      default: 0
    },
    stage: {
      type: String,
      default: 'uploading' // calculating, uploading, merging, completed, error
    },
    percentage: {
      type: Number,
      default: 0
    },
    message: {
      type: String,
      default: ''
    }
  },
  computed: {
    stageText() {
      const stageMap = {
        calculating: '计算文件标识',
        uploading: '上传中',
        merging: '合并文件',
        completed: '上传完成',
        error: '上传失败'
      }
      return stageMap[this.stage] || '上传中'
    },
    progressText() {
      if (this.stage === 'completed') {
        return '100%'
      } else if (this.stage === 'error') {
        return '失败'
      }
      return `${this.percentage}%`
    },
    progressStatus() {
      if (this.stage === 'completed') {
        return 'success'
      } else if (this.stage === 'error') {
        return 'exception'
      }
      return null
    },
    showCancelButton() {
      return this.stage !== 'completed' && this.stage !== 'error'
    }
  },
  methods: {
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-progress {
  .file-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    
    .el-icon-document {
      font-size: 32px;
      color: #409EFF;
      margin-right: 12px;
    }
    
    .file-details {
      flex: 1;
      
      .file-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
        word-break: break-all;
      }
      
      .file-size {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .progress-section {
    .stage-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .stage-text {
        font-weight: 500;
        color: #303133;
      }
      
      .progress-text {
        font-size: 14px;
        color: #409EFF;
        font-weight: 500;
      }
    }
    
    .progress-details {
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      text-align: center;
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
