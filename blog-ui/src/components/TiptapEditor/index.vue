<template>
  <div class="create-post">
    <div class="editor-container">
      <div class="editor-toolbar">
        <div>
          <div class="bars br2 nbl">
            <div>
              <el-select
                ref="fontSelect"
                v-model="fontFamilyValue"
                @change="selectFont"
                @focus="onSelectFocus"
                @blur="onSelectBlur"
                placeholder="字体"
                style="width: 80px;"
                class="custom-select"
                size="mini"
                clearable
                popper-class="editor-select-dropdown">
                <el-option v-for="item in fontFamily" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select
                ref="sizeSelect"
                v-model="fontSizeValue"
                @change="selectFontSize"
                @focus="onSelectFocus"
                @blur="onSelectBlur"
                placeholder="size"
                style="width: 50px;font-size: 12px;"
                size="mini"
                clearable
                popper-class="editor-select-dropdown">
                <el-option v-for="item in fontSize" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div>
              <a :class="{ 'is-active': isBold }" @mousedown="preventBlur" @click="toggleBold" class="bar1" title="粗体"></a>
              <a :class="{ 'is-active': isItalic }" @mousedown="preventBlur" @click="toggleItalic" class="bar2" title="斜体"></a>
              <a :class="{ 'is-active': isStrike }" @mousedown="preventBlur" @click="toggleStrike" class="bar3" title="删除线"></a>
              <el-popover ref="colorPopover" width="auto" class="box-item" title="" placement="bottom" trigger="click">
                <template #reference>
                  <a @mousedown="preventBlur" class="bar4" title="字体颜色"></a>
                </template>
                <div class="color-picker">
                  <div class="color-row">
                    <span class="color-item" style="background-color: Black" @mousedown="preventBlur" @click="setFontColor('Black')" title="黑色"></span>
                    <span class="color-item" style="background-color: Sienna" @mousedown="preventBlur" @click="setFontColor('Sienna')" title="赭色"></span>
                    <span class="color-item" style="background-color: DarkOliveGreen" @mousedown="preventBlur" @click="setFontColor('DarkOliveGreen')" title="暗橄榄绿色"></span>
                    <span class="color-item" style="background-color: DarkGreen" @mousedown="preventBlur" @click="setFontColor('DarkGreen')" title="暗绿色"></span>
                    <span class="color-item" style="background-color: DarkSlateBlue" @mousedown="preventBlur" @click="setFontColor('DarkSlateBlue')" title="暗灰蓝色"></span>
                    <span class="color-item" style="background-color: Navy" @mousedown="preventBlur" @click="setFontColor('Navy')" title="海军色"></span>
                    <span class="color-item" style="background-color: Indigo" @mousedown="preventBlur" @click="setFontColor('Indigo')" title="靛青色"></span>
                    <span class="color-item" style="background-color: DarkSlateGray" @mousedown="preventBlur" @click="setFontColor('DarkSlateGray')" title="墨绿色"></span>
                  </div>
                  <div class="color-row">
                    <span class="color-item" style="background-color: DarkRed" @mousedown="preventBlur" @click="setFontColor('DarkRed')" title="暗红色"></span>
                    <span class="color-item" style="background-color: DarkOrange" @mousedown="preventBlur" @click="setFontColor('DarkOrange')" title="暗桔黄色"></span>
                    <span class="color-item" style="background-color: Olive" @mousedown="preventBlur" @click="setFontColor('Olive')" title="橄榄色"></span>
                    <span class="color-item" style="background-color: Green" @mousedown="preventBlur" @click="setFontColor('Green')" title="绿色"></span>
                    <span class="color-item" style="background-color: Teal" @mousedown="preventBlur" @click="setFontColor('Teal')" title="水鸭色"></span>
                    <span class="color-item" style="background-color: Blue" @mousedown="preventBlur" @click="setFontColor('Blue')" title="蓝色"></span>
                    <span class="color-item" style="background-color: SlateGray" @mousedown="preventBlur" @click="setFontColor('SlateGray')" title="灰石色"></span>
                    <span class="color-item" style="background-color: DimGray" @mousedown="preventBlur" @click="setFontColor('DimGray')" title="暗灰色"></span>
                  </div>
                  <div class="color-row">
                    <span class="color-item" style="background-color: Red" @mousedown="preventBlur" @click="setFontColor('Red')" title="红色"></span>
                    <span class="color-item" style="background-color: SandyBrown" @mousedown="preventBlur" @click="setFontColor('SandyBrown')" title="沙褐色"></span>
                    <span class="color-item" style="background-color: YellowGreen" @mousedown="preventBlur" @click="setFontColor('YellowGreen')" title="黄绿色"></span>
                    <span class="color-item" style="background-color: SeaGreen" @mousedown="preventBlur" @click="setFontColor('SeaGreen')" title="海绿色"></span>
                    <span class="color-item" style="background-color: MediumTurquoise" @mousedown="preventBlur" @click="setFontColor('MediumTurquoise')" title="间绿宝石"></span>
                    <span class="color-item" style="background-color: RoyalBlue" @mousedown="preventBlur" @click="setFontColor('RoyalBlue')" title="皇家蓝"></span>
                    <span class="color-item" style="background-color: Purple" @mousedown="preventBlur" @click="setFontColor('Purple')" title="紫色"></span>
                    <span class="color-item" style="background-color: Gray" @mousedown="preventBlur" @click="setFontColor('Gray')" title="灰色"></span>
                  </div>
                  <div class="color-row">
                    <span class="color-item" style="background-color: Magenta" @mousedown="preventBlur" @click="setFontColor('Magenta')" title="红紫色"></span>
                    <span class="color-item" style="background-color: Orange" @mousedown="preventBlur" @click="setFontColor('Orange')" title="橙色"></span>
                    <span class="color-item" style="background-color: Yellow" @mousedown="preventBlur" @click="setFontColor('Yellow')" title="黄色"></span>
                    <span class="color-item" style="background-color: Lime" @mousedown="preventBlur" @click="setFontColor('Lime')" title="酸橙色"></span>
                    <span class="color-item" style="background-color: Cyan" @mousedown="preventBlur" @click="setFontColor('Cyan')" title="青色"></span>
                    <span class="color-item" style="background-color: DeepSkyBlue" @mousedown="preventBlur" @click="setFontColor('DeepSkyBlue')" title="深天蓝色"></span>
                    <span class="color-item" style="background-color: DarkOrchid" @mousedown="preventBlur" @click="setFontColor('DarkOrchid')" title="暗紫色"></span>
                    <span class="color-item" style="background-color: Silver" @mousedown="preventBlur" @click="setFontColor('Silver')" title="银色"></span>
                  </div>
                  <div class="color-row">
                    <span class="color-item" style="background-color: Pink" @mousedown="preventBlur" @click="setFontColor('Pink')" title="粉色"></span>
                    <span class="color-item" style="background-color: Wheat" @mousedown="preventBlur" @click="setFontColor('Wheat')" title="浅黄色"></span>
                    <span class="color-item" style="background-color: LemonChiffon" @mousedown="preventBlur" @click="setFontColor('LemonChiffon')" title="柠檬绸色"></span>
                    <span class="color-item" style="background-color: PaleGreen" @mousedown="preventBlur" @click="setFontColor('PaleGreen')" title="苍绿色"></span>
                    <span class="color-item" style="background-color: PaleTurquoise" @mousedown="preventBlur" @click="setFontColor('PaleTurquoise')" title="苍宝石绿"></span>
                    <span class="color-item" style="background-color: LightBlue" @mousedown="preventBlur" @click="setFontColor('LightBlue')" title="亮蓝色"></span>
                    <span class="color-item" style="background-color: Plum" @mousedown="preventBlur" @click="setFontColor('Plum')" title="洋李色"></span>
                    <span class="color-item" style="background-color: White" @mousedown="preventBlur" @click="setFontColor('White')" title="白色"></span>
                  </div>
                </div>
              </el-popover>
              <a :class="{ 'is-active': isLink }" @mousedown="preventBlur" @click="addLink" class="bar5" title="链接"></a>
            </div>
          </div>
          <div class="bars br2">
            <div>
              <a @mousedown="preventBlur" @click="setAlign('auto')" title="自动排版" class="bar6"></a>
              <a @mousedown="preventBlur" @click="setAlign('left')" title="居左" class="bar7"></a>
              <a @mousedown="preventBlur" @click="setAlign('center')" title="居中" class="bar8"></a>
              <a @mousedown="preventBlur" @click="setAlign('right')" title="居右" class="bar9"></a>
            </div>
            <div>
              <a @mousedown="preventBlur" @click="setFloating('left')" title="左浮动" class="bar10"></a>
              <a @mousedown="preventBlur" @click="setFloating('right')" title="右浮动" class="bar11"></a>
              <a :class="{ 'is-active': isOrderedList }" @mousedown="preventBlur" @click="toggleOrderedList" title="有序列表" class="bar12"></a>
              <a :class="{ 'is-active': isBulletList }" @mousedown="preventBlur" @click="toggleBulletList" title="无序列表" class="bar13"></a>
            </div>
          </div>
          <div class="bars br2">
            <div style="display: flex;">
              <el-popover width="300px" class="box-item" title="" placement="bottom" trigger="hover">
                <template #reference>
                  <div @mousedown="preventBlur" style="cursor: pointer;">
                    <a title="表情" class="bar14"></a>
                    <div class="msgs">表情</div>
                  </div>
                </template>
                <div class="emoji-grid">
                  <span @mousedown="preventBlur" @click="onSelectEmoji(item)" class="emoji-item" v-for="(item, i) in emoji"
                    :key="i" :title="item">{{item}}</span>
                </div>
              </el-popover>

              <el-popover width="250px" class="box-item" title="" placement="bottom">
                <template #reference>
                  <div @mousedown="preventBlur" @click="uploadImage" style="margin: 0 5px;cursor: pointer;">
                    <a title="图片" class="bar15"></a>
                    <div class="msgs">图片</div>
                  </div>
                </template>
                <div>
                  上传图片<br />
                  文件大小: 小于 100MB,<br />
                  支持格式: jpg,jpeg,gif,png,bmp
                </div>
              </el-popover>

              <el-popover width="200px" class="box-item" title="" placement="bottom">
                <template #reference>
                  <div @mousedown="preventBlur" @click="uploadFile" style="cursor: pointer;">
                    <a title="附件" class="bar16"></a>
                    <div class="msgs">附件</div>
                  </div>
                </template>
                <div>
                  上传文件<br />
                  文件大小: 小于 100MB,<br />
                  支持格式: zip,doc,docx,xls,xlsx
                </div>
              </el-popover>
            </div>
          </div>
          <div class="bars br2 nbr">
            <a @mousedown="preventBlur" @click="redo" class="bar17" title="重做"></a>
            <br />
            <a @mousedown="preventBlur" @click="undo" class="bar18" title="撤销"></a>
            <br />
<!--            <el-button @click="testFonts" size="mini" type="primary">测试字体</el-button>-->
          </div>
        </div>
      </div>

      <div class="editor-content">
        <div style="max-height: 500px;overflow: auto;">
          <div
            ref="editorContent"
            class="rich-content"
            contenteditable="true"
            @input="handleInput"
            @keydown="handleKeyDown"
            @mouseup="saveSelection"
            @keyup="saveSelection"
            @focus="handleFocus"
            @blur="handleBlur"
            :style="{ minHeight: height + 'px' }">
          </div>
        </div>
      </div>

      <!-- 附件列表 -->
      <div v-if="attachments.length > 0" class="attachment-list">
        <div class="attachment-title">附件列表</div>
        <div class="attachment-items">
          <div v-for="(attachment, index) in attachments" :key="index" class="attachment-item">
            <div class="attachment-info">
              <span class="attachment-icon">📎</span>
              <div class="attachment-details">
                <div class="attachment-name">{{ attachment.fileName }}</div>
                <div class="attachment-meta">
                  {{ formatFileSize(attachment.fileSize) }} • {{ attachment.uploadTime }}
                </div>
              </div>
            </div>
            <div class="attachment-actions">
              <a :href="attachment.fileUrl" target="_blank" class="attachment-download">下载</a>
              <button @click="removeAttachment(index)" class="attachment-remove">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度对话框 -->
    <UploadProgress
      :visible="uploadProgress.visible"
      :fileName="uploadProgress.fileName"
      :fileSize="uploadProgress.fileSize"
      :stage="uploadProgress.stage"
      :percentage="uploadProgress.percentage"
      :message="uploadProgress.message"
      @cancel="cancelUpload"
    />

    <!-- 图片预览覆盖层 -->
    <div v-if="imagePreview.visible" class="image-preview-overlay" @click="closeImagePreview">
      <div class="image-preview-container">
        <img :src="imagePreview.url" alt="预览图片" class="preview-image" />
        <div class="preview-close-btn" @click="closeImagePreview">×</div>
      </div>
    </div>
  </div>
</template>

<script>
import emoji from '@/utils/emoji'
import fontFamily from '@/utils/font-family'
import fontSize from '@/utils/font-size'
import { getToken } from '@/utils/auth'
import { ChunkUploader, simpleUpload } from '@/utils/chunkUpload'
import UploadProgress from '@/components/UploadProgress'

export default {
  name: 'TiptapEditor',
  components: {
    UploadProgress
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    },
    uploadUrl: {
      type: String,
      default: process.env.VUE_APP_BASE_API + '/common/upload'
    }
  },
  data() {
    return {
      emoji,
      fontFamily,
      fontSize,
      fontFamilyValue: '',
      fontSizeValue: '',
      savedRange: null,
      savedSelection: null,
      loadingEditor: false,
      isSelectingFont: false,
      currentUploader: null,
      uploadProgress: {
        visible: false,
        fileName: '',
        fileSize: 0,
        stage: 'uploading',
        percentage: 0,
        message: ''
      },
      // 当前样式状态
      currentStyles: {
        fontFamily: '',
        fontSize: '',
        bold: false,
        italic: false,
        strike: false,
        color: ''
      },
      // 状态跟踪
      isBold: false,
      isItalic: false,
      isStrike: false,
      isBulletList: false,
      isOrderedList: false,
      isLink: false,
      // 附件相关
      attachments: [], // 附件列表
      imagePreview: {
        visible: false,
        url: ''
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initEditor()
      this.setupSelectionListener()
      // 清理可能存在的错误控制点
      this.cleanupResizeHandles()
    })
  },
  beforeDestroy() {
    this.removeSelectionListener()
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.$refs.editorContent && newVal !== this.getContent()) {
          this.$refs.editorContent.innerHTML = newVal
          // 处理新内容中的图片
          this.$nextTick(() => {
            this.fixLoadedContent()
            this.setupImageFeatures()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    initEditor() {
      if (this.value && this.$refs.editorContent) {
        this.$refs.editorContent.innerHTML = this.value

        // 修复内容并为已有图片设置功能
        this.$nextTick(() => {
          this.fixLoadedContent()
          this.setupImageFeatures()
        })
      }
    },

    setupSelectionListener() {
      // 监听全局选区变化
      this.selectionChangeHandler = () => {
        if (!this.isSelectingFont) {
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            // 只有当选区在编辑器内时才保存
            if (this.$refs.editorContent && this.$refs.editorContent.contains(range.commonAncestorContainer)) {
              this.saveSelection()
            }
          }
        }
      }
      document.addEventListener('selectionchange', this.selectionChangeHandler)
    },

    removeSelectionListener() {
      if (this.selectionChangeHandler) {
        document.removeEventListener('selectionchange', this.selectionChangeHandler)
      }
    },

    getContent() {
      return this.$refs.editorContent ? this.$refs.editorContent.innerHTML : ''
    },

    updateContent(skipImageSetup = false) {
      // 清理内容中的错误结构
      this.cleanupImageContainers()

      // 检查图片是否丢失
      this.checkForLostImages()

      const content = this.getContent()
      this.$emit('input', content)
      this.updateToolbarState()
    },

    // 检查图片是否丢失并尝试恢复
    checkForLostImages() {
      if (!this.$refs.editorContent) return

      // 查找所有图片容器
      const containers = this.$refs.editorContent.querySelectorAll('.image-resize-container')
      const orphanImages = this.$refs.editorContent.querySelectorAll('img:not(.image-resize-container img)')

      // 检查孤立的图片（没有被容器包装的）
      if (orphanImages.length > 0) {
        console.log('[图片检查] 发现', orphanImages.length, '张孤立图片，重新包装')
        orphanImages.forEach(img => {
          if (img.src && img.src.includes('upload')) { // 只处理上传的图片
            this.makeImageResizable(img)
          }
        })
      }

      // 检查空的容器
      containers.forEach(container => {
        const img = container.querySelector('img')
        if (!img) {
          console.log('[图片检查] 发现空容器，移除')
          container.remove()
        }
      })
    },

    // 修复加载的内容中的图片结构
    fixLoadedContent() {
      if (!this.$refs.editorContent) return

      console.log('[内容修复] 开始修复加载的内容')

      // 1. 修复图片被嵌套在控制点内的问题
      const handles = this.$refs.editorContent.querySelectorAll('.image-resize-handle')
      handles.forEach(handle => {
        const imgInHandle = handle.querySelector('img')
        if (imgInHandle) {
          console.log('[内容修复] 发现图片被错误嵌套在控制点内')

          // 找到父容器
          const container = handle.closest('.image-resize-container')
          if (container) {
            // 将图片移到容器的正确位置（第一个子元素）
            container.insertBefore(imgInHandle, container.firstChild)
            console.log('[内容修复] 图片已移动到正确位置')
          } else {
            // 如果没有容器，创建一个新的
            const newContainer = document.createElement('div')
            newContainer.className = 'image-resize-container'
            newContainer.style.cssText = `
              position: relative;
              display: inline-block;
              margin: 5px;
              vertical-align: middle;
              line-height: 0;
            `

            // 将图片移到新容器中
            handle.parentNode.insertBefore(newContainer, handle)
            newContainer.appendChild(imgInHandle)
            console.log('[内容修复] 创建新容器并移动图片')
          }
        }
      })

      // 2. 清理损坏的容器结构
      const containers = this.$refs.editorContent.querySelectorAll('.image-resize-container')
      containers.forEach(container => {
        const img = container.querySelector('img')
        const handles = container.querySelectorAll('.image-resize-handle')

        if (!img) {
          // 容器中没有图片，移除整个容器
          console.log('[内容修复] 移除没有图片的容器')
          container.remove()
          return
        }

        // 3. 移除重复的控制点
        const directions = new Set()
        const duplicateHandles = []

        handles.forEach(handle => {
          const direction = handle.dataset.direction
          if (directions.has(direction)) {
            duplicateHandles.push(handle)
          } else {
            directions.add(direction)
          }
        })

        duplicateHandles.forEach(handle => {
          console.log('[内容修复] 移除重复的控制点:', handle.dataset.direction)
          handle.remove()
        })

        // 4. 确保有完整的四个控制点
        const requiredDirections = ['nw', 'ne', 'sw', 'se']
        const existingDirections = Array.from(container.querySelectorAll('.image-resize-handle'))
          .map(h => h.dataset.direction)

        requiredDirections.forEach(direction => {
          if (!existingDirections.includes(direction)) {
            console.log('[内容修复] 补充缺失的控制点:', direction)
            // 这里不创建控制点，让setupImageFeatures来处理
            // 先移除整个容器，让图片重新被包装
            const parent = container.parentNode
            parent.insertBefore(img, container)
            container.remove()
          }
        })
      })

      // 5. 处理孤立的图片（没有被容器包装的）
      const orphanImages = this.$refs.editorContent.querySelectorAll('img:not(.image-resize-container img)')
      orphanImages.forEach(img => {
        console.log('[内容修复] 发现孤立的图片，将重新包装')
        // 这些图片会在setupImageFeatures中被重新包装
      })

      console.log('[内容修复] 内容修复完成')
    },

    // 清理图片容器中的错误结构
    cleanupImageContainers() {
      if (!this.$refs.editorContent) return

      const containers = this.$refs.editorContent.querySelectorAll('.image-resize-container')
      containers.forEach(container => {
        const img = container.querySelector('img')
        const handles = container.querySelectorAll('.image-resize-handle')

        if (img && handles.length > 0) {
          // 检查图片是否被错误地放在控制点内部
          handles.forEach(handle => {
            const imgInHandle = handle.querySelector('img')
            if (imgInHandle) {
              console.log('[内容清理] 修复图片位置错误')
              // 将图片移回容器根部
              container.insertBefore(imgInHandle, handle)
            }
          })

          // 移除重复的控制点
          const uniqueHandles = new Set()
          handles.forEach(handle => {
            const direction = handle.dataset.direction
            if (uniqueHandles.has(direction)) {
              console.log('[内容清理] 移除重复控制点:', direction)
              handle.remove()
            } else {
              uniqueHandles.add(direction)
            }
          })
        }
      })
    },

    updateToolbarState() {
      // 更新工具栏按钮状态
      this.isBold = document.queryCommandState('bold')
      this.isItalic = document.queryCommandState('italic')
      this.isStrike = document.queryCommandState('strikeThrough')
      this.isBulletList = document.queryCommandState('insertUnorderedList')
      this.isOrderedList = document.queryCommandState('insertOrderedList')

      // 检查其他状态
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement
          : range.commonAncestorContainer

        this.isHeading1 = this.isInElement(element, 'H1')
        this.isHeading2 = this.isInElement(element, 'H2')
        this.isHeading3 = this.isInElement(element, 'H3')
        this.isBlockquote = this.isInElement(element, 'BLOCKQUOTE')
        this.isCode = this.isInElement(element, 'CODE')
      }
    },

    isInElement(element, tagName) {
      let current = element
      while (current && current !== this.$refs.editorContent) {
        if (current.tagName === tagName) {
          return true
        }
        current = current.parentElement
      }
      return false
    },

    execCommand(command, value = null) {
      this.restoreSelection()
      document.execCommand(command, false, value)
      this.updateContent()
      this.$refs.editorContent.focus()
      // 保存新的选区状态
      this.saveSelection()
    },

    saveSelection() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        this.savedRange = range.cloneRange()
        this.savedSelection = {
          anchorNode: selection.anchorNode,
          anchorOffset: selection.anchorOffset,
          focusNode: selection.focusNode,
          focusOffset: selection.focusOffset,
          collapsed: range.collapsed
        }

        // 如果有选中文本，保存当前选中文本的样式
        if (!range.collapsed) {
          this.saveCurrentStyles()
        }
      }
      this.updateToolbarState()
    },

    saveCurrentStyles() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement
          : range.commonAncestorContainer

        // 获取当前选中文本的样式
        const computedStyle = window.getComputedStyle(element)
        this.currentStyles.fontFamily = computedStyle.fontFamily
        this.currentStyles.fontSize = computedStyle.fontSize
        this.currentStyles.bold = computedStyle.fontWeight === 'bold' || parseInt(computedStyle.fontWeight) >= 700
        this.currentStyles.italic = computedStyle.fontStyle === 'italic'
        this.currentStyles.color = computedStyle.color
      }
    },

    restoreSelection() {
      if (this.savedRange) {
        const selection = window.getSelection()
        selection.removeAllRanges()
        try {
          const newRange = this.savedRange.cloneRange()
          selection.addRange(newRange)
          // 确保编辑器获得焦点
          this.$refs.editorContent.focus()
        } catch (e) {
          // 如果恢复失败，尝试重新创建选区
          if (this.savedSelection) {
            try {
              const newRange = document.createRange()
              newRange.setStart(this.savedSelection.anchorNode, this.savedSelection.anchorOffset)
              newRange.setEnd(this.savedSelection.focusNode, this.savedSelection.focusOffset)
              selection.addRange(newRange)
              this.$refs.editorContent.focus()
            } catch (e2) {
            }
          }
        }
      }
    },

    handleInput(e) {
      // 如果有当前样式设置，应用到新输入的文本
      if (e.inputType === 'insertText' || e.inputType === 'insertCompositionText') {
        this.applyCurrentStylesToInput()
      }

      this.saveSelection()
      this.updateContent()
    },

    applyCurrentStylesToInput() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const currentNode = range.startContainer

        // 检查当前节点是否已经有样式
        if (currentNode.nodeType === Node.TEXT_NODE) {
          const parentElement = currentNode.parentElement

          // 如果父元素没有我们需要的样式，创建一个新的span
          if (this.needsStyleApplication(parentElement)) {
            const span = document.createElement('span')
            this.applyCurrentStylesToElement(span)

            // 将当前文本节点包装在span中
            try {
              const textContent = currentNode.textContent
              const newTextNode = document.createTextNode(textContent)
              span.appendChild(newTextNode)
              currentNode.parentNode.replaceChild(span, currentNode)

              // 重新设置光标位置
              const newRange = document.createRange()
              newRange.setStart(newTextNode, newTextNode.textContent.length)
              newRange.collapse(true)
              selection.removeAllRanges()
              selection.addRange(newRange)
            } catch (e) {
            }
          }
        }
      }
    },

    needsStyleApplication(element) {
      const computedStyle = window.getComputedStyle(element)

      if (this.currentStyles.fontFamily && computedStyle.fontFamily !== this.currentStyles.fontFamily) {
        return true
      }
      if (this.currentStyles.fontSize && computedStyle.fontSize !== this.currentStyles.fontSize) {
        return true
      }
      if (this.currentStyles.bold && (computedStyle.fontWeight !== 'bold' && parseInt(computedStyle.fontWeight) < 700)) {
        return true
      }
      if (this.currentStyles.italic && computedStyle.fontStyle !== 'italic') {
        return true
      }
      if (this.currentStyles.strike && !computedStyle.textDecoration.includes('line-through')) {
        return true
      }

      return false
    },

    applyCurrentStylesToElement(element) {
      if (this.currentStyles.fontFamily) {
        element.style.fontFamily = this.currentStyles.fontFamily
      }
      if (this.currentStyles.fontSize) {
        element.style.fontSize = this.currentStyles.fontSize
      }
      if (this.currentStyles.bold) {
        element.style.fontWeight = 'bold'
      }
      if (this.currentStyles.italic) {
        element.style.fontStyle = 'italic'
      }
      if (this.currentStyles.strike) {
        element.style.textDecoration = 'line-through'
      }
      if (this.currentStyles.color) {
        element.style.color = this.currentStyles.color
      }
    },

    handleFocus() {
      this.saveSelection()
    },

    handleBlur() {
      // 编辑器失去焦点时，强制保存当前选区
      this.saveSelection()
    },

    handleKeyDown(e) {
      // 处理列表中的回车键
      if (e.key === 'Enter') {
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          const listItem = this.findParentElement(range.commonAncestorContainer, 'LI')

          if (listItem) {
            const text = listItem.textContent.trim()
            // 只有当列表项为空且光标在开始位置时才退出列表
            if (!text && range.startOffset === 0) {
              e.preventDefault()

              // 创建新的列表项
              const newLi = document.createElement('li')
              newLi.innerHTML = '<br>'

              // 在当前列表项后插入新的列表项
              listItem.parentNode.insertBefore(newLi, listItem.nextSibling)

              // 设置光标到新列表项
              const newRange = document.createRange()
              newRange.setStart(newLi, 0)
              newRange.collapse(true)
              selection.removeAllRanges()
              selection.addRange(newRange)

              this.updateContent()
              return
            }
          }
        }
      }
    },

    findParentElement(node, tagName) {
      let current = node
      while (current && current !== this.$refs.editorContent) {
        if (current.nodeType === Node.ELEMENT_NODE && current.tagName === tagName) {
          return current
        }
        current = current.parentElement
      }
      return null
    },

    // 格式化方法
    toggleBold() {
      this.currentStyles.bold = !this.currentStyles.bold
      this.isBold = this.currentStyles.bold

      if (this.savedRange && !this.savedRange.collapsed) {
        // 有选中文本，应用粗体
        this.applyStyleToSelection('fontWeight', this.currentStyles.bold ? 'bold' : 'normal')
        // 操作完成后恢复选区
        this.$nextTick(() => {
          this.restoreSelection()
        })
      } else {
        // 没有选中文本，设置后续输入的样式
        this.setNextInputStyle()
      }
    },

    toggleItalic() {
      this.currentStyles.italic = !this.currentStyles.italic
      this.isItalic = this.currentStyles.italic

      if (this.savedRange && !this.savedRange.collapsed) {
        // 有选中文本，应用斜体
        this.applyStyleToSelection('fontStyle', this.currentStyles.italic ? 'italic' : 'normal')
        // 操作完成后恢复选区
        this.$nextTick(() => {
          this.restoreSelection()
        })
      } else {
        // 没有选中文本，设置后续输入的样式
        this.setNextInputStyle()
      }
    },

    toggleStrike() {
      this.currentStyles.strike = !this.currentStyles.strike
      this.isStrike = this.currentStyles.strike

      if (this.savedRange && !this.savedRange.collapsed) {
        // 有选中文本，应用删除线
        this.applyStyleToSelection('textDecoration', this.currentStyles.strike ? 'line-through' : 'none')
        // 操作完成后恢复选区
        this.$nextTick(() => {
          this.restoreSelection()
        })
      } else {
        // 没有选中文本，设置后续输入的样式
        this.setNextInputStyle()
      }
    },

    toggleCode() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const selectedText = range.toString()
        if (selectedText) {
          const code = document.createElement('code')
          code.textContent = selectedText
          code.style.backgroundColor = '#f5f5f5'
          code.style.padding = '2px 4px'
          code.style.borderRadius = '3px'
          code.style.fontFamily = 'monospace'
          range.deleteContents()
          range.insertNode(code)
          this.updateContent()
        }
      }
    },

    toggleHeading(level) {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement
          : range.commonAncestorContainer

        let targetElement = element
        while (targetElement && targetElement !== this.$refs.editorContent) {
          if (['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV'].includes(targetElement.tagName)) {
            break
          }
          targetElement = targetElement.parentElement
        }

        if (targetElement && targetElement !== this.$refs.editorContent) {
          const newElement = document.createElement(`h${level}`)
          newElement.innerHTML = targetElement.innerHTML
          targetElement.parentNode.replaceChild(newElement, targetElement)
          this.updateContent()
        }
      }
    },

    toggleBulletList() {
      this.restoreSelection()
      this.execCommand('insertUnorderedList')
      // 操作完成后恢复选区
      this.$nextTick(() => {
        this.restoreSelection()
      })
    },

    toggleOrderedList() {
      this.restoreSelection()
      this.execCommand('insertOrderedList')
      // 操作完成后恢复选区
      this.$nextTick(() => {
        this.restoreSelection()
      })
    },

    toggleBlockquote() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement
          : range.commonAncestorContainer

        const blockquote = this.findParentElement(element, 'BLOCKQUOTE')
        if (blockquote) {
          const p = document.createElement('p')
          p.innerHTML = blockquote.innerHTML
          blockquote.parentNode.replaceChild(p, blockquote)
        } else {
          let targetElement = element
          while (targetElement && targetElement !== this.$refs.editorContent) {
            if (['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV'].includes(targetElement.tagName)) {
              break
            }
            targetElement = targetElement.parentElement
          }

          if (targetElement && targetElement !== this.$refs.editorContent) {
            const blockquote = document.createElement('blockquote')
            blockquote.innerHTML = targetElement.innerHTML
            blockquote.style.borderLeft = '4px solid #ddd'
            blockquote.style.paddingLeft = '16px'
            blockquote.style.margin = '16px 0'
            blockquote.style.color = '#666'
            targetElement.parentNode.replaceChild(blockquote, targetElement)
          }
        }
        this.updateContent()
      }
    },

    addLink() {
      const url = window.prompt('请输入链接地址')
      if (url) {
        this.restoreSelection()
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          const selectedText = range.toString()

          if (selectedText) {
            const link = document.createElement('a')
            link.href = url
            link.textContent = selectedText
            link.style.color = '#409EFF'
            range.deleteContents()
            range.insertNode(link)
          } else {
            const link = document.createElement('a')
            link.href = url
            link.textContent = url
            link.style.color = '#409EFF'
            range.insertNode(link)
          }
          this.updateContent()
        }
      }
    },

    toggleTaskList() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const checkbox = document.createElement('input')
        checkbox.type = 'checkbox'
        checkbox.style.marginRight = '8px'
        range.insertNode(checkbox)
        this.updateContent()
      }
    },

    setHorizontalRule() {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const hr = document.createElement('hr')
        hr.style.border = 'none'
        hr.style.borderTop = '1px solid #ddd'
        hr.style.margin = '16px 0'
        range.insertNode(hr)
        this.updateContent()
      }
    },

    undo() {
      this.execCommand('undo')
    },

    redo() {
      this.execCommand('redo')
    },

    setAlign(alignment) {
      this.restoreSelection()
      switch (alignment) {
        case 'left':
          this.execCommand('justifyLeft')
          break
        case 'center':
          this.execCommand('justifyCenter')
          break
        case 'right':
          this.execCommand('justifyRight')
          break
        case 'auto':
          this.execCommand('justifyFull')
          break
      }
      // 操作完成后恢复选区
      this.$nextTick(() => {
        this.restoreSelection()
      })
    },

    setFloating(direction) {
      this.restoreSelection()
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const selectedElement = range.commonAncestorContainer

        if (selectedElement.nodeType === Node.ELEMENT_NODE) {
          selectedElement.style.float = direction
        } else if (selectedElement.parentElement) {
          selectedElement.parentElement.style.float = direction
        }
      }
      this.updateContent()
      // 操作完成后恢复选区
      this.$nextTick(() => {
        this.restoreSelection()
      })
    },

    selectFont() {
      if (!this.fontFamilyValue) return


      // 更新当前样式状态
      this.currentStyles.fontFamily = this.fontFamilyValue

      if (this.savedRange && !this.savedRange.collapsed) {
        // 有选中文本，直接应用字体（不要调用restoreSelection，会清空选区）
        setTimeout(() => {
          this.applyStyleToSelection('fontFamily', this.fontFamilyValue)
        }, 50)
      } else {
        // 没有选中文本，设置后续输入的样式
        this.setNextInputStyle()
      }
    },

    applyStyleToSelection(styleProperty, styleValue) {

      let selection = window.getSelection()
      let range = null

      // 首先尝试使用当前选区
      if (selection.rangeCount > 0) {
        range = selection.getRangeAt(0)
      }

      // 如果当前选区为空或者collapsed，使用保存的选区
      if ((!range || range.collapsed) && this.savedRange && !this.savedRange.collapsed) {
        selection.removeAllRanges()
        try {
          selection.addRange(this.savedRange.cloneRange())
          range = this.savedRange.cloneRange()
        } catch (e) {
          range = this.savedRange
        }
      }

      if (range && !range.collapsed) {
        try {
          const selectedText = range.toString()

          // 确保range是有效的
          if (!selectedText) {
            return
          }

          const span = document.createElement('span')

          // 应用新样式
          if (styleProperty === 'fontFamily') {
            // 确保字体名称正确设置
            span.style.fontFamily = `"${styleValue}", serif`
          } else {
            span.style[styleProperty] = styleValue
          }

          // 应用其他当前样式（但不覆盖新样式）
          if (this.currentStyles.fontFamily && styleProperty !== 'fontFamily') {
            span.style.fontFamily = `"${this.currentStyles.fontFamily}", serif`
          }
          if (this.currentStyles.fontSize && styleProperty !== 'fontSize') {
            span.style.fontSize = this.currentStyles.fontSize
          }
          if (this.currentStyles.bold && styleProperty !== 'fontWeight') {
            span.style.fontWeight = 'bold'
          }
          if (this.currentStyles.italic && styleProperty !== 'fontStyle') {
            span.style.fontStyle = 'italic'
          }
          if (this.currentStyles.strike && styleProperty !== 'textDecoration') {
            span.style.textDecoration = 'line-through'
          }
          if (this.currentStyles.color && styleProperty !== 'color') {
            span.style.color = this.currentStyles.color
          }

          span.textContent = selectedText

          // 使用range操作DOM
          range.deleteContents()
          range.insertNode(span)

          // 重新选中修改后的内容
          const newRange = document.createRange()
          newRange.selectNode(span)
          selection.removeAllRanges()
          selection.addRange(newRange)
          this.savedRange = newRange.cloneRange()

          this.updateContent()

        } catch (e) {
        }
      } else {
      }
    },

    setNextInputStyle() {
      // 为下次输入设置样式
      if (this.$refs.editorContent) {
        // 获取当前光标位置
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)

          // 在光标位置创建一个带样式的span
          const span = document.createElement('span')

          // 应用当前样式
          if (this.currentStyles.fontFamily) {
            span.style.fontFamily = `"${this.currentStyles.fontFamily}", serif`
          }
          if (this.currentStyles.fontSize) {
            span.style.fontSize = this.currentStyles.fontSize
          }
          if (this.currentStyles.bold) {
            span.style.fontWeight = 'bold'
          }
          if (this.currentStyles.italic) {
            span.style.fontStyle = 'italic'
          }
          if (this.currentStyles.strike) {
            span.style.textDecoration = 'line-through'
          }
          if (this.currentStyles.color) {
            span.style.color = this.currentStyles.color
          }

          // 插入一个零宽度空格作为占位符
          span.innerHTML = '&#8203;'
          range.insertNode(span)

          // 将光标移到span内部
          const newRange = document.createRange()
          newRange.setStart(span.firstChild, 1)
          newRange.collapse(true)
          selection.removeAllRanges()
          selection.addRange(newRange)
        }

        this.$refs.editorContent.focus()
      }
    },

    onSelectFocus() {
      // 下拉框获得焦点时，保存当前选区
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        if (!range.collapsed) {
          this.savedRange = range.cloneRange()
        }
      }
      this.isSelectingFont = true
    },

    onSelectBlur() {
      // 下拉框失去焦点时，延迟清除标记并恢复选区
      setTimeout(() => {
        this.isSelectingFont = false
        // 如果有保存的选区，恢复它
        if (this.savedRange && !this.savedRange.collapsed) {
          this.restoreSelection()
        }
      }, 200)
    },

    preventBlur(e) {
      // 阻止按钮点击时编辑器失去焦点
      if (e && e.preventDefault) {
        e.preventDefault()
      }
      if (e && e.stopPropagation) {
        e.stopPropagation()
      }
      return false
    },

    selectFontSize() {
      if (!this.fontSizeValue) return


      // 更新当前样式状态
      this.currentStyles.fontSize = this.fontSizeValue

      if (this.savedRange && !this.savedRange.collapsed) {
        // 有选中文本，直接应用字体大小（不要调用restoreSelection，会清空选区）
        setTimeout(() => {
          this.applyStyleToSelection('fontSize', this.fontSizeValue)
        }, 50)
      } else {
        // 没有选中文本，设置后续输入的样式
        this.setNextInputStyle()
      }
    },

    // 设置字体颜色
    setFontColor(color) {
      // 更新当前样式状态
      this.currentStyles.color = color

      // 获取当前选区
      const selection = window.getSelection()

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)

        if (!range.collapsed && range.toString().trim()) {
          // 有选中文本，直接应用颜色
          try {
            const selectedText = range.toString()
            const span = document.createElement('span')
            span.style.color = color
            span.textContent = selectedText

            // 删除原内容并插入新的span
            range.deleteContents()
            range.insertNode(span)

            // 重新选中修改后的内容
            const newRange = document.createRange()
            newRange.selectNode(span)
            selection.removeAllRanges()
            selection.addRange(newRange)

            this.updateContent()
          } catch (error) {
            // 颜色应用失败，静默处理
          }
        } else {
          // 没有选中文本，设置后续输入的样式
          this.setNextInputStyle()
        }
      } else {
        // 没有选区，设置后续输入的样式
        this.setNextInputStyle()
      }

      // 关闭颜色选择器
      if (this.$refs.colorPopover) {
        this.$refs.colorPopover.doClose()
      }

      // 确保编辑器保持焦点
      this.$refs.editorContent.focus()
    },

    onSelectEmoji(emoji) {
      this.restoreSelection()
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const textNode = document.createTextNode(emoji)
        range.insertNode(textNode)

        // 移动光标到表情后面
        range.setStartAfter(textNode)
        range.collapse(true)
        selection.removeAllRanges()
        selection.addRange(range)

        this.updateContent()
        this.saveSelection()
      }
      this.$refs.editorContent.focus()
    },

    async uploadImage() {
      // 保存当前选区
      this.saveSelection()

      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.jpg,.jpeg,.gif,.png,.bmp'

      input.onchange = async (e) => {
        const file = e.target.files?.[0]
        if (!file) return

        if (file.size > 100 * 1024 * 1024) {
          this.$message.warning('文件不能超过100MB')
          return
        }

        try {
          this.loadingEditor = true
          let url

          // 判断文件大小，决定使用分片上传还是普通上传
          if (file.size > 10 * 1024 * 1024) {
            // 大于10MB，使用分片上传

            // 显示上传进度对话框
            this.uploadProgress = {
              visible: true,
              fileName: file.name,
              fileSize: file.size,
              stage: 'calculating',
              percentage: 0,
              message: '正在计算文件标识...'
            }

            const uploader = new ChunkUploader({
              bizType: 'forum',
              onProgress: (progress) => {
                this.uploadProgress.stage = progress.stage
                this.uploadProgress.percentage = progress.percent || 0
                this.uploadProgress.message = progress.message || ''
              },
              onError: (error) => {
                this.uploadProgress.stage = 'error'
                this.uploadProgress.message = error.message
                setTimeout(() => {
                  this.uploadProgress.visible = false
                }, 3000)
              },
              onSuccess: () => {
                this.uploadProgress.stage = 'completed'
                this.uploadProgress.percentage = 100
                this.uploadProgress.message = '上传完成'
                setTimeout(() => {
                  this.uploadProgress.visible = false
                }, 2000)
              }
            })

            this.currentUploader = uploader
            url = await uploader.upload(file)
          } else {
            // 小于等于10MB，使用普通上传

            // 显示简单的加载提示
            this.$message({
              message: '正在上传图片...',
              type: 'info',
              duration: 0,
              showClose: false,
              customClass: 'upload-loading-message'
            })

            url = await simpleUpload(file, this.uploadUrl)

            // 关闭加载提示
            this.$message.closeAll()
          }

          if (url) {

            // 确保编辑器获得焦点
            this.$refs.editorContent.focus()

            // 尝试恢复选区，如果失败则在编辑器末尾插入
            let range
            const selection = window.getSelection()

            if (this.savedRange) {
              try {
                range = this.savedRange.cloneRange()
                selection.removeAllRanges()
                selection.addRange(range)
              } catch (e) {
                range = null
              }
            }

            // 如果没有有效的选区，在编辑器末尾创建一个
            if (!range || !this.$refs.editorContent.contains(range.commonAncestorContainer)) {
              range = document.createRange()
              range.selectNodeContents(this.$refs.editorContent)
              range.collapse(false) // 移动到末尾
              selection.removeAllRanges()
              selection.addRange(range)
            }

            const img = document.createElement('img')
            img.src = url

            // 设置图片基础样式
            img.style.maxWidth = '100%'
            img.style.height = 'auto'
            img.style.display = 'inline-block'
            img.style.margin = '5px'
            img.style.cursor = 'pointer'
            img.style.border = '1px solid #ddd'
            img.style.borderRadius = '4px'
            img.style.verticalAlign = 'middle'
            img.style.position = 'relative'

            try {
              range.insertNode(img)

              // 在图片后添加一个空格，方便继续编辑
              const textNode = document.createTextNode(' ')
              range.setStartAfter(img)
              range.insertNode(textNode)
              range.setStartAfter(textNode)
              range.collapse(true)
              selection.removeAllRanges()
              selection.addRange(range)

              // 图片插入DOM后再添加调整大小功能
              this.$nextTick(() => {
                this.makeImageResizable(img)
              })

              this.updateContent()
              this.$message.success('图片上传成功')
            } catch (error) {
              // 如果插入失败，直接添加到编辑器末尾
              this.$refs.editorContent.appendChild(img)
              this.$refs.editorContent.appendChild(document.createTextNode(' '))

              this.$nextTick(() => {
                this.makeImageResizable(img)
              })

              this.updateContent()
              this.$message.success('图片上传成功')
            }
          }
        } catch (error) {
          this.$message.error('图片上传失败: ' + (error.message || '未知错误'))
        } finally {
          this.loadingEditor = false
        }
      }
      input.click()
    },

    async uploadFile() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.zip,.doc,.docx,.xls,.xlsx,.pdf,.txt'

      input.onchange = async (e) => {
        const file = e.target.files?.[0]
        if (!file) return

        if (file.size > 100 * 1024 * 1024) {
          this.$message.warning('文件不能超过100MB')
          return
        }

        try {
          this.loadingEditor = true
          let url

          // 判断文件大小，决定使用分片上传还是普通上传
          if (file.size > 10 * 1024 * 1024) {
            // 大于10MB，使用分片上传

            // 显示上传进度对话框
            this.uploadProgress = {
              visible: true,
              fileName: file.name,
              fileSize: file.size,
              stage: 'calculating',
              percentage: 0,
              message: '正在计算文件标识...'
            }

            const uploader = new ChunkUploader({
              bizType: 'forum',
              onProgress: (progress) => {
                this.uploadProgress.stage = progress.stage
                this.uploadProgress.percentage = progress.percent || 0
                this.uploadProgress.message = progress.message || ''
              },
              onError: (error) => {
                this.uploadProgress.stage = 'error'
                this.uploadProgress.message = error.message
                setTimeout(() => {
                  this.uploadProgress.visible = false
                }, 3000)
              },
              onSuccess: () => {
                this.uploadProgress.stage = 'completed'
                this.uploadProgress.percentage = 100
                this.uploadProgress.message = '上传完成'
                setTimeout(() => {
                  this.uploadProgress.visible = false
                }, 2000)
              }
            })

            this.currentUploader = uploader
            url = await uploader.upload(file)
          } else {
            // 小于等于10MB，使用普通上传

            // 显示简单的加载提示
            this.$message({
              message: '正在上传文件...',
              type: 'info',
              duration: 0,
              showClose: false,
              customClass: 'upload-loading-message'
            })

            url = await simpleUpload(file, this.uploadUrl)

            // 关闭加载提示
            this.$message.closeAll()
          }

          if (url) {
            // 添加到附件列表
            const attachment = {
              fileName: file.name,
              fileUrl: url,
              fileSize: file.size,
              uploadTime: new Date().toLocaleString()
            }
            this.attachments.push(attachment)
            this.$emit('attachment-change', this.attachments)
            this.$message.success('文件上传成功')
          }
        } catch (error) {
          this.$message.error('文件上传失败: ' + (error.message || '未知错误'))
        } finally {
          this.loadingEditor = false
        }
      }
      input.click()
    },


    async checkFontLoading() {

      const fonts = ['Caliste', 'Billstone', 'Howdy', 'Miskan', 'FandolSong-Bold', 'SourceHanSansCN', 'kaiti', 'fangsong']

      for (const fontName of fonts) {
        try {
          const fontFace = new FontFace(fontName, `url(/fonts/${fontName}.ttf)`)
          await fontFace.load()
          document.fonts.add(fontFace)
        } catch (error) {
          try {
            // 尝试.otf格式
            const fontFace = new FontFace(fontName, `url(/fonts/${fontName}.otf)`)
            await fontFace.load()
            document.fonts.add(fontFace)
          } catch (error2) {
          }
        }
      }

      // 检查中文字体
      try {
        const kaitiFontFace = new FontFace('kaiti', 'url(/fonts/王汉宗粗楷体简.ttf)')
        await kaitiFontFace.load()
        document.fonts.add(kaitiFontFace)
      } catch (error) {
      }

      try {
        const fangsongFontFace = new FontFace('fangsong', 'url(/fonts/王汉宗中仿宋简.ttf)')
        await fangsongFontFace.load()
        document.fonts.add(fangsongFontFace)
      } catch (error) {
      }
    },

    cancelUpload() {
      if (this.currentUploader) {
        // 这里可以添加取消上传的逻辑
        console.log('取消上传')
        this.currentUploader = null
      }
      this.uploadProgress.visible = false
      this.loadingEditor = false
    },

    showImagePreview(url) {
      console.log('[图片预览] 显示预览:', url)
      this.imagePreview.url = url
      this.imagePreview.visible = true
    },

    closeImagePreview() {
      this.imagePreview.visible = false
      this.imagePreview.url = ''
    },

    removeAttachment(index) {
      this.attachments.splice(index, 1)
      this.$emit('attachment-change', this.attachments)
    },

    getAttachments() {
      return this.attachments
    },

    setAttachments(attachments) {
      this.attachments = attachments || []
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 使图片可调整大小和移动
    makeImageResizable(img) {
      console.log('[图片功能] 开始为图片添加功能', img.src)

      // 检查图片是否已经在DOM中
      if (!img.parentNode) {
        console.warn('[图片功能] 图片还未插入DOM')
        return
      }

      // 检查是否已经被包装过
      if (img.parentNode.classList && img.parentNode.classList.contains('image-resize-container')) {
        console.log('[图片功能] 图片已有功能，重新绑定事件')
        // 重新绑定事件而不是跳过
        this.rebindImageEvents(img.parentNode)
        return
      }

      // 创建图片容器
      const imgContainer = document.createElement('div')
      imgContainer.className = 'image-resize-container'
      imgContainer.style.cssText = `
        position: relative;
        display: inline-block;
        margin: 5px;
        vertical-align: middle;
        line-height: 0;
      `

      // 设置图片样式
      img.style.cssText = `
        max-width: 100%;
        height: auto;
        display: block;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: all 0.2s ease;
      `

      // 创建四个角的调整控制点
      const corners = [
        { name: 'nw', cursor: 'nw-resize', position: 'top: -5px; left: -5px;' },
        { name: 'ne', cursor: 'ne-resize', position: 'top: -5px; right: -5px;' },
        { name: 'sw', cursor: 'sw-resize', position: 'bottom: -5px; left: -5px;' },
        { name: 'se', cursor: 'se-resize', position: 'bottom: -5px; right: -5px;' }
      ]

      const resizeHandles = []

      corners.forEach(corner => {
        const handle = document.createElement('div')
        handle.className = `image-resize-handle resize-${corner.name}`
        handle.style.cssText = `
          position: absolute;
          ${corner.position}
          width: 10px;
          height: 10px;
          background: #409EFF;
          border: 2px solid #fff;
          border-radius: 50%;
          cursor: ${corner.cursor};
          opacity: 0;
          transition: all 0.2s ease;
          z-index: 1000;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.4);
        `
        handle.dataset.direction = corner.name
        resizeHandles.push(handle)
      })

      // 将图片包装在容器中
      try {
        img.parentNode.insertBefore(imgContainer, img)
        imgContainer.appendChild(img)
        resizeHandles.forEach(handle => imgContainer.appendChild(handle))
        console.log('[图片功能] 容器创建成功')
      } catch (error) {
        console.error('[图片功能] 创建失败:', error)
        return
      }

      // 设置拖拽移动功能
      this.setupImageDrag(imgContainer, img)

      // 设置调整大小功能
      this.setupImageResize(imgContainer, img, resizeHandles)

      // 图片双击预览 - 使用容器而不是图片本身，确保小图片也能双击
      let clickCount = 0
      let clickTimer = null

      const handleImageClick = (e) => {
        e.preventDefault()
        e.stopPropagation()

        clickCount++

        if (clickCount === 1) {
          clickTimer = setTimeout(() => {
            // 单击 - 不做任何操作
            clickCount = 0
          }, 300)
        } else if (clickCount === 2) {
          // 双击
          clearTimeout(clickTimer)
          clickCount = 0
          console.log('[图片功能] 双击预览图片')
          this.showImagePreview(img.src)
        }
      }

      // 在容器上绑定点击事件，确保小图片也能响应
      imgContainer.addEventListener('click', handleImageClick)
      img.addEventListener('click', handleImageClick)
    },

    // 设置图片拖拽移动功能
    setupImageDrag(imgContainer, img) {
      let isDragging = false
      let dragStartX, dragStartY
      let imgStartX, imgStartY
      let originalParent = null
      let originalNextSibling = null
      let dragStartTime = 0
      let hasMoved = false

      img.addEventListener('mousedown', (e) => {
        // 只有在图片中心区域才能拖拽移动
        const rect = img.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        // 根据图片大小调整拖拽区域
        const minThreshold = 20
        const maxThreshold = 50
        const threshold = Math.max(minThreshold, Math.min(maxThreshold, Math.min(rect.width, rect.height) * 0.3))

        if (Math.abs(e.clientX - centerX) < threshold && Math.abs(e.clientY - centerY) < threshold) {
          // 确保容器和图片都在DOM中
          if (!document.contains(imgContainer) || !document.contains(img)) {
            console.error('[拖拽移动] 容器或图片不在DOM中，取消拖拽')
            return
          }

          // 记录拖拽开始时间和位置
          dragStartTime = Date.now()
          dragStartX = e.clientX
          dragStartY = e.clientY
          hasMoved = false

          // 保存原始位置信息
          originalParent = imgContainer.parentNode
          originalNextSibling = imgContainer.nextSibling

          // 确保原始父节点存在
          if (!originalParent) {
            console.error('[拖拽移动] 原始父节点不存在，取消拖拽')
            return
          }

          const containerRect = imgContainer.getBoundingClientRect()
          const editorRect = this.$refs.editorContent.getBoundingClientRect()
          imgStartX = containerRect.left - editorRect.left
          imgStartY = containerRect.top - editorRect.top

          e.preventDefault()
          e.stopPropagation()

          document.addEventListener('mousemove', handleDrag)
          document.addEventListener('mouseup', stopDrag)
        }
      })

      const handleDrag = (e) => {
        const deltaX = e.clientX - dragStartX
        const deltaY = e.clientY - dragStartY
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        // 只有移动距离超过5px才开始真正的拖拽
        if (distance > 5 && !isDragging) {
          console.log('[拖拽移动] 开始拖拽，图片:', img.src)
          isDragging = true
          hasMoved = true

          // 设置拖拽状态
          imgContainer.style.position = 'absolute'
          imgContainer.style.left = imgStartX + 'px'
          imgContainer.style.top = imgStartY + 'px'
          imgContainer.style.zIndex = '1000'
          imgContainer.style.pointerEvents = 'none'
          img.style.cursor = 'move'
        }

        if (isDragging) {
          let newX = imgStartX + deltaX
          let newY = imgStartY + deltaY

          // 限制在编辑器范围内
          const editorRect = this.$refs.editorContent.getBoundingClientRect()
          const containerWidth = imgContainer.offsetWidth
          const containerHeight = imgContainer.offsetHeight

          const maxX = editorRect.width - containerWidth
          const maxY = editorRect.height - containerHeight

          newX = Math.max(0, Math.min(newX, maxX))
          newY = Math.max(0, Math.min(newY, maxY))

          imgContainer.style.left = newX + 'px'
          imgContainer.style.top = newY + 'px'
        }
      }

      const stopDrag = () => {
        document.removeEventListener('mousemove', handleDrag)
        document.removeEventListener('mouseup', stopDrag)

        // 如果没有真正拖拽（移动距离很小或时间很短），则不进行移动操作
        const dragDuration = Date.now() - dragStartTime
        if (!hasMoved || dragDuration < 100) {
          console.log('[拖拽移动] 未达到拖拽条件，取消移动')
          if (isDragging) {
            // 恢复样式
            img.style.cursor = 'pointer'
            imgContainer.style.position = 'relative'
            imgContainer.style.left = ''
            imgContainer.style.top = ''
            imgContainer.style.zIndex = ''
            imgContainer.style.pointerEvents = ''
          }
          isDragging = false
          return
        }

        if (isDragging) {
          console.log('[拖拽移动] 结束拖拽')
          isDragging = false

          // 计算拖拽结束位置，找到最合适的插入点
          const finalX = parseInt(imgContainer.style.left)
          const finalY = parseInt(imgContainer.style.top)

          console.log('[拖拽移动] 最终位置:', { x: finalX, y: finalY })

          // 恢复样式
          img.style.cursor = 'pointer'
          imgContainer.style.position = 'relative'
          imgContainer.style.left = ''
          imgContainer.style.top = ''
          imgContainer.style.zIndex = ''
          imgContainer.style.pointerEvents = ''

          // 找到最合适的插入位置
          const insertionPoint = this.findBestInsertionPoint(finalX, finalY)

          // 安全地移动图片容器
          const moveSuccess = this.safelyMoveContainer(imgContainer, insertionPoint)

          // 延迟更新内容，确保DOM稳定，并重新绑定事件
          this.$nextTick(() => {
            this.updateContent()
            // 只有移动成功才重新绑定事件
            if (moveSuccess && document.contains(imgContainer)) {
              this.rebindImageEvents(imgContainer)
            }
          })
        }
      }
    },

    // 安全地移动容器到目标位置
    safelyMoveContainer(imgContainer, insertionPoint) {
      try {
        // 首先确保容器还在DOM中
        if (!imgContainer || !document.contains(imgContainer)) {
          console.error('[拖拽移动] 容器已不在DOM中')
          return false
        }

        // 确保编辑器还存在
        if (!this.$refs.editorContent) {
          console.error('[拖拽移动] 编辑器不存在')
          return false
        }

        // 先从当前位置移除容器
        if (imgContainer.parentNode) {
          imgContainer.parentNode.removeChild(imgContainer)
          console.log('[拖拽移动] 已从原位置移除容器')
        }

        // 如果没有找到插入点，直接插入到编辑器末尾
        if (!insertionPoint) {
          console.log('[拖拽移动] 未找到插入点，插入到编辑器末尾')
          this.$refs.editorContent.appendChild(imgContainer)
          return true
        }

        // 验证插入点的有效性
        const { parent, nextSibling } = insertionPoint

        if (!parent || !document.contains(parent)) {
          console.log('[拖拽移动] 目标父节点无效，插入到编辑器末尾')
          this.$refs.editorContent.appendChild(imgContainer)
          return true
        }

        // 如果有nextSibling，验证它是否还是parent的子节点
        if (nextSibling) {
          if (nextSibling.parentNode !== parent) {
            console.log('[拖拽移动] nextSibling已不是parent的子节点，插入到末尾')
            parent.appendChild(imgContainer)
          } else {
            console.log('[拖拽移动] 插入到指定位置之前')
            parent.insertBefore(imgContainer, nextSibling)
          }
        } else {
          console.log('[拖拽移动] 插入到父容器末尾')
          parent.appendChild(imgContainer)
        }

        return true
      } catch (error) {
        console.error('[拖拽移动] 移动容器失败:', error)

        // 最后的安全措施：确保图片不会丢失
        try {
          if (imgContainer && this.$refs.editorContent) {
            // 如果容器还存在但不在DOM中，重新添加到编辑器
            if (!document.contains(imgContainer)) {
              this.$refs.editorContent.appendChild(imgContainer)
              console.log('[拖拽移动] 紧急恢复：图片已重新添加到编辑器')
            }
          }
        } catch (recoveryError) {
          console.error('[拖拽移动] 紧急恢复也失败:', recoveryError)
        }

        return false
      }
    },

    // 找到最佳插入位置
    findBestInsertionPoint(x, y) {
      try {
        const editorContent = this.$refs.editorContent
        if (!editorContent) {
          console.error('[拖拽定位] 编辑器不存在')
          return null
        }

        const editorRect = editorContent.getBoundingClientRect()

        // 转换为相对于编辑器的坐标
        const relativeX = x
        const relativeY = y

        console.log('[拖拽定位] 目标位置:', { x: relativeX, y: relativeY })

        // 如果编辑器为空，直接返回编辑器作为父容器
        if (editorContent.children.length === 0) {
          console.log('[拖拽定位] 编辑器为空，插入到编辑器')
          return { parent: editorContent, nextSibling: null }
        }

        // 如果拖拽到很靠上的位置，插入到开头
        if (relativeY < 20) {
          console.log('[拖拽定位] 插入到编辑器开头')
          const firstChild = editorContent.firstChild
          return { parent: editorContent, nextSibling: firstChild }
        }

        // 简化插入逻辑：只使用元素级别的插入，避免复杂的文本节点操作
        const children = Array.from(editorContent.children)

        // 遍历所有子元素，找到合适的插入位置
        for (let i = 0; i < children.length; i++) {
          const child = children[i]

          // 跳过图片容器本身（避免插入到自己前面）
          if (child.classList && child.classList.contains('image-resize-container')) {
            continue
          }

          const rect = child.getBoundingClientRect()
          const childY = rect.top - editorRect.top
          const childHeight = rect.height

          // 如果拖拽位置在当前元素上方
          if (relativeY < childY + childHeight / 2) {
            console.log('[拖拽定位] 插入到元素', i, '之前')
            return { parent: editorContent, nextSibling: child }
          }
        }

        // 如果拖拽到最下方，插入到最后
        console.log('[拖拽定位] 插入到编辑器末尾')
        return { parent: editorContent, nextSibling: null }

      } catch (error) {
        console.error('[拖拽定位] 查找插入位置失败:', error)
        // 返回安全的默认位置
        return { parent: this.$refs.editorContent, nextSibling: null }
      }
    },

    // 获取指定坐标的光标位置
    getCaretPositionFromPoint(x, y) {
      if (document.caretPositionFromPoint) {
        const position = document.caretPositionFromPoint(x, y)
        if (position) {
          const range = document.createRange()
          range.setStart(position.offsetNode, position.offset)
          range.collapse(true)
          return range
        }
      } else if (document.caretRangeFromPoint) {
        return document.caretRangeFromPoint(x, y)
      }
      return null
    },

    // 在指定range位置插入
    insertAtRange(range) {
      const container = range.startContainer
      const offset = range.startOffset

      if (container.nodeType === Node.TEXT_NODE) {
        // 在文本节点中插入
        const parent = container.parentNode
        if (offset === 0) {
          // 插入到文本开头
          return { parent: parent, nextSibling: container }
        } else if (offset >= container.textContent.length) {
          // 插入到文本结尾
          return { parent: parent, nextSibling: container.nextSibling }
        } else {
          // 分割文本节点
          const beforeText = container.textContent.substring(0, offset)
          const afterText = container.textContent.substring(offset)

          const beforeNode = document.createTextNode(beforeText)
          const afterNode = document.createTextNode(afterText)

          parent.insertBefore(beforeNode, container)
          parent.insertBefore(afterNode, container)
          parent.removeChild(container)

          return { parent: parent, nextSibling: afterNode }
        }
      } else {
        // 在元素节点中插入
        if (offset < container.childNodes.length) {
          return { parent: container, nextSibling: container.childNodes[offset] }
        } else {
          return { parent: container, nextSibling: null }
        }
      }
    },

    // 设置图片调整大小功能
    setupImageResize(imgContainer, img, resizeHandles) {
      let isResizing = false
      let resizeDirection = ''
      let startX, startY, startWidth, startHeight

      // 鼠标进入容器时显示控制点
      imgContainer.addEventListener('mouseenter', () => {
        console.log('[图片缩放] 鼠标进入，显示控制点')
        resizeHandles.forEach(handle => {
          handle.style.opacity = '1'
          handle.style.display = 'block'
        })
        img.style.borderColor = '#409EFF'
        img.style.boxShadow = '0 2px 8px rgba(64, 158, 255, 0.2)'
      })

      // 鼠标离开容器时隐藏控制点
      imgContainer.addEventListener('mouseleave', () => {
        if (!isResizing) {
          console.log('[图片缩放] 鼠标离开，隐藏控制点')
          resizeHandles.forEach(handle => {
            handle.style.opacity = '0'
          })
          img.style.borderColor = '#ddd'
          img.style.boxShadow = 'none'
        }
      })

      // 为每个控制点添加事件
      resizeHandles.forEach(handle => {
        handle.addEventListener('mousedown', (e) => {
          console.log('[调整大小] 开始调整:', handle.dataset.direction)
          e.preventDefault()
          e.stopPropagation()

          isResizing = true
          resizeDirection = handle.dataset.direction
          startX = e.clientX
          startY = e.clientY
          startWidth = parseInt(getComputedStyle(img).width, 10)
          startHeight = parseInt(getComputedStyle(img).height, 10)

          document.addEventListener('mousemove', doResize)
          document.addEventListener('mouseup', stopResize)

          // 调整时保持控制点可见
          resizeHandles.forEach(h => h.style.opacity = '1')
        })
      })

      const doResize = (e) => {
        const deltaX = e.clientX - startX
        const deltaY = e.clientY - startY

        let newWidth = startWidth
        let newHeight = startHeight

        // 根据拖拽方向计算新尺寸
        switch (resizeDirection) {
          case 'se': // 右下角
            newWidth = startWidth + deltaX
            break
          case 'sw': // 左下角
            newWidth = startWidth - deltaX
            break
          case 'ne': // 右上角
            newWidth = startWidth + deltaX
            break
          case 'nw': // 左上角
            newWidth = startWidth - deltaX
            break
        }

        // 限制最小和最大尺寸
        const minSize = 50
        const maxWidth = this.$refs.editorContent.clientWidth - 20

        if (newWidth >= minSize && newWidth <= maxWidth) {
          img.style.width = newWidth + 'px'
          img.style.height = 'auto'
        }
      }

      const stopResize = () => {
        console.log('[调整大小] 结束调整')
        isResizing = false
        resizeDirection = ''
        document.removeEventListener('mousemove', doResize)
        document.removeEventListener('mouseup', stopResize)

        // 调整完成后隐藏控制点
        resizeHandles.forEach(handle => {
          handle.style.opacity = '0'
        })
        img.style.borderColor = '#ddd'
        img.style.boxShadow = 'none'

        this.updateContent()
      }
    },



    // 重新绑定图片事件（完整版本）
    rebindImageEvents(imgContainer) {
      const img = imgContainer.querySelector('img')
      const handles = imgContainer.querySelectorAll('.image-resize-handle')

      if (!img || handles.length !== 4) {
        // 如果容器结构不完整，重新创建
        if (img) {
          console.log('[图片功能] 容器结构不完整，重新创建')
          // 先移除旧容器
          const parent = imgContainer.parentNode
          parent.insertBefore(img, imgContainer)
          parent.removeChild(imgContainer)
          // 重新创建
          this.makeImageResizable(img)
        }
        return
      }

      console.log('[图片功能] 重新绑定事件到图片:', img.src)

      // 清除旧的事件监听器（通过克隆节点）
      const newImg = img.cloneNode(true)
      const newContainer = imgContainer.cloneNode(false)

      // 重新添加图片和控制点
      newContainer.appendChild(newImg)
      handles.forEach(handle => {
        const newHandle = handle.cloneNode(true)
        newContainer.appendChild(newHandle)
      })

      // 替换旧容器
      imgContainer.parentNode.replaceChild(newContainer, imgContainer)

      // 重新设置功能
      this.setupImageDrag(newContainer, newImg)
      this.setupImageResize(newContainer, newImg, Array.from(newContainer.querySelectorAll('.image-resize-handle')))

      // 重新绑定双击预览 - 使用改进的点击处理
      let clickCount = 0
      let clickTimer = null

      const handleImageClick = (e) => {
        e.preventDefault()
        e.stopPropagation()

        clickCount++

        if (clickCount === 1) {
          clickTimer = setTimeout(() => {
            // 单击 - 不做任何操作
            clickCount = 0
          }, 300)
        } else if (clickCount === 2) {
          // 双击
          clearTimeout(clickTimer)
          clickCount = 0
          console.log('[图片功能] 双击预览图片')
          this.showImagePreview(newImg.src)
        }
      }

      // 在容器和图片上都绑定点击事件，确保小图片也能响应
      newContainer.addEventListener('click', handleImageClick)
      newImg.addEventListener('click', handleImageClick)
    },

    // 清理所有错误的控制点
    cleanupResizeHandles() {
      if (!this.$refs.editorContent) return

      // 移除所有孤立的控制点（不在图片容器中的）
      const allHandles = this.$refs.editorContent.querySelectorAll('.image-resize-handle')
      allHandles.forEach(handle => {
        const container = handle.parentNode
        if (!container || !container.classList.contains('image-resize-container')) {
          handle.remove()
        }
      })

      // 清理损坏的图片容器
      const containers = this.$refs.editorContent.querySelectorAll('.image-resize-container')
      containers.forEach(container => {
        const img = container.querySelector('img')
        const handles = container.querySelectorAll('.image-resize-handle')

        // 如果容器中没有图片或控制点数量不对，清理整个容器
        if (!img || handles.length !== 4) {
          if (img) {
            // 保留图片，移除容器
            container.parentNode.insertBefore(img, container)
          }
          container.remove()
        }
      })
    },

    // 为已有图片设置功能
    setupImageFeatures() {
      if (!this.$refs.editorContent) return

      // 先清理错误的控制点
      this.cleanupResizeHandles()

      const images = this.$refs.editorContent.querySelectorAll('img')
      console.log('[图片功能] 找到图片数量:', images.length)

      // 为了避免在处理过程中DOM变化导致的问题，先收集所有图片信息
      const imageInfos = Array.from(images).map((img, index) => ({
        img,
        index,
        src: img.src,
        isWrapped: img.parentNode && img.parentNode.classList && img.parentNode.classList.contains('image-resize-container')
      }))

      imageInfos.forEach(({ img, index, src, isWrapped }) => {
        // 再次检查图片是否还在DOM中（避免处理过程中被移除）
        if (!img.parentNode || !document.contains(img)) {
          console.log(`[图片功能] 第${index + 1}张图片已不在DOM中，跳过`)
          return
        }

        if (!isWrapped) {
          console.log(`[图片功能] 为第${index + 1}张图片添加功能:`, src)

          // 重新设置基础样式
          img.style.maxWidth = '100%'
          img.style.height = 'auto'
          img.style.display = 'block'
          img.style.margin = '5px'
          img.style.cursor = 'pointer'
          img.style.border = '1px solid #ddd'
          img.style.borderRadius = '4px'
          img.style.verticalAlign = 'middle'

          // 添加可调整大小功能（包含双击预览事件）
          this.makeImageResizable(img)
        } else {
          console.log(`[图片功能] 第${index + 1}张图片已有功能，重新绑定事件:`, src)
          // 重新绑定所有事件
          this.rebindImageEvents(img.parentNode)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 清除浮动 */
.rich-content>* {
  overflow: hidden;
}

/* 浮动元素样式 */
[style*="float: left"],
[style*="float: right"] {
  width: 50%;
  margin: 0 1em 1em 0;
}

[style*="float: right"] {
  margin: 0 0 1em 1em;
}

:deep(.el-select__wrapper) {
  height: 22px !important;
  font-size: 12px;
  line-height: 22px;
  min-height: 22px;
  padding: 0 5px !important;
  border-radius: 0 !important;
}

:deep(.el-select) {
  height: 22px !important;
}

.bars {
  line-height: 1.5;
}

.bars a {
  float: left;
  border: 1px solid #F2F2F2;
  background: transparent url('/static/editor.gif') no-repeat 0 0;
  overflow: hidden;
  text-indent: -999px;
  width: 22px;
  height: 22px;
  cursor: pointer;
  margin: 0 2px;
}

.bars a:hover {
  border-color: #09C;
  background-color: #FFF;
  text-decoration: none;
}

.bars a.is-active {
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.bar1 {
  background-position: 0 0 !important;
}

.bar2 {
  background-position: -20px 0 !important;
}

.bar3 {
  background-position: -40px 0 !important;
}

.bar4 {
  background-position: -60px 0 !important;
}

.bar5 {
  background-position: -40px -20px !important;
}

.bar6 {
  background-position: -220px -40px !important;
}

.bar7 {
  background-position: -80px -20px !important;
}

.bar8 {
  background-position: -240px -40px !important;
}

.bar9 {
  background-position: -260px -40px !important;
}

.bar10 {
  background-position: -100px -60px !important;
}

.bar11 {
  background-position: -120px -60px !important;
}

.bar12 {
  background-position: -100px -20px !important;
}

.bar13 {
  background-position: 0px -60px !important;
}

.bar14 {
  padding-top: 27px;
  width: 35px !important;
  height: 15px;
  background-position: -3px -80px !important;
}

.bar15 {
  padding-top: 27px;
  width: 35px !important;
  height: 15px;
  background-position: -123px -80px !important;
}

.bar16 {
  padding-top: 27px;
  width: 35px !important;
  height: 15px;
  background-position: -83px -80px !important;
}

.bar17 {
  background-position: -20px -40px !important;
}

.bar18 {
  background-position: -40px -40px !important;
}

.br2 {
  position: relative;
  float: left;
  border-left: 1px solid #FEFEFE;
  padding: 0 3px;
  border-right: 1px solid #DDD;
  height: 44px;
}

.nbr {
  border-right: none;
  padding-right: 0;
}

.nbl {
  border-left: none;
  padding-left: 0;
}

.msgs {
  clear: both;
  font-size: 11px;
  text-align: center;
  width: 37px;
  margin: 0 auto;
}

.create-post {
  .editor-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 4px;
    border-bottom: 1px solid #DDD;
    background: #F2F2F2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .editor-content {
    padding: 16px;
    min-height: 300px;
    background-color: #fff;

    .rich-content {
      min-height: 300px;
      outline: none;
      font-size: 14px; // 默认字体大小

      // 确保span元素的样式能正确应用
      span {
        display: inline !important;

        // 确保字体样式能正确应用 - 移除这些规则，它们会阻止样式应用
      }

      // 强制应用字体样式
      *[style*="font-family: Caliste"] {
        font-family: "Caliste", serif !important;
      }

      *[style*="font-family: Billstone"] {
        font-family: "Billstone", serif !important;
      }

      *[style*="font-family: Howdy"] {
        font-family: "Howdy", serif !important;
      }

      *[style*="font-family: Miskan"] {
        font-family: "Miskan", serif !important;
      }

      *[style*="font-family: FandolSong-Bold"] {
        font-family: "FandolSong-Bold", serif !important;
      }

      *[style*="font-family: SourceHanSansCN"] {
        font-family: "SourceHanSansCN", sans-serif !important;
      }

      *[style*="font-family: kaiti"] {
        font-family: "kaiti", serif !important;
      }

      *[style*="font-family: fangsong"] {
        font-family: "fangsong", serif !important;
      }

      img {
        max-width: 100%;
        height: auto;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: all 0.2s ease;
        vertical-align: middle;

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        &:focus {
          outline: 2px solid #409EFF;
          outline-offset: 2px;
        }
      }

      a {
        color: #409EFF;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      // 附件容器样式
      div[style*="background-color: #f5f7fa"] {
        transition: all 0.2s ease;

        &:hover {
          background-color: #ecf5ff !important;
          border-color: #b3d8ff !important;
        }
      }

      // 确保编辑器内容可以正常换行
      p, div {
        margin: 0.5em 0;
        line-height: 1.6;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      // 列表样式
      ul, ol {
        margin: 0.5em 0;
        padding-left: 2em;

        li {
          margin: 0.2em 0;
        }
      }
    }
  }

  // 附件列表样式
  .attachment-list {
    margin-top: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    .attachment-title {
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 500;
      color: #303133;
      font-size: 14px;
    }

    .attachment-items {
      padding: 8px;
    }

    .attachment-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      margin-bottom: 8px;
      background-color: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .attachment-info {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .attachment-icon {
      font-size: 20px;
      margin-right: 12px;
    }

    .attachment-details {
      flex: 1;
    }

    .attachment-name {
      font-size: 14px;
      color: #303133;
      margin-bottom: 4px;
      word-break: break-all;
    }

    .attachment-meta {
      font-size: 12px;
      color: #909399;
    }

    .attachment-actions {
      display: flex;
      gap: 8px;
    }

    .attachment-download {
      color: #409EFF;
      text-decoration: none;
      font-size: 12px;
      padding: 4px 8px;
      border: 1px solid #409EFF;
      border-radius: 3px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #409EFF;
        color: white;
      }
    }

    .attachment-remove {
      color: #f56c6c;
      background: none;
      border: 1px solid #f56c6c;
      border-radius: 3px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f56c6c;
        color: white;
      }
    }
  }

  // 图片预览样式
  .image-preview-container {
    text-align: center;
    padding: 20px;

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  // 图片调整大小相关样式
  .image-resize-container {
    position: relative;
    display: inline-block;
    margin: 5px;
    vertical-align: middle;
    line-height: 0;
    transition: all 0.2s ease;

    // 调整大小控制点
    .image-resize-handle {
      position: absolute;
      width: 10px;
      height: 10px;
      background: #409EFF;
      border: 2px solid #fff;
      border-radius: 50%;
      opacity: 0;
      transition: all 0.2s ease;
      z-index: 1000;
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.4);

      &:hover {
        background: #337ecc;
        transform: scale(1.2);
        box-shadow: 0 4px 8px rgba(64, 158, 255, 0.6);
      }

      &:active {
        transform: scale(1.0);
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.8);
      }

      // 四个角的位置和光标
      &.resize-nw {
        top: -5px;
        left: -5px;
        cursor: nw-resize;
      }

      &.resize-ne {
        top: -5px;
        right: -5px;
        cursor: ne-resize;
      }

      &.resize-sw {
        bottom: -5px;
        left: -5px;
        cursor: sw-resize;
      }

      &.resize-se {
        bottom: -5px;
        right: -5px;
        cursor: se-resize;
      }
    }

    // 图片样式
    img {
      display: block;
      max-width: 100%;
      height: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
    }

    // 悬停时显示控制点
    &:hover {
      .image-resize-handle {
        opacity: 1;
      }

      img {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
    }
  }

  // 强制隐藏所有孤立的控制点
  .image-resize-handle:not(.image-resize-container .image-resize-handle) {
    display: none !important;
  }

  // 确保只有在正确容器中的控制点才能显示
  .rich-content .image-resize-handle {
    display: none !important;
  }

  .rich-content .image-resize-container .image-resize-handle {
    display: block !important;
  }
}

// 表情包网格样式
.emoji-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 8px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
  background: #fff;
  border-radius: 4px;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  font-size: 20px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  }

  &:active {
    transform: scale(1.1);
  }
}

// 表情弹出框样式
:deep(.emoji-popover) {
  .el-popover {
    padding: 0 !important;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  }
}

// 滚动条样式
.emoji-grid::-webkit-scrollbar {
  width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 下拉框样式
:deep(.editor-select-dropdown) {
  z-index: 9999 !important;
}

:deep(.el-select-dropdown__item) {
  user-select: none;
}

// 图片预览覆盖层样式
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
}

.image-preview-container {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.preview-close-btn {
  position: absolute;
  top: -40px;
  right: -40px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #fff;
    transform: scale(1.1);
  }
}

/* 上传加载提示样式 */
:deep(.upload-loading-message) {
  .el-message__content {
    display: flex;
    align-items: center;
  }

  .el-message__content::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #409EFF;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 颜色选择器样式 */
.color-picker {
  padding: 8px;
  max-width: 200px;
}

.color-row {
  display: flex;
  gap: 2px;
  margin-bottom: 2px;
  line-height: 1;
}

.color-item {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin: 2px;
  cursor: pointer;
  border: 1px solid #ddd;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.2);
    border-color: #409EFF;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    z-index: 10;
    position: relative;
  }
}
</style>
