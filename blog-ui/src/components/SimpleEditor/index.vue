<template>
  <div class="simple-editor">
    <div class="editor-toolbar">
      <!-- 基本格式化 -->
      <div class="toolbar-group">
        <button class="toolbar-button" :class="{ active: isActive('bold') }" @click="toggleFormat('bold')" title="粗体">
          <i class="el-icon-bold">B</i>
        </button>
        <button class="toolbar-button" :class="{ active: isActive('italic') }" @click="toggleFormat('italic')" title="斜体">
          <i class="el-icon-italic">I</i>
        </button>
        <button class="toolbar-button" :class="{ active: isActive('strike') }" @click="toggleFormat('strike')" title="删除线">
          <i class="el-icon-delete"></i>
        </button>
        <button class="toolbar-button" @click="toggleBookmark" title="书签">
          <i class="el-icon-bookmark"></i>
        </button>
      </div>

      <!-- 标题 -->
      <div class="toolbar-group">
        <button class="toolbar-button" :class="{ active: isActive('header', 1) }" @click="toggleHeader(1)" title="标题1">
          H1
        </button>
        <button class="toolbar-button" :class="{ active: isActive('header', 2) }" @click="toggleHeader(2)" title="标题2">
          H2
        </button>
        <button class="toolbar-button" :class="{ active: isActive('header', 3) }" @click="toggleHeader(3)" title="标题3">
          H3
        </button>
        <button class="toolbar-button" @click="showEmojiPicker" title="表情">
          <i class="el-icon-emoji">😊</i>
        </button>
      </div>

      <!-- 列表和对齐 -->
      <div class="toolbar-group">
        <button class="toolbar-button" :class="{ active: isActive('list', 'ordered') }" @click="toggleList('ordered')" title="有序列表">
          <i class="el-icon-tickets"></i>
        </button>
        <button class="toolbar-button" :class="{ active: isActive('list', 'bullet') }" @click="toggleList('bullet')" title="无序列表">
          <i class="el-icon-menu"></i>
        </button>
        <button class="toolbar-button" :class="{ active: isActive('align', 'center') }" @click="toggleAlign('center')" title="居中对齐">
          <i class="el-icon-s-operation"></i>
        </button>
        <button class="toolbar-button" :class="{ active: isActive('blockquote') }" @click="toggleFormat('blockquote')" title="引用">
          <i class="el-icon-chat-line-square"></i>
        </button>
      </div>

      <!-- 链接和媒体 -->
      <div class="toolbar-group">
        <button class="toolbar-button" @click="insertLink" title="链接">
          <i class="el-icon-link"></i>
        </button>
        <button class="toolbar-button" @click="insertImage" title="图片">
          <i class="el-icon-picture"></i>
        </button>
        <button class="toolbar-button" @click="insertAttachment" title="附件">
          <i class="el-icon-paperclip"></i>
        </button>
      </div>

      <!-- 其他功能 -->
      <div class="toolbar-group">
        <button class="toolbar-button" @click="insertCheckbox" title="复选框">
          <i class="el-icon-check"></i>
        </button>
        <button class="toolbar-button" @click="insertHorizontalRule" title="水平线">
          <i class="el-icon-minus"></i>
        </button>
      </div>

      <!-- 撤销/重做 -->
      <div class="toolbar-group">
        <button class="toolbar-button" @click="undo" title="撤销">
          <i class="el-icon-back"></i>
        </button>
        <button class="toolbar-button" @click="redo" title="重做">
          <i class="el-icon-right"></i>
        </button>
      </div>
    </div>

    <!-- 字体选择 -->
    <div class="font-selector">
      <el-select v-model="selectedFont" placeholder="字体" @change="changeFont">
        <el-option
          v-for="font in fontOptions"
          :key="font.value"
          :label="font.label"
          :value="font.value">
        </el-option>
      </el-select>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="editor-content" ref="editor"></div>

    <!-- 表情选择器对话框 -->
    <el-dialog
      title="选择表情"
      :visible.sync="emojiDialogVisible"
      width="400px"
      :append-to-body="true"
    >
      <div class="emoji-container">
        <span
          v-for="(emoji, index) in emojis"
          :key="index"
          class="emoji-item"
          @click="insertEmoji(emoji)"
        >
          {{ emoji }}
        </span>
      </div>
    </el-dialog>

    <!-- 链接对话框 -->
    <el-dialog
      title="插入链接"
      :visible.sync="linkDialogVisible"
      width="400px"
      :append-to-body="true"
    >
      <el-form>
        <el-form-item label="链接文本">
          <el-input v-model="linkText" placeholder="请输入链接文本"></el-input>
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input v-model="linkUrl" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmInsertLink">确认</el-button>
          <el-button @click="linkDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Quill from 'quill';
import 'quill/dist/quill.snow.css';

// 表情列表
const emojis = [
  "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
  "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
  "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
  "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣",
  "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬"
];

export default {
  name: 'SimpleEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    }
  },
  data() {
    return {
      editor: null,
      content: '',
      selectedFont: 'sans-serif',
      fontOptions: [
        { label: '默认字体', value: 'sans-serif' },
        { label: '衬线字体', value: 'serif' },
        { label: '等宽字体', value: 'monospace' },
        { label: '宋体', value: 'SimSun' },
        { label: '黑体', value: 'SimHei' },
        { label: '微软雅黑', value: 'Microsoft YaHei' },
        { label: '楷体', value: 'KaiTi' }
      ],
      emojis: emojis,
      emojiDialogVisible: false,
      linkDialogVisible: false,
      linkText: '',
      linkUrl: ''
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.content && this.editor) {
          this.content = val;
          this.editor.root.innerHTML = this.content;
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initQuill();
  },
  methods: {
    initQuill() {
      // 注册字体
      const Font = Quill.import('formats/font');
      Font.whitelist = ['sans-serif', 'serif', 'monospace', 'SimSun', 'SimHei', 'Microsoft YaHei', 'KaiTi'];
      Quill.register(Font, true);

      // 创建编辑器
      this.editor = new Quill(this.$refs.editor, {
        modules: {
          toolbar: false, // 禁用默认工具栏，使用自定义工具栏
          history: {
            delay: 1000,
            maxStack: 100,
            userOnly: true
          }
        },
        placeholder: this.placeholder,
        theme: 'snow'
      });

      // 设置编辑器高度
      this.$refs.editor.style.height = `${this.height}px`;

      // 设置初始内容
      if (this.value) {
        this.editor.root.innerHTML = this.value;
      }

      // 监听内容变化
      this.editor.on('text-change', () => {
        const html = this.editor.root.innerHTML;
        if (html === '<p><br></p>') {
          this.content = '';
        } else {
          this.content = html;
        }
        this.$emit('input', this.content);
      });
    },

    // 检查格式是否激活
    isActive(format, value) {
      if (!this.editor) return false;
      const formats = this.editor.getFormat();
      if (value) {
        return formats[format] === value;
      }
      return !!formats[format];
    },

    // 切换格式
    toggleFormat(format) {
      if (!this.editor) return;
      this.editor.focus();
      const isActive = this.isActive(format);
      this.editor.format(format, !isActive);
    },

    // 切换标题
    toggleHeader(level) {
      if (!this.editor) return;
      this.editor.focus();
      const isActive = this.isActive('header', level);
      this.editor.format('header', isActive ? false : level);
    },

    // 切换列表
    toggleList(type) {
      if (!this.editor) return;
      this.editor.focus();
      const isActive = this.isActive('list', type);
      this.editor.format('list', isActive ? false : type);
    },

    // 切换对齐方式
    toggleAlign(align) {
      if (!this.editor) return;
      this.editor.focus();
      const isActive = this.isActive('align', align);
      this.editor.format('align', isActive ? false : align);
    },

    // 切换书签
    toggleBookmark() {
      if (!this.editor) return;
      this.editor.focus();
      // 实现书签功能
      // 这里只是一个示例，实际实现可能需要更复杂的逻辑
      this.editor.format('background', this.isActive('background', '#FFEB3B') ? false : '#FFEB3B');
    },

    // 显示表情选择器
    showEmojiPicker() {
      this.emojiDialogVisible = true;
    },

    // 插入表情
    insertEmoji(emoji) {
      if (!this.editor) return;
      this.editor.focus();
      const range = this.editor.getSelection();
      if (range) {
        this.editor.insertText(range.index, emoji);
        this.editor.setSelection(range.index + 1);
      }
      this.emojiDialogVisible = false;
    },

    // 插入链接
    insertLink() {
      this.linkText = '';
      this.linkUrl = '';
      this.linkDialogVisible = true;
    },

    // 确认插入链接
    confirmInsertLink() {
      if (!this.editor) return;
      this.editor.focus();
      const range = this.editor.getSelection();
      if (range) {
        if (this.linkText && this.linkUrl) {
          this.editor.deleteText(range.index, range.length);
          this.editor.insertText(range.index, this.linkText, 'link', this.linkUrl);
        } else if (this.linkUrl) {
          this.editor.format('link', this.linkUrl);
        }
      }
      this.linkDialogVisible = false;
    },

    // 插入图片
    insertImage() {
      if (!this.editor) return;
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = () => {
        const file = input.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            const range = this.editor.getSelection();
            if (range) {
              this.editor.insertEmbed(range.index, 'image', e.target.result);
            }
          };
          reader.readAsDataURL(file);
        }
      };
      input.click();
    },

    // 插入附件
    insertAttachment() {
      // 实现附件上传功能
      // 这里只是一个示例，实际实现可能需要更复杂的逻辑
      this.$message.info('附件功能待实现');
    },

    // 插入复选框
    insertCheckbox() {
      if (!this.editor) return;
      this.editor.focus();
      const range = this.editor.getSelection();
      if (range) {
        this.editor.insertText(range.index, '☐ ');
      }
    },

    // 插入水平线
    insertHorizontalRule() {
      if (!this.editor) return;
      this.editor.focus();
      const range = this.editor.getSelection();
      if (range) {
        this.editor.insertText(range.index, '\n---\n');
      }
    },

    // 撤销
    undo() {
      if (!this.editor) return;
      this.editor.history.undo();
    },

    // 重做
    redo() {
      if (!this.editor) return;
      this.editor.history.redo();
    },

    // 更改字体
    changeFont(font) {
      if (!this.editor) return;
      this.editor.focus();
      this.editor.format('font', font);
    }
  }
};
</script>

<style scoped>
.simple-editor {
  border: 2px solid #409eff;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.toolbar-group {
  display: flex;
  margin-right: 10px;
  border-right: 1px solid #dcdfe6;
  padding-right: 10px;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 2px;
  font-size: 14px;
}

.toolbar-button:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.toolbar-button.active {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

.font-selector {
  padding: 8px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.editor-content {
  padding: 10px;
  min-height: 200px;
  overflow-y: auto;
}

.emoji-container {
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.emoji-item:hover {
  transform: scale(1.2);
  background-color: #f0f0f0;
  border-radius: 4px;
}
</style>
