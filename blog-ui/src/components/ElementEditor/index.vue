<template>
  <div class="element-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="mini" :type="isBold ? 'primary' : 'default'" @click="toggleBold">B</el-button>
        <el-button size="mini" :type="isItalic ? 'primary' : 'default'" @click="toggleItalic">I</el-button>
        <el-button size="mini" :type="isUnderline ? 'primary' : 'default'" @click="toggleUnderline">U</el-button>
        <el-button size="mini" :type="isStrike ? 'primary' : 'default'" @click="toggleStrike">S</el-button>
      </el-button-group>

      <el-button-group style="margin-left: 10px;">
        <el-button size="mini" @click="setHeader(1)">H1</el-button>
        <el-button size="mini" @click="setHeader(2)">H2</el-button>
        <el-button size="mini" @click="setHeader(3)">H3</el-button>
        <el-button size="mini" @click="showEmojiPicker">😊</el-button>
      </el-button-group>

      <el-button-group style="margin-left: 10px;">
        <el-button size="mini" @click="setList('ordered')">1.</el-button>
        <el-button size="mini" @click="setList('bullet')">•</el-button>
        <el-button size="mini" @click="setAlign('center')">⧏⧐</el-button>
        <el-button size="mini" @click="setQuote">"</el-button>
      </el-button-group>

      <el-button-group style="margin-left: 10px;">
        <el-button size="mini" @click="insertLink">🔗</el-button>
        <el-button size="mini" @click="insertImage">🖼️</el-button>
        <el-button size="mini" @click="insertAttachment">📎</el-button>
      </el-button-group>

      <el-button-group style="margin-left: 10px;">
        <el-button size="mini" @click="insertCheckbox">✓</el-button>
        <el-button size="mini" @click="insertHorizontalRule">—</el-button>
      </el-button-group>

      <el-button-group style="margin-left: 10px;">
        <el-button size="mini" @click="undo">↩</el-button>
        <el-button size="mini" @click="redo">↪</el-button>
      </el-button-group>
    </div>

    <div class="font-selector">
      <el-dropdown trigger="click" @command="handleFontCommand" @visible-change="handleDropdownVisibleChange">
        <el-button size="mini" style="width: 120px; text-align: left; padding-left: 10px;">
          <span style="float: left;">{{ selectedFontLabel }}</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="font in fontOptions"
            :key="font.value"
            :command="font.value"
            :style="{fontFamily: font.value}">
            {{ font.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown trigger="click" @command="handleFontSizeCommand" @visible-change="handleDropdownVisibleChange" style="margin-left: 10px;">
        <el-button size="mini" style="width: 80px; text-align: left; padding-left: 10px;">
          <span style="float: left;">{{ selectedFontSize }}</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="size in fontSizeOptions"
            :key="size.value"
            :command="size.value">
            {{ size.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <div class="editor-content">
      <div ref="editorContent" class="rich-content" contenteditable="true" @input="handleInput" @mouseup="saveSelection" @keyup="saveSelection"></div>
    </div>

    <!-- 隐藏的文件上传输入框 -->
    <input type="file" ref="fileInput" style="display: none" @change="handleFileSelected" accept="image/*" />
    <input type="file" ref="attachmentInput" style="display: none" @change="handleAttachmentSelected" />

    <!-- 表情选择器对话框 -->
    <el-dialog
      title="选择表情"
      :visible.sync="emojiDialogVisible"
      width="400px"
      :append-to-body="true"
    >
      <div class="emoji-container">
        <span
          v-for="(emoji, index) in emojis"
          :key="index"
          class="emoji-item"
          @click="insertEmoji(emoji)"
        >
          {{ emoji }}
        </span>
      </div>
    </el-dialog>

    <!-- 链接对话框 -->
    <el-dialog
      title="插入链接"
      :visible.sync="linkDialogVisible"
      width="400px"
      :append-to-body="true"
    >
      <el-form>
        <el-form-item label="链接文本">
          <el-input v-model="linkText" placeholder="请输入链接文本"></el-input>
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input v-model="linkUrl" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmInsertLink">确认</el-button>
          <el-button @click="linkDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';
import { getToken } from "@/utils/auth";

// 表情列表
const emojis = [
  "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
  "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
  "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
  "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣",
  "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬"
];

export default {
  name: 'ElementEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    },
    uploadUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      content: '',
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isStrike: false,
      selectedFont: 'sans-serif',
      fontOptions: [
        { label: '默认字体', value: 'sans-serif' },
        { label: '衬线字体', value: 'serif' },
        { label: '等宽字体', value: 'monospace' },
        { label: '宋体', value: 'SimSun' },
        { label: '黑体', value: 'SimHei' },
        { label: '微软雅黑', value: 'Microsoft YaHei' },
        { label: '楷体', value: 'KaiTi' }
      ],
      emojis: emojis,
      emojiDialogVisible: false,
      linkDialogVisible: false,
      linkText: '',
      linkUrl: '',
      savedSelection: null,
      savedRange: null,
      actionSavedRange: null, // 用于存储操作前的选区
      selectedFontSize: '3',
      fontSizeOptions: [
        { label: '8px', value: '1' },
        { label: '10px', value: '2' },
        { label: '12px', value: '3' },
        { label: '14px', value: '4' },
        { label: '16px', value: '5' },
        { label: '18px', value: '6' },
        { label: '24px', value: '7' },
        { label: '32px', value: '8' },
        { label: '48px', value: '9' }
      ]
    };
  },
  computed: {
    selectedFontLabel() {
      const font = this.fontOptions.find(f => f.value === this.selectedFont);
      return font ? font.label : '字体';
    },
    selectedFontSize() {
      const size = this.fontSizeOptions.find(s => s.value === this.selectedFontSize);
      return size ? size.label : '字号';
    }
  },
  watch: {
    value: {
      handler(val) {
        if (this.$refs.editorContent && val !== this.getContentHtml()) {
          this.$refs.editorContent.innerHTML = val || '';
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initEditor();
  },
  methods: {
    initEditor() {
      const editorContent = this.$refs.editorContent;
      if (editorContent) {
        // 设置编辑器高度
        editorContent.style.minHeight = `${this.height}px`;
        // 设置初始内容
        editorContent.innerHTML = this.value || '';
        // 添加粘贴事件监听器，处理纯文本粘贴
        editorContent.addEventListener('paste', this.handlePaste);
      }
    },

    // 处理输入事件
    handleInput() {
      this.saveSelection();
      this.updateContent();
    },

    // 保存当前选区
    saveSelection() {
      if (window.getSelection) {
        const sel = window.getSelection();
        if (sel.getRangeAt && sel.rangeCount) {
          this.savedRange = sel.getRangeAt(0);
          this.savedSelection = sel;
        }
      }
    },

    // 在执行操作前保存选区，用于下拉菜单等交互
    saveSelectionBeforeAction() {
      // 先保存当前选区
      this.saveSelection();
      // 将选区保存到一个特殊的变量中，不会被其他操作覆盖
      this.actionSavedRange = this.savedRange ? this.savedRange.cloneRange() : null;
    },

    // 恢复选区
    restoreSelection() {
      if (window.getSelection) {
        const sel = window.getSelection();
        sel.removeAllRanges();

        // 优先使用操作前保存的选区
        if (this.actionSavedRange) {
          sel.addRange(this.actionSavedRange);
          // 使用后清除，避免影响后续操作
          this.actionSavedRange = null;
          return true;
        } else if (this.savedRange) {
          sel.addRange(this.savedRange);
          return true;
        }
      }
      return false;
    },

    // 获取编辑器内容
    getContentHtml() {
      return this.$refs.editorContent ? this.$refs.editorContent.innerHTML : '';
    },

    // 更新内容并触发 input 事件
    updateContent() {
      const html = this.getContentHtml();
      this.$emit('input', html);
    },

    // 执行文档命令
    execCommand(command, value = null) {
      this.restoreSelection();
      document.execCommand(command, false, value);
      this.updateContent();
      this.$refs.editorContent.focus();
    },

    // 切换粗体
    toggleBold() {
      this.execCommand('bold');
      this.isBold = !this.isBold;
    },

    // 切换斜体
    toggleItalic() {
      this.execCommand('italic');
      this.isItalic = !this.isItalic;
    },

    // 切换下划线
    toggleUnderline() {
      this.execCommand('underline');
      this.isUnderline = !this.isUnderline;
    },

    // 切换删除线
    toggleStrike() {
      this.execCommand('strikeThrough');
      this.isStrike = !this.isStrike;
    },

    // 设置标题
    setHeader(level) {
      this.execCommand('formatBlock', `<h${level}>`);
    },

    // 设置列表
    setList(type) {
      if (type === 'ordered') {
        this.execCommand('insertOrderedList');
      } else {
        this.execCommand('insertUnorderedList');
      }
    },

    // 设置对齐方式
    setAlign(align) {
      this.execCommand('justifyCenter');
    },

    // 设置引用
    setQuote() {
      this.execCommand('formatBlock', '<blockquote>');
    },

    // 显示表情选择器
    showEmojiPicker() {
      this.saveSelectionBeforeAction();
      this.emojiDialogVisible = true;
    },

    // 插入表情
    insertEmoji(emoji) {
      this.restoreSelection();
      this.execCommand('insertText', emoji);
      this.emojiDialogVisible = false;
    },

    // 插入链接
    insertLink() {
      this.saveSelectionBeforeAction();
      // 获取选中的文本作为链接文本
      if (this.savedRange || this.actionSavedRange) {
        const range = this.actionSavedRange || this.savedRange;
        this.linkText = range.toString();
      }
      this.linkUrl = '';
      this.linkDialogVisible = true;
    },

    // 确认插入链接
    confirmInsertLink() {
      this.restoreSelection();
      if (this.linkUrl) {
        if (this.linkText && this.savedRange && !this.savedRange.collapsed) {
          // 如果有选中文本，创建带文本的链接
          this.execCommand('createLink', this.linkUrl);
        } else {
          // 如果没有选中文本，创建带URL的链接
          const linkText = this.linkText || this.linkUrl;
          const linkHtml = `<a href="${this.linkUrl}" target="_blank">${linkText}</a>`;
          this.execCommand('insertHTML', linkHtml);
        }
      }
      this.linkDialogVisible = false;
    },

    // 插入图片
    insertImage() {
      this.saveSelectionBeforeAction();
      this.$refs.fileInput.click();
    },

    // 处理文件选择
    handleFileSelected(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 显示上传中提示
      const loading = this.$loading({
        lock: true,
        text: '图片上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 使用原生的上传方式
      const xhr = new XMLHttpRequest();
      xhr.open('POST', this.uploadUrl);
      xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());

      xhr.onload = () => {
        loading.close();
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log('上传响应:', response);
            if (response.code === 200) {
              const url = response.fileName || response.url || response.data;
              if (url) {
                this.restoreSelection();
                // 如果返回的是完整URL，直接使用，否则添加基础URL
                const imageUrl = url.startsWith('http') ? url : process.env.VUE_APP_BASE_API + url;
                this.execCommand('insertImage', imageUrl);
              }
            } else {
              this.$message.error(response.msg || '上传失败');
            }
          } catch (e) {
            console.error('解析响应失败:', e, xhr.responseText);
            this.$message.error('上传失败');
          }
        } else {
          console.error('上传失败:', xhr.status, xhr.statusText);
          this.$message.error('上传失败');
        }
      };

      xhr.onerror = () => {
        loading.close();
        console.error('上传失败');
        this.$message.error('上传失败');
      };

      const formData = new FormData();
      formData.append('file', file);
      xhr.send(formData);

      // 清空文件输入框，以便下次选择同一文件时也能触发change事件
      this.$refs.fileInput.value = '';
    },

    // 插入附件
    insertAttachment() {
      this.saveSelectionBeforeAction();
      this.$refs.attachmentInput.click();
    },

    // 处理附件选择
    handleAttachmentSelected(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 显示上传中提示
      const loading = this.$loading({
        lock: true,
        text: '附件上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 使用原生的上传方式
      const xhr = new XMLHttpRequest();
      xhr.open('POST', this.uploadUrl);
      xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());

      xhr.onload = () => {
        loading.close();
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log('上传响应:', response);
            if (response.code === 200) {
              const url = response.fileName || response.url || response.data;
              if (url) {
                this.restoreSelection();
                const fileName = file.name;
                // 如果返回的是完整URL，直接使用，否则添加基础URL
                const attachmentUrl = url.startsWith('http') ? url : process.env.VUE_APP_BASE_API + url;
                const attachmentHtml = `<a href="${attachmentUrl}" target="_blank" class="attachment">📎 ${fileName}</a>`;
                this.execCommand('insertHTML', attachmentHtml);
              }
            } else {
              this.$message.error(response.msg || '上传失败');
            }
          } catch (e) {
            console.error('解析响应失败:', e, xhr.responseText);
            this.$message.error('上传失败');
          }
        } else {
          console.error('上传失败:', xhr.status, xhr.statusText);
          this.$message.error('上传失败');
        }
      };

      xhr.onerror = () => {
        loading.close();
        console.error('上传失败');
        this.$message.error('上传失败');
      };

      const formData = new FormData();
      formData.append('file', file);
      xhr.send(formData);

      // 清空文件输入框，以便下次选择同一文件时也能触发change事件
      this.$refs.attachmentInput.value = '';
    },

    // 处理粘贴事件
    handlePaste(e) {
      // 阻止默认粘贴行为
      e.preventDefault();

      // 获取粘贴的文本
      let text = '';
      if (e.clipboardData && e.clipboardData.getData) {
        // 尝试获取HTML格式
        text = e.clipboardData.getData('text/html');
        if (!text) {
          // 如果没有HTML格式，获取纯文本
          text = e.clipboardData.getData('text/plain');
          // 将纯文本中的换行符转换为<br>
          if (text) {
            text = text.replace(/\n/g, '<br>');
          }
        }
      }

      // 插入文本
      if (text) {
        this.execCommand('insertHTML', text);
      }
    },

    // 插入复选框
    insertCheckbox() {
      this.execCommand('insertHTML', '☐ ');
    },

    // 插入水平线
    insertHorizontalRule() {
      this.execCommand('insertHorizontalRule');
    },

    // 撤销
    undo() {
      this.execCommand('undo');
    },

    // 重做
    redo() {
      this.execCommand('redo');
    },

    // 处理字体下拉菜单可见性变化
    handleDropdownVisibleChange(visible) {
      if (visible) {
        // 当下拉菜单显示时，保存选区
        this.saveSelectionBeforeAction();
      }
    },

    // 处理字体命令
    handleFontCommand(font) {
      // 设置选中的字体
      this.selectedFont = font;
      // 恢复选区
      this.restoreSelection();
      // 应用字体
      this.execCommand('fontName', font);
      // 让编辑器重新获得焦点
      this.$refs.editorContent.focus();
    },

    // 处理字体大小命令
    handleFontSizeCommand(size) {
      // 设置选中的字体大小
      this.selectedFontSize = size;
      // 恢复选区
      this.restoreSelection();
      // 应用字体大小
      this.execCommand('fontSize', size);
      // 让编辑器重新获得焦点
      this.$refs.editorContent.focus();
    }
  }
};
</script>

<style scoped>
.element-editor {
  border: 2px solid #409eff;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.font-selector {
  padding: 8px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.editor-content {
  padding: 10px;
}

.rich-content {
  min-height: 200px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  outline: none;
  line-height: 1.5;
  font-size: 14px;
  overflow-y: auto;
}

.emoji-container {
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.emoji-item:hover {
  transform: scale(1.2);
  background-color: #f0f0f0;
  border-radius: 4px;
}

/* 附件样式 */
.attachment {
  display: inline-block;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin: 5px 0;
  color: #409eff;
  text-decoration: none;
}

.attachment:hover {
  background-color: #e0e0e0;
}
</style>
