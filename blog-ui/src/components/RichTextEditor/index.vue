<template>
  <div class="rich-text-editor">
    <!-- 工具栏 -->
    <div :id="toolbarId">
      <!-- 基本格式 -->
      <span class="ql-formats">
        <select class="ql-font">
          <option value="sans-serif" selected>默认字体</option>
          <option value="serif">衬线字体</option>
          <option value="monospace">等宽字体</option>
        </select>
        <select class="ql-size">
          <option value="8px">8px</option>
          <option value="10px">10px</option>
          <option value="12px">12px</option>
          <option value="14px" selected>14px</option>
          <option value="16px">16px</option>
          <option value="18px">18px</option>
          <option value="20px">20px</option>
          <option value="24px">24px</option>
          <option value="32px">32px</option>
          <option value="48px">48px</option>
        </select>
      </span>

      <!-- 文本格式 -->
      <span class="ql-formats">
        <button class="ql-bold"></button>
        <button class="ql-italic"></button>
        <button class="ql-underline"></button>
        <button class="ql-strike"></button>
      </span>

      <!-- 颜色 -->
      <span class="ql-formats">
        <select class="ql-color"></select>
        <select class="ql-background"></select>
      </span>

      <!-- 对齐方式 -->
      <span class="ql-formats">
        <button class="ql-list" value="ordered"></button>
        <button class="ql-list" value="bullet"></button>
        <select class="ql-align"></select>
      </span>

      <!-- 链接和图片 -->
      <span class="ql-formats">
        <button class="ql-link"></button>
        <button class="ql-image"></button>
      </span>

      <!-- 表情按钮 -->
      <span class="ql-formats">
        <button class="ql-emoji" title="插入表情">😊</button>
      </span>
    </div>

    <!-- 编辑器容器 -->
    <div ref="editor"></div>

    <!-- 表情选择器弹窗 -->
    <el-dialog
      title="选择表情"
      :visible.sync="emojiDialogVisible"
      width="500px"
      :append-to-body="true"
      :before-close="closeEmojiDialog"
      custom-class="emoji-dialog"
    >
      <div class="emoji-search">
        <el-input
          v-model="emojiSearch"
          placeholder="搜索表情..."
          prefix-icon="el-icon-search"
          clearable
        ></el-input>
      </div>
      <div class="emoji-container">
        <span
          v-for="(item, index) in filteredEmojis"
          :key="index"
          class="emoji-item"
          @click="insertEmoji(item)"
        >
          {{ item }}
        </span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEmojiDialog">取消</el-button>
        <el-button type="primary" @click="closeEmojiDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import emojis from '@/utils/emoji';

export default {
  name: 'RichTextEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editor: null,
      content: '',
      emojis: emojis,
      emojiDialogVisible: false,
      emojiSearch: '',
      toolbarId: 'toolbar-' + Date.now() + '-' + Math.floor(Math.random() * 1000)
    };
  },
  computed: {
    filteredEmojis() {
      if (!this.emojiSearch) {
        return this.emojis;
      }
      return this.emojis.filter(emoji => emoji.includes(this.emojiSearch));
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.content && this.editor) {
          this.content = val;
          this.editor.root.innerHTML = this.content;
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initQuill();
  },
  beforeDestroy() {
    this.editor = null;
  },
  methods: {
    initQuill() {
      // 注册字体大小格式
      const Size = Quill.import('attributors/style/size');
      Size.whitelist = ['8px', '10px', '12px', '14px', '16px', '18px', '20px', '24px', '32px', '48px'];
      Quill.register(Size, true);

      // 创建工具栏处理程序
      const toolbarOptions = {
        container: '#' + this.toolbarId,
        handlers: {
          emoji: () => {
            this.emojiDialogVisible = true;
          }
        }
      };

      // 创建编辑器
      this.editor = new Quill(this.$refs.editor, {
        modules: {
          toolbar: toolbarOptions
        },
        placeholder: this.placeholder,
        readOnly: this.readOnly,
        theme: 'snow'
      });

      // 设置编辑器高度
      this.$refs.editor.style.height = `${this.height}px`;

      // 设置初始内容
      if (this.value) {
        this.editor.root.innerHTML = this.value;
      }

      // 监听内容变化
      this.editor.on('text-change', () => {
        const html = this.editor.root.innerHTML;
        if (html === '<p><br></p>') {
          this.content = '';
        } else {
          this.content = html;
        }
        this.$emit('input', this.content);
      });
    },

    // 插入表情
    insertEmoji(emoji) {
      if (!this.editor) return;

      const range = this.editor.getSelection();
      if (range) {
        // 直接插入表情文本
        this.editor.insertText(range.index, emoji);
        // 将光标移动到表情后面
        this.editor.setSelection(range.index + emoji.length);
      }

      this.closeEmojiDialog();
    },

    // 关闭表情选择器
    closeEmojiDialog() {
      this.emojiDialogVisible = false;
    }
  }
};
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #ccc;
  border-radius: 4px;
}

.emoji-container {
  display: flex;
  flex-wrap: wrap;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.emoji-item {
  font-size: 28px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s;
  margin: 2px;
  border-radius: 4px;
}

.emoji-item:hover {
  transform: scale(1.2);
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 表情搜索框样式 */
.emoji-search {
  margin-bottom: 15px;
}

/* 表情对话框样式 */
/deep/ .emoji-dialog .el-dialog__body {
  padding-top: 10px;
}

/* 自定义表情按钮样式 */
/deep/ .ql-emoji {
  font-size: 18px;
  line-height: 1;
}

/* 确保编辑器内容区域有足够的高度 */
/deep/ .ql-container {
  min-height: 200px;
}
</style>
