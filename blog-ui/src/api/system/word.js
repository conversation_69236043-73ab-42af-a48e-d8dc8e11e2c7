import request from '@/utils/request'

// 查询敏感词列表
export function listWord(query) {
  return request({
    url: '/system/word/list',
    method: 'get',
    params: query
  })
}

// 查询敏感词详细
export function getWord(wordId) {
  return request({
    url: '/system/word/' + wordId,
    method: 'get'
  })
}

// 新增敏感词
export function addWord(data) {
  return request({
    url: '/system/word',
    method: 'post',
    data: data
  })
}

// 修改敏感词
export function updateWord(data) {
  return request({
    url: '/system/word',
    method: 'put',
    data: data
  })
}

// 删除敏感词
export function delWord(wordId) {
  return request({
    url: '/system/word/' + wordId,
    method: 'delete'
  })
}

// 刷新敏感词缓存
export function refreshWordCache() {
  return request({
    url: '/system/word/refresh',
    method: 'get'
  })
}

// 设置指定敏感词
export function setSpecified(wordId, isSpecified) {
  return request({
    url: '/system/word/specified',
    method: 'put',
    data: {
      wordId: wordId,
      isSpecified: isSpecified
    }
  })
}

// 批量设置指定敏感词
export function batchSetSpecified(words) {
  return request({
    url: '/system/word/batchSpecified',
    method: 'post',
    data: words
  })
}

// 获取所有指定的敏感词
export function getSpecifiedWords() {
  return request({
    url: '/system/word/specified',
    method: 'get'
  })
}

// 导入敏感词
export function importWord(data) {
  return request({
    url: '/system/word/importData',
    method: 'post',
    data: data
  })
}

// 下载敏感词模板
export function getImportTemplate() {
  return request({
    url: '/system/word/template',
    method: 'get',
    responseType: 'blob'
  })
}
