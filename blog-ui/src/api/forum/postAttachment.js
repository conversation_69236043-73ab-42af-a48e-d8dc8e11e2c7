import request from '@/utils/request'

// 查询帖子附件列表
export function listPostAttachment(query) {
  return request({
    url: '/forum/postAttachment/list',
    method: 'get',
    params: query
  })
}

// 查询帖子附件详细
export function getPostAttachment(attachmentId) {
  return request({
    url: '/forum/postAttachment/' + attachmentId,
    method: 'get'
  })
}

// 新增帖子附件
export function addPostAttachment(data) {
  return request({
    url: '/forum/postAttachment',
    method: 'post',
    data: data
  })
}

// 修改帖子附件
export function updatePostAttachment(data) {
  return request({
    url: '/forum/postAttachment',
    method: 'put',
    data: data
  })
}

// 删除帖子附件
export function delPostAttachment(attachmentId) {
  return request({
    url: '/forum/postAttachment/' + attachmentId,
    method: 'delete'
  })
}
