import request from '@/utils/request'

// 查询用户发帖图片列表
export function listUserPostImage(query) {
  return request({
    url: '/forum/userPostImage/list',
    method: 'get',
    params: query
  })
}

// 查询用户发帖图片详细
export function getUserPostImage(imageId) {
  return request({
    url: '/forum/userPostImage/' + imageId,
    method: 'get'
  })
}

// 新增用户发帖图片
export function addUserPostImage(data) {
  return request({
    url: '/forum/userPostImage',
    method: 'post',
    data: data
  })
}

// 修改用户发帖图片
export function updateUserPostImage(data) {
  return request({
    url: '/forum/userPostImage',
    method: 'put',
    data: data
  })
}

// 删除用户发帖图片
export function delUserPostImage(imageId) {
  return request({
    url: '/forum/userPostImage/' + imageId,
    method: 'delete'
  })
}
