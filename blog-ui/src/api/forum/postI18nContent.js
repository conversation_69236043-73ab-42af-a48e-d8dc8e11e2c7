import request from '@/utils/request'

// 查询多语言帖子内容列表
export function listPostI18nContent(query) {
  return request({
    url: '/forum/postI18nContent/list',
    method: 'get',
    params: query
  })
}

// 查询多语言帖子内容详细
export function getPostI18nContent(contentId) {
  return request({
    url: '/forum/postI18nContent/' + contentId,
    method: 'get'
  })
}

// 新增多语言帖子内容
export function addPostI18nContent(data) {
  return request({
    url: '/forum/postI18nContent',
    method: 'post',
    data: data
  })
}

// 修改多语言帖子内容
export function updatePostI18nContent(data) {
  return request({
    url: '/forum/postI18nContent',
    method: 'put',
    data: data
  })
}

// 删除多语言帖子内容
export function delPostI18nContent(contentId) {
  return request({
    url: '/forum/postI18nContent/' + contentId,
    method: 'delete'
  })
}
