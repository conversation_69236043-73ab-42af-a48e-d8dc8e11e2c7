import request from '@/utils/request'

// 查询用户发帖列表
export function listUserPost(query) {
  return request({
    url: '/forum/userPost/list',
    method: 'get',
    params: query
  })
}

// 查询用户发帖详细
export function getUserPost(userPostId) {
  return request({
    url: '/forum/userPost/' + userPostId,
    method: 'get'
  })
}

// 新增用户发帖
export function addUserPost(data) {
  return request({
    url: '/forum/userPost',
    method: 'post',
    data: data
  })
}

// 修改用户发帖
export function updateUserPost(data) {
  return request({
    url: '/forum/userPost',
    method: 'put',
    data: data
  })
}

// 删除用户发帖
export function delUserPost(userPostId) {
  return request({
    url: '/forum/userPost/' + userPostId,
    method: 'delete'
  })
}
