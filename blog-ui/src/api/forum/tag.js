import request from '@/utils/request'

// 查询帖子标签列表
export function listTag(query) {
  return request({
    url: '/forum/forumTag/list',
    method: 'get',
    params: query
  })
}

// 查询帖子标签详细
export function getTag(tagId) {
  return request({
    url: '/forum/forumTag/' + tagId,
    method: 'get'
  })
}

// 新增帖子标签
export function addTag(data) {
  return request({
    url: '/forum/forumTag',
    method: 'post',
    data: data
  })
}

// 修改帖子标签
export function updateTag(data) {
  return request({
    url: '/forum/forumTag',
    method: 'put',
    data: data
  })
}

// 删除帖子标签
export function delTag(tagId) {
  return request({
    url: '/forum/forumTag/' + tagId,
    method: 'delete'
  })
}

// 获取支持的语言列表
export function listLanguages() {
  return request({
    url: '/forum/language/listEnabled',
    method: 'get'
  })
}
