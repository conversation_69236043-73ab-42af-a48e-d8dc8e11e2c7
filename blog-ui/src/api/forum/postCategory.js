import request from '@/utils/request'

// 查询帖子分类列表
export function listPostCategory(query) {
  return request({
    url: '/forum/postCategory/list',
    method: 'get',
    params: query
  })
}

// 查询帖子分类详细
export function getPostCategory(categoryId) {
  return request({
    url: '/forum/postCategory/' + categoryId,
    method: 'get'
  })
}

// 新增帖子分类
export function addPostCategory(data) {
  return request({
    url: '/forum/postCategory',
    method: 'post',
    data: data
  })
}

// 修改帖子分类
export function updatePostCategory(data) {
  return request({
    url: '/forum/postCategory',
    method: 'put',
    data: data
  })
}

// 删除帖子分类
export function delPostCategory(categoryId) {
  return request({
    url: '/forum/postCategory/' + categoryId,
    method: 'delete'
  })
}

// 获取支持的语言列表
export function listLanguages() {
  return request({
    url: '/forum/language/listEnabled',
    method: 'get'
  })
}
