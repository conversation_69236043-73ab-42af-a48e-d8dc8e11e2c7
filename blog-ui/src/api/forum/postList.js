import request from '@/utils/request'

// 查询帖子列表
export function listPost(query) {
  return request({
    url: '/forum/postList/list',
    method: 'get',
    params: query
  })
}

// 查询帖子详细
export function getPost(postId) {
  return request({
    url: '/forum/postList/' + postId,
    method: 'get'
  })
}

// 新增帖子
export function addPost(data) {
  return request({
    url: '/forum/postList',
    method: 'post',
    data: data
  })
}

// 修改帖子
export function updatePost(data) {
  return request({
    url: '/forum/postList',
    method: 'put',
    data: data
  })
}

// 删除帖子
export function delPost(postId) {
  return request({
    url: '/forum/postList/' + postId,
    method: 'delete'
  })
}

// 获取分类列表
export function getCategoryList() {
  return request({
    url: '/forum/postList/categories',
    method: 'get'
  })
}

// 获取标签列表
export function getTagList() {
  return request({
    url: '/forum/postList/tags',
    method: 'get'
  })
}

// 审核帖子
export function auditPost(data) {
  return request({
    url: '/forum/postList/audit',
    method: 'put',
    data: data
  })
}

// 设置帖子置顶状态
export function setPostTop(data) {
  return request({
    url: '/forum/postList/setTop',
    method: 'put',
    data: data
  })
}
