import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/forum/user/list',
    method: 'get',
    params: query
  })
}

// 修改用户禁止发帖状态
export function changeBannedPost(userId, isBannedPost) {
  return request({
    url: '/forum/user/changeBannedPost/' + userId + '/' + isBannedPost,
    method: 'put'
  })
}

// 修改用户状态
export function changeUserStatus(sysUserId, status) {
  return request({
    url: '/forum/user/changeStatus/' + sysUserId + '/' + status,
    method: 'put'
  })
}

// 重置用户密码
export function resetUserPassword(userId, newPassword) {
  return request({
    url: '/forum/user/resetPassword',
    method: 'post',
    params: {
      userId: userId,
      newPassword: newPassword
    }
  })
}
