import request from '@/utils/request'

// 查询语言类型列表
export function listLanguage(query) {
  return request({
    url: '/forum/language/list',
    method: 'get',
    params: query
  })
}

// 查询语言类型详细
export function getLanguage(langId) {
  return request({
    url: '/forum/language/' + langId,
    method: 'get'
  })
}

// 新增语言类型
export function addLanguage(data) {
  return request({
    url: '/forum/language',
    method: 'post',
    data: data
  })
}

// 修改语言类型
export function updateLanguage(data) {
  return request({
    url: '/forum/language',
    method: 'put',
    data: data
  })
}

// 删除语言类型
export function delLanguage(langId) {
  return request({
    url: '/forum/language/' + langId,
    method: 'delete'
  })
}
