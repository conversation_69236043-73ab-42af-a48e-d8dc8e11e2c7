import request from '@/utils/request'

// 查询用户管理列表
export function listUserExtend(query) {
  return request({
    url: '/forum/userExtend/list',
    method: 'get',
    params: query
  })
}

// 查询用户管理详细
export function getUserExtend(userId) {
  return request({
    url: '/forum/userExtend/' + userId,
    method: 'get'
  })
}

// 新增用户管理
export function addUserExtend(data) {
  return request({
    url: '/forum/userExtend',
    method: 'post',
    data: data
  })
}

// 修改用户管理
export function updateUserExtend(data) {
  return request({
    url: '/forum/userExtend',
    method: 'put',
    data: data
  })
}

// 删除用户管理
export function delUserExtend(userId) {
  return request({
    url: '/forum/userExtend/' + userId,
    method: 'delete'
  })
}

// 重置用户密码
export function resetUserPassword(userId, newPassword) {
  return request({
    url: '/forum/userExtend/resetPassword',
    method: 'post',
    params: {
      userId: userId,
      newPassword: newPassword
    }
  })
}
