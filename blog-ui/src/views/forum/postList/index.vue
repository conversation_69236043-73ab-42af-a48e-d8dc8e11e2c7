<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标签" prop="tagIds">
        <el-select v-model="queryParams.tagIds" placeholder="请选择标签" clearable multiple>
          <el-option
            v-for="tag in tagOptions"
            :key="tag.tagId"
            :label="`${tag.tagName} (${tag.postCount || 0})`"
            :value="tag.tagId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户昵称或用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable>
          <el-option
            v-for="category in categoryOptions"
            :key="category.categoryId"
            :label="`${category.categoryName} (${category.postCount || 0})`"
            :value="category.categoryId"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="状态" prop="postStatus">-->
<!--        <el-select v-model="queryParams.postStatus" placeholder="请选择状态" clearable>-->
<!--          <el-option key="all" label="全部" value="" />-->
<!--&lt;!&ndash;          <el-option key="0" label="草稿" value="0" />&ndash;&gt;-->
<!--          <el-option key="1" label="已发布" value="1" />-->
<!--          <el-option key="2" label="待审核" value="2" />-->
<!--          <el-option key="3" label="审核不通过" value="3" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="上架状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择上架状态" clearable>
          <el-option key="all" label="全部" value="" />
          <el-option key="0" label="正常" value="0" />
          <el-option key="2" label="下架" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键词"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['forum:post:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['forum:post:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDeleteBatch"
          v-hasPermi="['forum:post:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          disabled
          @click="handleExport"
          v-hasPermi="['forum:post:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="封面" align="center" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <el-image-->
<!--            v-if="scope.row.coverImage && scope.row.coverImage !== ''"-->
<!--            :src="scope.row.coverImage"-->
<!--            style="width: 80px; height: 50px"-->
<!--            :preview-src-list="[scope.row.coverImage]"-->
<!--            @error="handleImageError"-->
<!--          ></el-image>-->
<!--          <span v-else>无封面</span>-->
<!--          &lt;!&ndash; 调试信息 &ndash;&gt;-->
<!--          <div v-if="debug" style="display: none;">-->
<!--            {{ JSON.stringify(scope.row) }}-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->

<!--      <el-table-column label="描述" align="center" prop="summary" :show-overflow-tooltip="true" />-->
      <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="描述" align="center" prop="summary" width="200">
        <template slot-scope="scope">
          <div class="content-preview" @click="previewContent(scope.row.summary)">
            {{ scope.row.summary }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" prop="content" width="200">
        <template slot-scope="scope">
          <div class="content-preview" @click="previewContent(scope.row.content)">
            {{ contentPreview(scope.row.content) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="categoryName" />
      <el-table-column label="标签" align="center" width="150">
        <template slot-scope="scope">
          <el-tag
            v-for="tag in scope.row.tags"
            :key="tag.tagId"
            :type="tag.color ? undefined : 'info'"
            :color="tag.color"
            :effect="tag.color ? 'dark' : 'plain'"
            size="mini"
            :style="{
              marginRight: '5px',
              marginBottom: '5px',
              color: getTextColorForBackground(tag.color)
            }"
          >
            {{ tag.tagName || '无名称' }}
          </el-tag>
          <span v-if="!scope.row.tags || scope.row.tags.length === 0">无标签</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" prop="nickname" />
<!--      <el-table-column label="浏览量" align="center" prop="viewCount" width="80" />-->
<!--      <el-table-column label="评论数" align="center" prop="commentCount" width="80" />-->
<!--      <el-table-column label="点赞数" align="center" prop="likeCount" width="80" />-->
<!--      <el-table-column label="帖子状态" align="center" prop="postStatus">-->
<!--        <template slot-scope="scope">-->
<!--          <el-tag v-if="scope.row.postStatus === '0'" type="info">草稿</el-tag>-->
<!--          <el-tag v-else-if="scope.row.postStatus === '1'" type="success">已发布</el-tag>-->
<!--          <el-tag v-else-if="scope.row.postStatus === '2'" type="warning">待审核</el-tag>-->
<!--          <el-tag v-else-if="scope.row.postStatus === '3'" type="danger">审核不通过</el-tag>-->
<!--          <el-tag v-else type="info">{{ getPostStatusText(scope.row.postStatus) }}</el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="是否置顶" align="center" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isTop"
            :active-value="'1'"
            :inactive-value="'0'"
            @change="handleTopChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="上架状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0' || scope.row.status === 0 || scope.row.status === undefined || scope.row.status === null" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status === '1' || scope.row.status === 1" type="danger">已删除</el-tag>
          <el-tag v-else-if="scope.row.status === '2' || scope.row.status === 2" type="info">已下架</el-tag>
          <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['forum:post:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['forum:post:remove']"
          >删除</el-button>
          <!-- 审核按钮 -->
<!--          <el-dropdown v-if="scope.row.postStatus === '2' || scope.row.postStatus === 2" @command="(command) => handleAudit(command, scope.row)" v-hasPermi="['forum:post:edit']">-->
<!--            <el-button size="mini" type="text" icon="el-icon-s-check">审核<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--            <el-dropdown-menu slot="dropdown">-->
<!--              <el-dropdown-item command="pass">审核通过</el-dropdown-item>-->
<!--              <el-dropdown-item command="reject">审核不通过</el-dropdown-item>-->
<!--            </el-dropdown-menu>-->
<!--          </el-dropdown>-->
          <!-- 上下架按钮 -->
          <el-button
            v-if="scope.row.status === '0' || scope.row.status === 0 || scope.row.status === undefined || scope.row.status === null"
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleShelf(scope.row, '2')"
            v-hasPermi="['forum:post:edit']"
          >下架</el-button>
          <el-button
            v-if="scope.row.status === '2' || scope.row.status === 2"
            size="mini"
            type="text"
            icon="el-icon-upload2"
            @click="handleShelf(scope.row, '0')"
            v-hasPermi="['forum:post:edit']"
          >上架</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改帖子对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择分类">
            <el-option
              v-for="category in categoryOptions"
              :key="category.categoryId"
              :label="`${category.categoryName} (${category.postCount || 0})`"
              :value="category.categoryId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tagIds">
          <el-select v-model="form.tagIds" placeholder="请选择标签" multiple>
            <el-option
              v-for="tag in tagOptions"
              :key="tag.tagId"
              :label="`${tag.tagName} (${tag.postCount || 0})`"
              :value="tag.tagId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入描述" />
        </el-form-item>
<!--        <el-form-item label="封面图" prop="coverImage">-->
<!--          <el-upload-->
<!--            class="avatar-uploader"-->
<!--            :action="uploadImgUrl"-->
<!--            :headers="headers"-->
<!--            :show-file-list="false"-->
<!--            :on-success="handleCoverSuccess"-->
<!--            :before-upload="beforeCoverUpload">-->
<!--            <img v-if="form.coverImage" :src="form.coverImage" class="avatar">-->
<!--            <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--          </el-upload>-->
<!--        </el-form-item>-->
        <el-form-item label="内容" prop="content">
          <tiptap-editor
            ref="tiptapEditor"
            v-model="form.content"
            :height="300"
            :upload-url="ossUploadUrl"
            @attachment-change="handleAttachmentChange"/>
        </el-form-item>
<!--        <el-form-item label="帖子状态" prop="postStatus">-->
<!--          <el-radio-group v-model="form.postStatus">-->
<!--&lt;!&ndash;            <el-radio key="0" label="0">草稿</el-radio>&ndash;&gt;-->
<!--            <el-radio key="1" label="1">已发布</el-radio>-->
<!--            <el-radio key="2" label="2">待审核</el-radio>-->
<!--            <el-radio key="3" label="3">审核不通过</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
        <el-form-item label="上架状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="'0'">正常</el-radio>
            <el-radio :label="'2'">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
  getCategoryList,
  getTagList,
  auditPost,
  setPostTop
} from "@/api/forum/postList";
import { getToken } from "@/utils/auth";
import Editor from '@/components/Editor';
import ElementEditor from '@/components/ElementEditor';
import TiptapEditor from '@/components/TiptapEditor';

export default {
  name: "PostList",
  components: {
    Editor,
    ElementEditor,
    TiptapEditor
  },
  // 不再使用字典，直接硬编码状态
  // dicts: ['post_status'],
  data() {
    return {
      // 调试模式
      debug: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 帖子表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 上传地址
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      // OSS上传地址
      ossUploadUrl: process.env.VUE_APP_BASE_API + "/common/upload?bizType=forum",
      // 请求头
      headers: { Authorization: "Bearer " + getToken() },
      // 分类选项
      categoryOptions: [],
      // 标签选项
      tagOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tagIds: [],
        userName: null,
        categoryId: null,
        postStatus: null,
        status: null,
        keyword: null,
      },
      // 表单参数
      form: {
        postId: null,
        title: null,
        categoryId: null,
        tagIds: [],
        summary: null,
        coverImage: null,
        content: null,
        postStatus: "0",
        status: "0",
        attachments: [], // 附件列表
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "分类不能为空", trigger: "change" }
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getCategoryOptions();
    this.getTagOptions();
  },
  methods: {
    /** 查询帖子列表 */
    getList() {
      this.loading = true;

      // 添加默认查询条件，过滤掉已删除的帖子
      const queryParams = { ...this.queryParams };

      // 始终过滤掉删除状态的帖子，无论用户是否选择了状态过滤
      queryParams.excludeStatus = '1'; // 添加一个标记，表示始终不包含删除状态

      // 删除notStatus参数，使用excludeStatus替代
      if (queryParams.notStatus) {
        delete queryParams.notStatus;
      }

      listPost(queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;

      });
    },
    // 获取分类选项
    getCategoryOptions() {
      getCategoryList().then(response => {
        this.categoryOptions = response.data;
      });
    },
    // 获取标签选项
    getTagOptions() {
      getTagList().then(response => {
        this.tagOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: null,
        title: null,
        categoryId: null,
        tagIds: [],
        summary: null,
        coverImage: null,
        content: null,
        postStatus: "0",
        status: "0",
        attachments: [],
      };
      this.resetForm("form");
      // 重置编辑器的附件列表
      if (this.$refs.tiptapEditor) {
        this.$refs.tiptapEditor.setAttachments([]);
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 在提交查询前打印查询参数，便于调试
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加帖子";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids
      getPost(postId).then(response => {
        this.form = response.data;
        // 获取帖子关联的标签
        if (!this.form.tagIds) {
          this.form.tagIds = [];
        }
        // 确保状态字段为字符串
        if (this.form.status !== undefined && this.form.status !== null) {
          // 如果是删除状态，则设置为正常状态（编辑时不允许选择删除状态）
          if (this.form.status.toString() === '1') {
            this.form.status = "0";
          } else {
            this.form.status = this.form.status.toString();
          }
        } else {
          this.form.status = "0";
        }
        if (this.form.postStatus !== undefined && this.form.postStatus !== null) {
          this.form.postStatus = this.form.postStatus.toString();
        } else if (this.form.postStatus === undefined || this.form.postStatus === null) {
          this.form.postStatus = "0";
        }

        // 处理附件列表
        if (!this.form.attachments) {
          this.form.attachments = [];
        }

        // 设置编辑器的附件列表
        this.$nextTick(() => {
          if (this.$refs.tiptapEditor) {
            this.$refs.tiptapEditor.setAttachments(this.form.attachments);
          }
        });

        this.open = true;
        this.title = "修改帖子";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保 tagIds 是数组类型
          if (this.form.tagIds === undefined || this.form.tagIds === null) {
            this.form.tagIds = [];
          }

          // 如果 tagIds 不是数组，将其转换为数组
          if (!Array.isArray(this.form.tagIds)) {
            this.form.tagIds = [this.form.tagIds];
          }


          // 创建一个新的对象，确保数据类型正确
          const postData = { ...this.form };

          if (this.form.postId != null) {
            updatePost(postData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改失败：', error);
              this.$modal.msgError('修改失败，请检查后台日志');
            });
          } else {
            addPost(postData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增失败：', error);
              this.$modal.msgError('新增失败，请检查后台日志');
            });
          }
        }
      });
    },
    /** 删除按钮操作（逻辑删除） */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal.confirm('是否确认删除帖子编号为"' + postIds + '"的数据项？删除后将无法恢复！').then(() => {
        // 构建请求数据，将status设置为1（删除状态）
        const postData = {
          postId: postIds,
          status: '1'
        };
        // 使用updatePost而不是delPost，实现逻辑删除
        updatePost(postData).then(response => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        }).catch(error => {
          this.$modal.msgError('删除失败，请检查后台日志');
        });
      }).catch(() => {});
    },
    /** 批量删除按钮操作（逻辑删除） */
    handleDeleteBatch() {
      const postIds = this.ids;
      if (!postIds || postIds.length === 0) {
        this.$modal.msgWarning('请选择要删除的帖子');
        return;
      }

      this.$modal.confirm('是否确认删除选中的 ' + postIds.length + ' 个帖子？删除后将无法恢复！').then(() => {
        // 批量处理每个帖子的删除
        const deletePromises = postIds.map(postId => {
          const postData = {
            postId: postId,
            status: '1'
          };
          return updatePost(postData);
        });

        // 等待所有删除操作完成
        Promise.all(deletePromises).then(responses => {
          this.$modal.msgSuccess("批量删除成功");
          this.getList();
        }).catch(error => {
          console.error('批量删除失败：', error);
          this.$modal.msgError('批量删除失败，请检查后台日志');
        });
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('forum/postList/export', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    },

    /** 处理图片加载错误 */
    handleImageError(e) {
      // 将错误的图片替换为默认图片或隐藏
      e.target.style.display = 'none';
      // 显示错误提示
      const errorText = document.createElement('span');
      errorText.innerText = '图片错误';
      errorText.style.color = '#F56C6C';
      e.target.parentNode.appendChild(errorText);
    },

    // 根据背景色判断文字颜色
    getTextColorForBackground(backgroundColor) {
      // 如果没有背景色，返回默认颜色
      if (!backgroundColor) {
        return '';
      }

      // 将颜色转换为RGB格式
      let r, g, b;

      // 处理十六进制颜色值
      if (backgroundColor.startsWith('#')) {
        const hex = backgroundColor.substring(1);
        // 处理简写形式 #fff
        if (hex.length === 3) {
          r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
          g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
          b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
        }
        // 处理完整形式 #ffffff
        else if (hex.length === 6) {
          r = parseInt(hex.substring(0, 2), 16);
          g = parseInt(hex.substring(2, 4), 16);
          b = parseInt(hex.substring(4, 6), 16);
        } else {
          return '#000000'; // 默认使用黑色文字
        }
      }
      // 处理rgb格式
      else if (backgroundColor.startsWith('rgb')) {
        const rgbMatch = backgroundColor.match(/\d+/g);
        if (rgbMatch && rgbMatch.length >= 3) {
          r = parseInt(rgbMatch[0]);
          g = parseInt(rgbMatch[1]);
          b = parseInt(rgbMatch[2]);
        } else {
          return '#000000'; // 默认使用黑色文字
        }
      }
      // 其他格式或命名颜色，默认使用黑色文字
      else {
        return '#000000';
      }

      // 计算颜色亮度，使用亮度公式: (0.299*R + 0.587*G + 0.114*B)
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // 如果亮度超过0.6（较亮的颜色），使用黑色文字，否则使用白色文字
      return brightness > 0.6 ? '#000000' : '#ffffff';
    },

    // 封面图片上传成功处理
    handleCoverSuccess(res, file) {
      if (res.code === 200) {
        this.form.coverImage = res.url;
        this.$message.success('上传成功');
      } else {
        this.$message.error(res.msg);
      }
    },
    // 上传前校验封面图片
    beforeCoverUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传封面图片只能是 JPG/PNG/GIF 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传封面图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },

    // 根据状态码获取状态文本
    getPostStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '已发布',
        '2': '待审核',
        '3': '审核不通过'
      };
      return statusMap[status] || status;
    },

    // 内容预览，去除HTML标签和链接，截取前100个字符
    contentPreview(content) {
      if (!content) return '无内容';

      // 创建一个临时元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;

      // 移除所有链接
      const links = tempDiv.querySelectorAll('a');
      links.forEach(link => {
        link.parentNode.removeChild(link);
      });

      // 获取纯文本
      const textContent = tempDiv.textContent || tempDiv.innerText || '';

      // 去除多余的空格和换行
      const cleanText = textContent.replace(/\s+/g, ' ').trim();

      // 截取前100个字符
      return cleanText.length > 100 ? cleanText.substring(0, 100) + '...' : cleanText;
    },

    // 预览全部内容
    previewContent(content) {
      if (!content) {
        this.$message.info('无内容可预览');
        return;
      }

      this.$alert(
        '<div style="max-height: 500px; overflow-y: auto;">' + content + '</div>',
        '内容预览',
        {
          dangerouslyUseHTMLString: true,
          customClass: 'content-preview-dialog'
        }
      );
    },

    // 处理审核
    handleAudit(command, row) {
      const postStatus = command === 'pass' ? '1' : '3';
      const statusText = command === 'pass' ? '审核通过' : '审核不通过';

      this.$modal.confirm('确认要将帖子"' + row.title + '"标记为' + statusText + '吗？').then(() => {
        const postData = { postId: row.postId, postStatus: postStatus };
        auditPost(postData).then(response => {
          this.$modal.msgSuccess(statusText + '成功');
          this.getList();
        });
      }).catch(() => {});
    },

    // 处理置顶状态变更
    handleTopChange(row) {
      const isTop = row.isTop;
      console.log('当前置顶状态：', isTop, typeof isTop);
      const statusText = isTop === "1" ? '置顶' : '取消置顶';

      this.$modal.confirm('确认要' + statusText + '帖子"' + row.title + '"吗？').then(() => {
        const postData = { postId: row.postId, isTop: isTop };
        console.log('发送置顶请求：', postData);
        setPostTop(postData).then(response => {
          console.log('置顶请求响应：', response);
          this.$modal.msgSuccess(statusText + '成功');
          this.getList();
        }).catch(error => {
          console.error('置顶请求失败：', error);
          this.$modal.msgError('操作失败，请检查后台日志');
          // 如果请求失败，还原开关状态
          row.isTop = row.isTop === "1" ? "0" : "1";
        });
      }).catch(() => {
        console.log('用户取消操作，还原开关状态');
        // 如果用户取消操作，则还原开关状态
        row.isTop = row.isTop === "1" ? "0" : "1";
      });
    },

    // 处理上下架
    handleShelf(row, status) {
      const statusText = status === '0' ? '上架' : '下架';

      this.$modal.confirm('确认要将帖子"' + row.title + '"' + statusText + '吗？').then(() => {
        // 构建请求数据，只包含必要的字段
        const postData = {
          postId: row.postId,
          status: status
        };
        updatePost(postData).then(response => {
          this.$modal.msgSuccess(statusText + '成功');
          this.getList();
        }).catch(error => {
          this.$modal.msgError('操作失败，请检查后台日志');
        });
      }).catch(() => {});
    },

    // 处理附件变化
    handleAttachmentChange(attachments) {
      this.form.attachments = attachments.map(attachment => ({
        fileName: attachment.fileName,
        fileUrl: attachment.fileUrl,
        fileSize: attachment.fileSize,
        fileType: this.getFileExtension(attachment.fileName)
      }));
    },

    // 获取文件扩展名
    getFileExtension(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
    }
  }
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.content-preview {
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  line-height: 1.5;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
}

.content-preview:hover {
  color: #409EFF;
  text-decoration: underline;
}

/* 内容预览对话框样式 */
.content-preview-dialog {
  max-width: 80%;
  min-width: 600px;
}

.content-preview-dialog .el-message-box__content {
  max-height: 600px;
  overflow-y: auto;
}

.content-preview-dialog img {
  max-width: 100%;
  height: auto;
}

/* 编辑器样式 */
.el-tabs__content {
  padding: 10px 0;
}

/* 编辑器容器样式 */
.element-editor {
  border: 2px solid #409eff !important;
  margin-top: 0 !important;
}
</style>
