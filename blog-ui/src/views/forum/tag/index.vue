<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标签名称" prop="tagName">
        <el-input
          v-model="queryParams.tagName"
          placeholder="请输入标签名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['forum:forumTag:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['forum:forumTag:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['forum:forumTag:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          disabled
          @click="handleExport"
          v-hasPermi="['forum:forumTag:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tagList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="标签ID" align="center" prop="tagId" />-->
      <el-table-column label="标签名称" align="center" prop="tagName">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.color"
            :color="scope.row.color"
            :style="{color: isLightColor(scope.row.color) ? '#000' : '#fff'}">
            {{ scope.row.tagName }}
          </el-tag>
          <span v-else>{{ scope.row.tagName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="颜色" align="center" prop="color" width="120">
        <template slot-scope="scope">
          <el-color-picker
            v-if="scope.row.color"
            v-model="scope.row.color"
            size="mini"
            @change="handleColorChange(scope.row)"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
<!--      <el-table-column label="状态" align="center" prop="status">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.createTime) }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['forum:forumTag:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['forum:forumTag:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改帖子标签对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-tabs v-model="activeLanguage">
          <el-tab-pane v-for="lang in languages" :key="lang.langId" :label="lang.langName" :name="lang.langCode">
            <el-form-item :label="'标签名称(' + lang.langName + '):'">
              <el-input
                v-model="form.i18n[lang.langCode].tagName"
                :placeholder="'请输入' + lang.langName + '标签名称'"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
        <el-form-item label="颜色" prop="color">
          <el-color-picker v-model="form.color" show-alpha />
          <el-button size="mini" type="primary" @click="generateColor" style="margin-left: 10px">随机颜色</el-button>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTag, getTag, delTag, addTag, updateTag, listLanguages } from "@/api/forum/tag";

export default {
  name: "Tag",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 帖子标签表格数据
      tagList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前激活的语言标签
      activeLanguage: "zh-CN",
      // 支持的语言列表
      languages: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tagName: null,
        status: null,
      },
      // 表单参数
      form: {
        tagId: null,
        tagName: null,
        color: "#409EFF",
        sort: null,
        status: "0",
        remark: null,
        i18n: {}
      }
    };
  },
  created() {
    this.getList();
    this.getLanguages();
  },
  methods: {
    /** 查询帖子标签列表 */
    getList() {
      this.loading = true;
      listTag(this.queryParams).then(response => {
        // 确保每个标签都有默认颜色
        this.tagList = response.rows.map(tag => {
          if (!tag.color) {
            tag.color = "#409EFF";
          }
          return tag;
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tagId: null,
        tagName: null,
        color: "#409EFF",
        sort: null,
        status: "0",
        remark: null,
        i18n: {}
      };

      // 初始化多语言表单
      if (this.languages && this.languages.length > 0) {
        this.languages.forEach(lang => {
          this.$set(this.form.i18n, lang.langCode, {
            langId: lang.langId,
            tagName: ""
          });
        });
      }

      this.resetForm("form");
    },

    // 获取支持的语言列表
    getLanguages() {
      listLanguages().then(response => {
        this.languages = response.data;
        // 初始化多语言表单
        if (!this.form.i18n) {
          this.form.i18n = {};
        }
        this.languages.forEach(lang => {
          this.$set(this.form.i18n, lang.langCode, {
            langId: lang.langId,
            tagName: ""
          });
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.tagId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加帖子标签";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tagId = row.tagId || this.ids[0];
      getTag(tagId).then(response => {
        const data = response.data;
        this.form = {
          tagId: data.data.tagId,
          tagName: data.data.tagName,
          color: data.data.color || "#409EFF",
          sort: data.data.sort,
          status: data.data.status,
          remark: data.data.remark,
          i18n: {}
        };

        // 处理多语言数据
        if (data.i18n && Object.keys(data.i18n).length > 0) {
          // 遍历所有语言，确保每种语言都有数据
          this.languages.forEach(lang => {
            if (data.i18n[lang.langCode]) {
              // 使用Vue的$set方法确保响应式更新
              this.$set(this.form.i18n, lang.langCode, {
                langId: lang.langId,
                tagName: data.i18n[lang.langCode].tagName || ""
              });
            } else {
              // 如果没有该语言的数据，使用默认标签名称
              this.$set(this.form.i18n, lang.langCode, {
                langId: lang.langId,
                tagName: data.data.tagName || ""
              });
            }
          });
        } else {
          // 如果没有多语言数据，使用默认标签名称
          this.languages.forEach(lang => {
            this.$set(this.form.i18n, lang.langCode, {
              langId: lang.langId,
              tagName: data.data.tagName || ""
            });
          });
        }

        this.open = true;
        this.title = "修改帖子标签";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 手动验证表单
      let isValid = true;

      // 验证每种语言的标签名称是否填写
      for (const lang of this.languages) {
        if (!this.form.i18n[lang.langCode] || !this.form.i18n[lang.langCode].tagName) {
          this.$message.error(lang.langName + '标签名称不能为空');
          isValid = false;
          break;
        }
      }

      if (isValid) {
        // 将默认语言(中文)的标签名称设置到主表的tagName字段
        if (this.form.i18n["zh-CN"] && this.form.i18n["zh-CN"].tagName) {
          this.form.tagName = this.form.i18n["zh-CN"].tagName;
        }

        if (this.form.tagId != null) {
          updateTag(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        } else {
          addTag(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tagIds = row.tagId || this.ids;
      this.$modal.confirm('是否确认删除帖子标签编号为"' + tagIds + '"的数据项？').then(function() {
        return delTag(tagIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('forum/forumTag/export', {
        ...this.queryParams
      }, `tag_${new Date().getTime()}.xlsx`)
    },

    // 添加方法判断颜色是否为浅色，以决定文字颜色
    isLightColor(color) {
      // 如果没有颜色或者颜色不是十六进制格式，返回true（默认使用黑色文字）
      if (!color || typeof color !== 'string' || !color.startsWith('#')) {
        return true;
      }

      // 将十六进制颜色转换为RGB
      let hex = color.substring(1);
      // 处理简写形式（如#FFF）
      if (hex.length === 3) {
        hex = hex.split('').map(h => h + h).join('');
      }

      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      // 计算亮度（使用感知亮度公式）
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;

      // 如果亮度大于125，认为是浅色，应该使用黑色文字
      return brightness > 125;
    },

    // 生成随机颜色
    generateColor() {
      const colors = [
        "#409EFF", // 蓝色
        "#67C23A", // 绿色
        "#E6A23C", // 黄色
        "#F56C6C", // 红色
        "#909399", // 灰色
        "#8E44AD", // 紫色
        "#16A085", // 青色
        "#D35400", // 橙色
        "#2980B9", // 深蓝色
        "#27AE60"  // 深绿色
      ];
      const index = Math.floor(Math.random() * colors.length);
      this.form.color = colors[index];
    },

    // 处理颜色变更
    handleColorChange(row) {
      // 创建一个对象来保存需要更新的数据
      const updateData = {
        tagId: row.tagId,
        color: row.color
      };

      // 调用更新API
      updateTag(updateData).then(response => {
        this.$modal.msgSuccess("颜色更新成功");
        // 刷新标签列表
        this.getList();
      }).catch(() => {
        // 如果更新失败，刷新列表以恢复原来的颜色
        this.getList();
      });
    }
  }
};
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>
