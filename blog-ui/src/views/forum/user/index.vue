<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名称" prop="nameOrNickname">
        <el-input
          v-model="queryParams.nameOrNickname"
          placeholder="请输入用户名称或昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户邮箱" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入用户邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['forum:user:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" :height="tableHeight" border>
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="用户名称" align="center" prop="userName" min-width="100" show-overflow-tooltip />-->
      <el-table-column label="用户昵称" align="center" prop="nickname" min-width="100" show-overflow-tooltip />
      <el-table-column label="用户性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex"/>
        </template>
      </el-table-column>
      <el-table-column label="用户邮箱" align="center" prop="email" min-width="150" show-overflow-tooltip />
      <el-table-column label="用户头像" align="center" prop="avatar" width="80">
        <template slot-scope="scope">
          <el-image
            style="width: 40px; height: 40px; border-radius: 50%"
            :src="scope.row.avatar || '/avatar.jpg'"
            :preview-src-list="[scope.row.avatar || '/avatar.jpg']">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="个人简介" align="center" prop="bio" min-width="120">
        <template slot-scope="scope">
          <el-popover
            placement="top-start"
            title="个人简介"
            width="300"
            trigger="hover">
            <div style="max-height: 200px; overflow-y: auto;">{{ scope.row.bio || '暂无简介' }}</div>
            <el-button slot="reference" size="mini" type="text">{{ formatBio(scope.row.bio) }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="发帖数" align="center" prop="postCount" width="80">
        <template slot-scope="scope">
          <el-tag type="info">{{ scope.row.postCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="被评论数" align="center" prop="commentCount" width="80">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.commentCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="获赞数" align="center" prop="likeCount" width="80">
        <template slot-scope="scope">
          <el-tag type="danger">{{ scope.row.likeCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="收藏数" align="center" prop="collectCount" width="80">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.collectCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发帖权限" align="center" width="100">
        <template slot-scope="scope">
          <div class="permission-container">
            <el-switch
              v-model="scope.row.isBannedPost"
              active-value="1"
              inactive-value="0"
              active-color="#F56C6C"
              inactive-color="#67C23A"
              @change="handleBannedPostChange(scope.row)"
            >
            </el-switch>
            <div class="switch-label">
              <span v-if="scope.row.isBannedPost === '1'" class="ban-text">禁止</span>
              <span v-else class="allow-text">允许</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="帐号状态" align="center" width="100">
        <template slot-scope="scope">
          <div class="permission-container">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              active-color="#67C23A"
              inactive-color="#F56C6C"
              @change="handleStatusChange(scope.row)"
            >
            </el-switch>
            <div class="switch-label">
              <span v-if="scope.row.status === '0'" class="normal-text">正常</span>
              <span v-else class="disabled-text">停用</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="最后登录时间" align="center" prop="loginDate" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.loginDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleResetPassword(scope.row)"
            v-hasPermi="['forum:user:resetPassword']"
          >重置密码</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 重置密码对话框 -->
    <el-dialog title="重置用户密码" :visible.sync="resetPasswordOpen" width="500px" append-to-body>
      <el-form ref="resetPasswordForm" :model="resetPasswordForm" :rules="resetPasswordRules" label-width="80px">
        <el-form-item label="用户邮箱" prop="email">
          <el-input v-model="resetPasswordForm.email" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <div style="display: flex; gap: 10px;">
            <el-input v-model="resetPasswordForm.newPassword" type="password" placeholder="请输入新密码" show-password style="flex: 1;" />
            <el-button type="info" size="small" @click="setDefaultPassword">默认密码</el-button>
          </div>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="resetPasswordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResetPassword">确 定</el-button>
        <el-button @click="cancelResetPassword">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, changeBannedPost, changeUserStatus, resetUserPassword } from "@/api/forum/user";

export default {
  name: "User",
  dicts: ['sys_user_sex'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nameOrNickname: null,
        email: null
      },
      // 表格高度
      tableHeight: 600,
      // 重置密码对话框是否显示
      resetPasswordOpen: false,
      // 重置密码表单
      resetPasswordForm: {
        userId: null,
        email: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 重置密码表单校验
      resetPasswordRules: {
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: "密码长度为6-20位", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: this.validateConfirmPassword, trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.calculateTableHeight();
    // 监听窗口大小变化，动态调整表格高度
    window.addEventListener('resize', this.calculateTableHeight);
  },

  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 格式化个人简介
    formatBio(bio) {
      if (!bio) return '暂无简介';
      if (bio.length > 20) {
        return bio.substring(0, 20) + '...';
      }
      return bio;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改用户禁止发帖状态 */
    handleBannedPostChange(row) {
      let text = row.isBannedPost === "1" ? "禁止" : "允许";
      this.$modal.confirm('确认要' + text + '该用户发帖吗？').then(function() {
        return changeBannedPost(row.userId, row.isBannedPost);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.isBannedPost = row.isBannedPost === "1" ? "0" : "1";
      });
    },
    /** 修改用户状态 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要' + text + '该用户吗？').then(function() {
        return changeUserStatus(row.sysUserId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('forum/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },

    /** 计算表格高度 */
    calculateTableHeight() {
      // 计算表格高度，留出一定空间给其他元素
      this.tableHeight = window.innerHeight - 250;
    },

    /** 重置密码按钮操作 */
    handleResetPassword(row) {
      this.resetPasswordForm = {
        userId: row.userId,
        email: row.email,
        newPassword: '',
        confirmPassword: ''
      };
      this.resetPasswordOpen = true;
      this.$nextTick(() => {
        this.$refs["resetPasswordForm"].clearValidate();
      });
    },

    /** 确认密码验证 */
    validateConfirmPassword(rule, value, callback) {
      if (value === '') {
        callback(new Error('请再次输入新密码'));
      } else if (value !== this.resetPasswordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },

    /** 设置默认密码 */
    setDefaultPassword() {
      const defaultPassword = 'mdiyer123';
      this.resetPasswordForm.newPassword = defaultPassword;
      this.resetPasswordForm.confirmPassword = defaultPassword;
      this.$message.success('已设置默认密码：' + defaultPassword);
    },

    /** 提交重置密码 */
    submitResetPassword() {
      this.$refs["resetPasswordForm"].validate(valid => {
        if (valid) {
          resetUserPassword(this.resetPasswordForm.userId, this.resetPasswordForm.newPassword)
            .then(response => {
              this.$modal.msgSuccess("密码重置成功");
              this.resetPasswordOpen = false;
              this.resetPasswordForm = {
                userId: null,
                email: '',
                newPassword: '',
                confirmPassword: ''
              };
            });
        }
      });
    },

    /** 取消重置密码 */
    cancelResetPassword() {
      this.resetPasswordOpen = false;
      this.resetPasswordForm = {
        userId: null,
        email: '',
        newPassword: '',
        confirmPassword: ''
      };
    }
  }
};
</script>

<style scoped>
.user-name {
  font-weight: bold;
  color: #409EFF;
}

.permission-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.switch-label {
  margin-top: 5px;
  font-size: 12px;
}

.ban-text {
  color: #F56C6C;
  font-weight: bold;
}

.allow-text {
  color: #67C23A;
  font-weight: bold;
}

.normal-text {
  color: #67C23A;
  font-weight: bold;
}

.disabled-text {
  color: #F56C6C;
  font-weight: bold;
}

.el-tag {
  width: 50px;
}
</style>
