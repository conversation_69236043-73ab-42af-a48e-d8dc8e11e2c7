<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入评论内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="内容类型" prop="contentType">-->
<!--        <el-select v-model="queryParams.contentType" placeholder="请选择内容类型" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.forum_content_type"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['forum:comment:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="评论ID" align="center" prop="commentId" width="80" />-->
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div class="user-info">
            <el-avatar :size="40" :src="scope.row.avatar || '/avatar.jpg'"></el-avatar>
            <div class="nickname">{{ scope.row.nickname }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="评论内容" align="left" prop="content" :show-overflow-tooltip="false">
        <template slot-scope="scope">
          <div v-html="formatContent(scope.row.content)"></div>
        </template>
      </el-table-column>
      <el-table-column label="发帖类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isUserPost === '1'" type="warning">用户发帖</el-tag>
          <el-tag v-else type="primary">系统发帖</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="帖子标题" align="center" prop="postTitle" :show-overflow-tooltip="true" width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="handlePreviewPost(scope.row)" v-if="scope.row.postId">
            {{ scope.row.postTitle }}
          </el-link>
          <span v-else>{{ scope.row.postTitle }}</span>
        </template>
      </el-table-column>
      <el-table-column label="帖子内容" align="left" width="200">
        <template slot-scope="scope">
          <el-link type="primary" @click="handlePreviewPost(scope.row)" v-if="scope.row.postId">
            {{ formatPostContent(scope.row.postContent) }}
          </el-link>
          <span v-else>{{ formatPostContent(scope.row.postContent) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="点赞数" align="center" prop="likeCount" width="80" />
      <el-table-column label="回复数" align="center" prop="replyCount" width="80" />
      <el-table-column label="评论时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.replyCount > 0"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewReplies(scope.row)"
          >查看回复</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['forum:comment:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />

    <!-- 查看回复对话框 -->
    <el-dialog :title="'评论回复 - ' + replyComment.nickname" :visible.sync="replyOpen" width="800px" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="replyMultiple"
            @click="handleDeleteReplies"
            v-hasPermi="['forum:comment:remove']"
          >批量删除</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="replyLoading" :data="replyList" @selection-change="handleReplySelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户信息" align="center" width="150">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :size="40" :src="scope.row.avatar || '/avatar.jpg'"></el-avatar>
              <div class="nickname">{{ scope.row.nickname }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="回复内容" align="left" prop="content" :show-overflow-tooltip="false">
          <template slot-scope="scope">
            <div v-html="formatContent(scope.row.content)"></div>
          </template>
        </el-table-column>
        <el-table-column label="点赞数" align="center" prop="likeCount" width="80" />
        <el-table-column label="评论时间" align="center" prop="createTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteReply(scope.row)"
              v-hasPermi="['forum:comment:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 帖子预览组件 -->
    <post-preview
      :visible.sync="postPreviewVisible"
      :post-id="currentPostId"
      :is-user-post="currentIsUserPost"
      @reset="resetPostPreview"
    />
  </div>
</template>

<script>
import { listComment, delComment, getCommentReplies } from "@/api/forum/comment";
import PostPreview from "./components/PostPreview";

export default {
  name: "Comment",
  components: {
    PostPreview
  },
  dicts: ['forum_content_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评论表格数据
      commentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 回复列表加载状态
      replyLoading: false,
      // 是否显示回复对话框
      replyOpen: false,
      // 当前查看回复的评论
      replyComment: {},
      // 回复列表
      replyList: [],
      // 回复选中数组
      replyIds: [],
      // 回复非多个禁用
      replyMultiple: true,
      // 帖子预览相关
      postPreviewVisible: false,
      currentPostId: null,
      currentIsUserPost: '0',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickname: null,
        content: null,
        contentType: null,
        parentId: 0
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询评论列表 */
    getList() {
      this.loading = true;
      listComment(this.queryParams).then(response => {
        this.commentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 格式化评论内容
    formatContent(content) {
      if (!content) return '';
      // 限制显示100个字符
      if (content.length > 100) {
        content = content.substring(0, 100) + '...';
      }
      return content;
    },
    // 格式化帖子内容（去除链接，截取前50个字符）
    formatPostContent(content) {
      if (!content) return '';

      // 去除HTML标签和链接
      let plainText = content.replace(/<a[^>]*>.*?<\/a>/g, ''); // 移除链接
      plainText = plainText.replace(/<[^>]*>/g, ''); // 移除其他HTML标签

      // 限制显示50个字符
      if (plainText.length > 50) {
        plainText = plainText.substring(0, 50) + '...';
      }

      return plainText;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 关闭帖子预览对话框
      this.postPreviewVisible = false;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 关闭帖子预览对话框
      this.postPreviewVisible = false;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.commentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    // 回复多选框选中数据
    handleReplySelectionChange(selection) {
      this.replyIds = selection.map(item => item.commentId)
      this.replyMultiple = !selection.length
    },
    /** 查看回复按钮操作 */
    handleViewReplies(row) {
      this.replyComment = row;
      this.replyOpen = true;
      this.replyLoading = true;
      getCommentReplies(row.commentId).then(response => {
        this.replyList = response.data;
        this.replyLoading = false;
      });
    },
    /** 预览帖子详情 */
    handlePreviewPost(row) {
      if (row.postId) {
        // 先关闭对话框，确保重置状态
        this.postPreviewVisible = false;

        // 使用setTimeout确保在当前事件循环结束后再打开新的对话框
        setTimeout(() => {
          this.currentPostId = row.postId;
          this.currentIsUserPost = row.isUserPost;
          this.postPreviewVisible = true;
        }, 100);
      }
    },

    /** 重置帖子预览状态 */
    resetPostPreview() {
      // 使用setTimeout确保在当前事件循环结束后再重置状态
      setTimeout(() => {
        this.currentPostId = null;
        this.currentIsUserPost = '0';
      }, 0);
    },

    /** 分页操作 */
    handlePagination() {
      // 关闭帖子预览对话框
      this.postPreviewVisible = false;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const commentIds = row.commentId || this.ids;
      this.$modal.confirm('是否确认删除评论编号为"' + commentIds + '"的数据项？').then(function() {
        return delComment(commentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 删除回复按钮操作 */
    handleDeleteReply(row) {
      this.$modal.confirm('是否确认删除评论编号为"' + row.commentId + '"的数据项？').then(function() {
        return delComment(row.commentId);
      }).then(() => {
        // 重新加载回复列表
        getCommentReplies(this.replyComment.commentId).then(response => {
          this.replyList = response.data;
        });
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 批量删除回复按钮操作 */
    handleDeleteReplies() {
      const commentIds = this.replyIds;
      this.$modal.confirm('是否确认批量删除选中的回复？').then(function() {
        return delComment(commentIds);
      }).then(() => {
        // 重新加载回复列表
        getCommentReplies(this.replyComment.commentId).then(response => {
          this.replyList = response.data;
        });
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nickname {
  margin-top: 5px;
  font-size: 12px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
