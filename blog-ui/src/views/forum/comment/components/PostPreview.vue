<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="800px" append-to-body>
    <div v-loading="loading" class="post-preview">
      <div class="post-header">
        <h2 class="post-title">{{ post.title }}</h2>
        <div class="post-meta">
          <span>发布时间: {{ parseTime(post.createTime) }}</span>
          <span v-if="post.isUserPost === '1'" class="post-type user-post">用户发帖</span>
          <span v-else class="post-type system-post">系统发帖</span>
        </div>
      </div>
      <div class="post-content" v-html="post.content"></div>
    </div>
  </el-dialog>
</template>

<script>
import { getPost } from "@/api/forum/postList";

export default {
  name: "PostPreview",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    postId: {
      type: [Number, String],
      default: null
    },
    isUserPost: {
      type: String,
      default: '0'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
        if (!val) {
          // 对话框关闭时清除帖子数据并通知父组件
          this.post = {
            title: '',
            content: '',
            createTime: '',
            isUserPost: '0'
          };
          this.$emit('reset');
        }
      }
    },
    title() {
      return this.post.isUserPost === '1' ? '用户发帖详情' : '系统发帖详情';
    }
  },
  data() {
    return {
      loading: false,
      post: {
        title: '',
        content: '',
        createTime: '',
        isUserPost: '0'
      }
    };
  },
  watch: {
    visible(val) {
      if (val && this.postId) {
        this.getPostDetail();
      }
    },
    postId() {
      // 当postId变化时，如果对话框可见，则重新加载帖子详情
      if (this.visible && this.postId) {
        this.getPostDetail();
      }
    }
  },
  methods: {
    getPostDetail() {
      if (!this.postId) return;

      this.loading = true;
      getPost(this.postId).then(response => {
        if (response.data) {
          this.post = response.data;
          this.post.isUserPost = this.isUserPost;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    close() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style scoped>
.post-preview {
  padding: 10px;
}
.post-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.post-title {
  margin: 0 0 10px 0;
  font-size: 20px;
  color: #333;
}
.post-meta {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.post-meta span {
  margin-right: 15px;
}
.post-type {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}
.user-post {
  background-color: #e6a23c;
  color: white;
}
.system-post {
  background-color: #409eff;
  color: white;
}
.post-content {
  line-height: 1.6;
  font-size: 14px;
}
</style>
