<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="头像URL" prop="avatar">
        <el-input
          v-model="queryParams.avatar"
          placeholder="请输入头像URL"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="个人简介" prop="bio">
        <el-input
          v-model="queryParams.bio"
          placeholder="请输入个人简介"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发帖数" prop="postCount">
        <el-input
          v-model="queryParams.postCount"
          placeholder="请输入发帖数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论数" prop="commentCount">
        <el-input
          v-model="queryParams.commentCount"
          placeholder="请输入评论数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="获赞数" prop="likeCount">
        <el-input
          v-model="queryParams.likeCount"
          placeholder="请输入获赞数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收藏数" prop="collectCount">
        <el-input
          v-model="queryParams.collectCount"
          placeholder="请输入收藏数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否禁止发帖" prop="isBannedPost">
        <el-input
          v-model="queryParams.isBannedPost"
          placeholder="请输入是否禁止发帖"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邮箱是否验证(0否 1是)" prop="emailVerified">
        <el-input
          v-model="queryParams.emailVerified"
          placeholder="请输入邮箱是否验证(0否 1是)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['forum:userExtend:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['forum:userExtend:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['forum:userExtend:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['forum:userExtend:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userExtendList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="昵称" align="center" prop="nickname" />
      <el-table-column label="头像URL" align="center" prop="avatar" />
      <el-table-column label="个人简介" align="center" prop="bio" />
      <el-table-column label="发帖数" align="center" prop="postCount" />
      <el-table-column label="评论数" align="center" prop="commentCount" />
      <el-table-column label="获赞数" align="center" prop="likeCount" />
      <el-table-column label="收藏数" align="center" prop="collectCount" />
      <el-table-column label="是否禁止发帖" align="center" prop="isBannedPost" />
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="邮箱是否验证(0否 1是)" align="center" prop="emailVerified" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['forum:userExtend:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleResetPassword(scope.row)"
            v-hasPermi="['forum:userExtend:resetPassword']"
          >重置密码</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['forum:userExtend:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="头像URL" prop="avatar">
          <el-input v-model="form.avatar" placeholder="请输入头像URL" />
        </el-form-item>
        <el-form-item label="个人简介" prop="bio">
          <el-input v-model="form.bio" placeholder="请输入个人简介" />
        </el-form-item>
        <el-form-item label="发帖数" prop="postCount">
          <el-input v-model="form.postCount" placeholder="请输入发帖数" />
        </el-form-item>
        <el-form-item label="评论数" prop="commentCount">
          <el-input v-model="form.commentCount" placeholder="请输入评论数" />
        </el-form-item>
        <el-form-item label="获赞数" prop="likeCount">
          <el-input v-model="form.likeCount" placeholder="请输入获赞数" />
        </el-form-item>
        <el-form-item label="收藏数" prop="collectCount">
          <el-input v-model="form.collectCount" placeholder="请输入收藏数" />
        </el-form-item>
        <el-form-item label="是否禁止发帖" prop="isBannedPost">
          <el-input v-model="form.isBannedPost" placeholder="请输入是否禁止发帖" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="邮箱是否验证(0否 1是)" prop="emailVerified">
          <el-input v-model="form.emailVerified" placeholder="请输入邮箱是否验证(0否 1是)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog title="重置用户密码" :visible.sync="resetPasswordOpen" width="400px" append-to-body>
      <el-form ref="resetPasswordForm" :model="resetPasswordForm" :rules="resetPasswordRules" label-width="80px">
        <el-form-item label="用户ID">
          <el-input v-model="resetPasswordForm.userId" disabled />
        </el-form-item>
        <el-form-item label="用户邮箱">
          <el-input v-model="resetPasswordForm.email" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResetPassword" :loading="resetPasswordLoading">确 定</el-button>
        <el-button @click="cancelResetPassword">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserExtend, getUserExtend, delUserExtend, addUserExtend, updateUserExtend, resetUserPassword } from "@/api/forum/userExtend";

export default {
  name: "UserExtend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户管理表格数据
      userExtendList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickname: null,
        avatar: null,
        bio: null,
        postCount: null,
        commentCount: null,
        likeCount: null,
        collectCount: null,
        isBannedPost: null,
        email: null,
        emailVerified: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        email: [
          { required: true, message: "邮箱不能为空", trigger: "blur" }
        ],
      },
      // 重置密码对话框
      resetPasswordOpen: false,
      resetPasswordLoading: false,
      resetPasswordForm: {
        userId: null,
        email: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 重置密码表单校验
      resetPasswordRules: {
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: "密码长度为6-20位", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value !== this.resetPasswordForm.newPassword) {
                callback(new Error("两次输入的密码不一致"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户管理列表 */
    getList() {
      this.loading = true;
      listUserExtend(this.queryParams).then(response => {
        this.userExtendList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        nickname: null,
        avatar: null,
        bio: null,
        postCount: null,
        commentCount: null,
        likeCount: null,
        collectCount: null,
        isBannedPost: null,
        updateTime: null,
        email: null,
        emailVerified: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids
      getUserExtend(userId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != null) {
            updateUserExtend(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUserExtend(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm('是否确认删除用户管理编号为"' + userIds + '"的数据项？').then(function() {
        return delUserExtend(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('forum/userExtend/export', {
        ...this.queryParams
      }, `userExtend_${new Date().getTime()}.xlsx`)
    },
    /** 重置密码按钮操作 */
    handleResetPassword(row) {
      this.resetPasswordForm = {
        userId: row.userId,
        email: row.email,
        newPassword: '',
        confirmPassword: ''
      };
      this.resetPasswordOpen = true;
    },
    /** 提交重置密码 */
    submitResetPassword() {
      this.$refs["resetPasswordForm"].validate(valid => {
        if (valid) {
          this.resetPasswordLoading = true;
          resetUserPassword(this.resetPasswordForm.userId, this.resetPasswordForm.newPassword).then(response => {
            this.$modal.msgSuccess("密码重置成功");
            this.resetPasswordOpen = false;
            this.resetPasswordLoading = false;
            this.resetResetPasswordForm();
          }).catch(() => {
            this.resetPasswordLoading = false;
          });
        }
      });
    },
    /** 取消重置密码 */
    cancelResetPassword() {
      this.resetPasswordOpen = false;
      this.resetResetPasswordForm();
    },
    /** 重置重置密码表单 */
    resetResetPasswordForm() {
      this.resetPasswordForm = {
        userId: null,
        email: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.resetForm("resetPasswordForm");
    }
  }
};
</script>
