<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类名称" prop="categoryName">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入分类名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['forum:postCategory:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['forum:postCategory:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['forum:postCategory:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['forum:postCategory:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postCategoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="分类ID" align="center" prop="categoryId" />-->
      <el-table-column label="分类名称" align="center" prop="categoryName" />
      <el-table-column label="排序" align="center" prop="sort" />
<!--      <el-table-column label="状态" align="center" prop="status" />-->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['forum:postCategory:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['forum:postCategory:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改帖子分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-tabs v-model="activeLanguage">
          <el-tab-pane v-for="lang in languages" :key="lang.langId" :label="lang.langName" :name="lang.langCode">
            <el-form-item :label="'分类名称(' + lang.langName + '):'">
              <el-input v-model="form.i18n[lang.langCode].categoryName" :placeholder="'请输入' + lang.langName + '分类名称'" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPostCategory, getPostCategory, delPostCategory, addPostCategory, updatePostCategory, listLanguages } from "@/api/forum/postCategory";

export default {
  name: "PostCategory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 帖子分类表格数据
      postCategoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前激活的语言标签
      activeLanguage: "en-US",
      // 支持的语言列表
      languages: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: null,
        sort: null,
        status: null,
      },
      // 表单参数
      form: {
        i18n: {}
      },
      // 表单校验
      // 注意：我们直接在表单项中定义规则，而不是在这里
    };
  },
  created() {
    // 先加载语言列表，然后再加载分类列表
    this.getLanguages().then(() => {
      this.getList();
    }).catch(error => {
      console.error('初始化语言列表失败:', error);
      // 即使语言列表加载失败，仍然尝试加载分类列表
      this.getList();
    });
  },
  methods: {
    /** 查询帖子分类列表 */
    getList() {
      this.loading = true;

      // 添加当前语言参数
      this.queryParams.langId = this.getCurrentLangId();

      console.log('查询参数:', this.queryParams);

      listPostCategory(this.queryParams).then(response => {
        console.log('分类列表响应:', response);
        this.postCategoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取分类列表失败:', error);
        this.loading = false;
      });
    },

    // 获取当前语言ID
    getCurrentLangId() {
      // 获取当前系统语言代码
      const langCode = this.getCurrentSystemLanguage();
      console.log('当前语言代码:', langCode);
      console.log('可用语言列表:', this.languages);

      // 如果语言列表为空，返回默认值
      if (!this.languages || this.languages.length === 0) {
        console.log('语言列表为空，返回默认值 1');
        return 1; // 默认使用英语，假设 ID 为 1
      }

      const lang = this.languages.find(l => l.langCode === langCode);
      if (lang) {
        console.log('找到语言:', lang);
        return lang.langId;
      } else {
        // 如果没有找到对应的语言，使用第一个语言
        console.log('没有找到语言，使用第一个语言:', this.languages[0]);
        return this.languages[0].langId;
      }
    },

    // 获取当前系统语言代码
    getCurrentSystemLanguage() {
      // 这里可以根据系统实际情况获取当前语言
      // 如果没有特定的获取方式，默认使用英文
      return "en-US";
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        categoryId: null,
        categoryName: null,
        sort: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        i18n: {}
      };

      // 初始化多语言表单
      if (this.languages && this.languages.length > 0) {
        this.languages.forEach(lang => {
          this.$set(this.form.i18n, lang.langCode, {
            langId: lang.langId,
            categoryName: ""
          });
        });
      }

      this.resetForm("form");
    },

    // 获取支持的语言列表
    getLanguages() {
      return new Promise((resolve, reject) => {
        listLanguages().then(response => {
          console.log('获取到的语言列表:', response.data);
          this.languages = response.data;
          // 初始化多语言表单
          if (!this.form.i18n) {
            this.form.i18n = {};
          }
          this.languages.forEach(lang => {
            this.$set(this.form.i18n, lang.langCode, {
              langId: lang.langId,
              categoryName: ""
            });
          });
          resolve(this.languages);
        }).catch(error => {
          console.error('获取语言列表失败:', error);
          reject(error);
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.categoryId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加帖子分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const categoryId = row.categoryId || this.ids
      console.log('请求分类详情，categoryId:', categoryId);

      // 确保语言列表已经加载
      const loadLanguages = (!this.languages || this.languages.length === 0)
        ? this.getLanguages()
        : Promise.resolve(this.languages);

      // 等待语言列表加载完成后再请求分类详情
      loadLanguages.then(() => {
        return getPostCategory(categoryId);
      }).then(response => {
        console.log('原始响应数据:', response);

        // 先清空表单
        this.form = {
          categoryId: null,
          categoryName: null,
          sort: null,
          status: null,
          remark: null,
          i18n: {}
        };

        // 获取返回的数据
        const data = response.data;
        console.log('获取到的分类数据:', data);  // 添加日志，便于调试

        // 设置基本属性
        this.form.categoryId = data.categoryId;
        this.form.categoryName = data.categoryName;
        this.form.sort = data.sort || 0;  // 添加默认值
        this.form.status = data.status || '0';  // 添加默认值
        this.form.remark = data.remark || '';  // 添加默认值

        console.log('设置后的表单数据:', this.form);  // 添加日志，便于调试

        // 初始化多语言对象
        this.form.i18n = {};

        // 处理多语言数据
        console.log('检查params字段:', data.params);

        if (data.params && data.params.i18n && Object.keys(data.params.i18n).length > 0) {
          console.log('找到多语言数据:', data.params.i18n);

          // 遍历所有语言，确保每种语言都有数据
          this.languages.forEach(lang => {
            console.log('处理语言:', lang.langCode, lang.langId);

            if (data.params.i18n[lang.langCode]) {
              console.log('该语言有数据:', data.params.i18n[lang.langCode]);

              // 使用Vue的$set方法确保响应式更新
              this.$set(this.form.i18n, lang.langCode, {
                langId: lang.langId,
                categoryName: data.params.i18n[lang.langCode].categoryName || ""
              });

              // 如果是英语，确保设置到主表的categoryName字段
              if (lang.langCode === "en-US") {
                this.form.categoryName = data.params.i18n[lang.langCode].categoryName || "";
              }
            } else {
              console.log('该语言没有数据:', lang.langCode);

              // 如果没有该语言的数据，使用空字符串
              this.$set(this.form.i18n, lang.langCode, {
                langId: lang.langId,
                categoryName: ""
              });
            }
          });
        } else {
          console.log('没有找到多语言数据');

          // 如果没有多语言数据，使用空字符串
          this.languages.forEach(lang => {
            this.$set(this.form.i18n, lang.langCode, {
              langId: lang.langId,
              categoryName: ""
            });
          });
        }

        // 设置当前激活的语言标签为英语
        this.activeLanguage = "en-US";

        this.open = true;
        this.title = "修改帖子分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 手动验证表单
      let isValid = true;

      // 验证每种语言的分类名称是否填写
      for (const lang of this.languages) {
        if (!this.form.i18n[lang.langCode] || !this.form.i18n[lang.langCode].categoryName) {
          this.$message.error(lang.langName + '分类名称不能为空');
          isValid = false;
          break;
        }
      }

      if (isValid) {
        // 将默认语言(英文)的分类名称设置到主表的categoryName字段
        if (this.form.i18n["en-US"] && this.form.i18n["en-US"].categoryName) {
          this.form.categoryName = this.form.i18n["en-US"].categoryName;
        } else {
          // 如果没有英文数据，使用第一个可用的语言
          const firstLang = Object.keys(this.form.i18n)[0];
          if (firstLang && this.form.i18n[firstLang].categoryName) {
            this.form.categoryName = this.form.i18n[firstLang].categoryName;
          }
        }

        // 确保sort字段有值
        if (this.form.sort === null || this.form.sort === undefined) {
          this.form.sort = 0;
        }

        // 确保status字段有值
        if (!this.form.status) {
          this.form.status = '0';
        }

        // 深拷贝表单数据，防止引用问题
        const formData = JSON.parse(JSON.stringify(this.form));

        console.log('提交的表单数据:', formData);  // 添加日志，便于调试

        if (formData.categoryId != null) {
          console.log('调用修改方法, categoryId:', formData.categoryId);  // 添加日志
          updatePostCategory(formData).then(response => {
            console.log('修改响应:', response);
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            console.error('修改失败:', error);
          });
        } else {
          console.log('调用新增方法');  // 添加日志
          addPostCategory(formData).then(response => {
            console.log('新增响应:', response);
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            console.error('新增失败:', error);
          });
        }
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const categoryIds = row.categoryId || this.ids;
      this.$modal.confirm('是否确认删除帖子分类编号为"' + categoryIds + '"的数据项？').then(function() {
        return delPostCategory(categoryIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('forum/postCategory/export', {
        ...this.queryParams
      }, `postCategory_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
