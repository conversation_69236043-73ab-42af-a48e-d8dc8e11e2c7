// 由于后台管理端使用的是 Vue 2，我们需要创建一个简化版本
// 这个文件主要用于字体大小的配置

export const fontSizeOptions = [
  { value: '12px', label: '1' },
  { value: '13px', label: '2' },
  { value: '14px', label: '3' },
  { value: '15px', label: '4' },
  { value: '20px', label: '5' },
  { value: '22px', label: '6' },
  { value: '30px', label: '7' }
]

// 字体大小应用函数
export function applyFontSize(element, size) {
  if (element && size) {
    element.style.fontSize = size
  }
}

// 获取当前字体大小
export function getCurrentFontSize(element) {
  if (element) {
    return window.getComputedStyle(element).fontSize
  }
  return null
}
