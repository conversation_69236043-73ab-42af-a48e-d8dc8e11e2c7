import { getToken } from '@/utils/auth'

/**
 * 分片上传工具类
 */
export class ChunkUploader {
  constructor(options = {}) {
    this.baseURL = options.baseURL || process.env.VUE_APP_BASE_API
    this.chunkSize = options.chunkSize || 5 * 1024 * 1024 // 5MB
    this.bizType = options.bizType || 'forum'
    this.onProgress = options.onProgress || (() => {})
    this.onError = options.onError || (() => {})
    this.onSuccess = options.onSuccess || (() => {})
  }

  /**
   * 生成文件唯一标识（与C端保持一致）
   * @param {File} file 文件对象
   * @returns {string} 文件唯一标识
   */
  generateFileIdentifier(file) {
    // 使用文件名、大小和最后修改时间组合生成唯一标识，与C端保持一致
    const lastModified = file.lastModified ? file.lastModified.toString() : Date.now().toString()
    return `${file.name}-${file.size}-${lastModified}`
  }

  /**
   * 检查分片状态 - 支持断点续传
   * @param {string} identifier 文件标识
   * @param {number} chunkNumber 分片编号
   * @returns {Promise<{skipUpload: boolean, uploadedChunks: number[], progress: number, fileUrl?: string}>}
   */
  async checkChunk(identifier, chunkNumber) {
    try {
      const response = await fetch(`${this.baseURL}/api/oss/chunk/check?identifier=${encodeURIComponent(identifier)}&chunkNumber=${chunkNumber}&bizType=${encodeURIComponent(this.bizType)}`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + getToken(),
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      if (result.code === 200 && result.data) {
        return {
          skipUpload: result.data.skipUpload || false,
          uploadedChunks: result.data.uploadedChunks || [],
          progress: result.data.progress || 0,
          fileUrl: result.data.fileUrl
        }
      }
      return {
        skipUpload: false,
        uploadedChunks: [],
        progress: 0
      }
    } catch (error) {
      console.error('检查分片状态失败:', error)
      return {
        skipUpload: false,
        uploadedChunks: [],
        progress: 0
      }
    }
  }

  /**
   * 检查文件是否已存在（保留兼容性）
   * @param {string} identifier 文件标识
   * @returns {Promise<object>} 检查结果 {exists: boolean, fileUrl?: string}
   */
  async checkFileExists(identifier) {
    const chunkResult = await this.checkChunk(identifier, 1)
    return {
      exists: chunkResult.skipUpload && !!chunkResult.fileUrl,
      fileUrl: chunkResult.fileUrl
    }
  }

  /**
   * 上传分片
   * @param {File} file 原文件
   * @param {Blob} chunk 分片数据
   * @param {object} chunkInfo 分片信息
   * @returns {Promise<object>} 上传结果
   */
  async uploadChunk(file, chunk, chunkInfo) {
    const formData = new FormData()
    formData.append('file', chunk)
    formData.append('chunkNumber', chunkInfo.chunkNumber)
    formData.append('totalChunks', chunkInfo.totalChunks)
    formData.append('chunkSize', this.chunkSize)
    formData.append('currentChunkSize', chunk.size)
    formData.append('totalSize', file.size)
    formData.append('identifier', chunkInfo.identifier)
    formData.append('filename', file.name)
    formData.append('bizType', this.bizType)

    const response = await fetch(`${this.baseURL}/api/oss/chunk/upload`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + getToken()
      },
      body: formData
    })

    const result = await response.json()
    if (result.code !== 200) {
      throw new Error(result.msg || '分片上传失败')
    }

    // 返回后端响应的数据，可能包含fileUrl（如果是最后一个分片且已自动合并）
    return result.data || {}
  }



  /**
   * 上传文件 - 支持断点续传
   * @param {File} file 文件对象
   * @returns {Promise<string>} 文件URL
   */
  async upload(file) {
    try {
      // 生成文件标识（与C端保持一致）
      this.onProgress({ stage: 'calculating', percent: 0, message: '正在生成文件标识...' })
      const identifier = this.generateFileIdentifier(file)

      // 检查分片上传状态
      const chunkCheck = await this.checkChunk(identifier, 1)

      console.log('🔍 分片检查结果:', {
        identifier,
        skipUpload: chunkCheck.skipUpload,
        uploadedChunks: chunkCheck.uploadedChunks,
        progress: chunkCheck.progress,
        fileUrl: chunkCheck.fileUrl
      })

      // 如果文件已完全上传（有fileUrl说明文件已完成）
      if (chunkCheck.fileUrl) {
        console.log('✅ 文件已完全上传，直接返回URL:', chunkCheck.fileUrl)
        this.onProgress({ stage: 'completed', percent: 100, message: '文件已存在，跳过上传' })
        this.onSuccess(chunkCheck.fileUrl)
        return chunkCheck.fileUrl
      }

      // 计算分片信息
      const totalChunks = Math.ceil(file.size / this.chunkSize)

      // 获取已上传的分片列表
      const uploadedChunks = chunkCheck.uploadedChunks || []

      // 如果有已上传的分片，显示断点续传提示
      if (uploadedChunks.length > 0) {
        const resumeProgress = Math.round((uploadedChunks.length / totalChunks) * 100)

        // 显示断点续传提示
        this.onProgress({
          stage: 'uploading',
          percent: resumeProgress,
          message: `🔄 检测到已上传的分片，正在恢复上传进度... (${uploadedChunks.length}/${totalChunks})`
        })

        // 延迟3秒让用户看到提示
        await new Promise(resolve => setTimeout(resolve, 3000))

        // 再次显示恢复信息
        this.onProgress({
          stage: 'uploading',
          percent: resumeProgress,
          message: `✅ 已恢复上传进度，继续上传剩余分片... (${uploadedChunks.length}/${totalChunks})`
        })

        // 再延迟1秒
        await new Promise(resolve => setTimeout(resolve, 1000))
      } else {
        this.onProgress({ stage: 'uploading', percent: chunkCheck.progress || 0, message: '开始上传分片...' })
      }

      // 上传分片
      for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
        // 如果分片已上传，跳过
        if (uploadedChunks.includes(chunkNumber)) {
          const percent = Math.round((chunkNumber / totalChunks) * 100)
          this.onProgress({
            stage: 'uploading',
            percent,
            message: `跳过已上传分片 ${chunkNumber}/${totalChunks}`
          })
          continue
        }

        // 创建分片
        const start = (chunkNumber - 1) * this.chunkSize
        const end = Math.min(start + this.chunkSize, file.size)
        const chunk = file.slice(start, end)

        // 上传分片
        const uploadResult = await this.uploadChunk(file, chunk, {
          chunkNumber,
          totalChunks,
          identifier
        })

        const percent = Math.round((chunkNumber / totalChunks) * 100)
        this.onProgress({ stage: 'uploading', percent, message: `已上传分片 ${chunkNumber}/${totalChunks}` })

        // 检查是否返回了文件URL（后端自动合并完成）
        if (uploadResult && uploadResult.fileUrl) {
          this.onProgress({ stage: 'completed', percent: 100, message: '上传完成' })
          this.onSuccess(uploadResult.fileUrl)
          return uploadResult.fileUrl
        }
      }

      // 如果所有分片都上传完成但没有返回fileUrl，抛出错误
      throw new Error('所有分片上传完成但未获得文件URL')
    } catch (error) {
      this.onError(error)
      throw error
    }
  }
}

/**
 * 简单上传（小于10MB的文件）
 * @param {File} file 文件对象
 * @param {string} uploadUrl 上传URL
 * @returns {Promise<string>} 文件URL
 */
export async function simpleUpload(file, uploadUrl) {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch(uploadUrl, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + getToken()
    },
    body: formData
  })

  const result = await response.json()
  if (result.code !== 200) {
    throw new Error(result.msg || '上传失败')
  }
  return result.url
}
