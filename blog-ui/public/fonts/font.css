@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Caliste";
    /* 引用字体包 */
    src: url('./Caliste.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Billstone";
    /* 引用字体包 */
    src: url('./Billstone.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Howdy";
    /* 引用字体包 */
    src: url('./Howdy.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "Miskan";
    /* 引用字体包 */
    src: url('./Miskan.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "FandolSong-Bold";
    /* 引用字体包 */
    src: url('./FandolSong-Bold.otf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "SourceHanSansCN";
    /* 引用字体包 */
    src: url('./_思源黑体SourceHanSansCN-Bold.otf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "kaiti";
    /* 引用字体包 */
    src: url('./王汉宗粗楷体简.ttf');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    /* 自定义的字体名车，调用该字体时使用 */
    font-family: "fangsong";
    /* 引用字体包 */
    src: url('./王汉宗中仿宋简.ttf');
    font-weight: normal;
    font-style: normal;
}
