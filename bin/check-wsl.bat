@echo off
chcp 65001
echo.
echo [INFO] Checking WSL environment.
echo.

echo [STEP 1] Checking if WSL is installed and running...
wsl --status
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] WSL is not installed or not running properly.
    echo Please install WSL by running the following command as administrator:
    echo wsl --install
    goto :end
)

echo [STEP 2] Checking if Docker is installed in WSL...
wsl which docker
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker is not installed in WSL.
    echo Please install Docker in WSL by running the following commands in WSL:
    echo sudo apt-get update
    echo sudo apt-get install -y docker.io
    goto :end
)

echo [STEP 3] Checking if Docker is running in WSL...
wsl docker info
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker is not running in WSL.
    echo Please start Docker in WSL by running the following command in WSL:
    echo sudo service docker start
    goto :end
)

echo [STEP 4] Checking if sshpass is installed in WSL...
wsl which sshpass
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] sshpass is not installed in WSL.
    echo Please run setup-wsl.bat to install sshpass.
    goto :end
)

echo [SUCCESS] WSL environment is properly configured.
echo You can now run docker-build.bat to build and deploy your application.

:end
pause
