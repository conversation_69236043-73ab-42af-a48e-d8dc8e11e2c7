@echo off
chcp 65001
echo.
echo [INFO] Building project with <PERSON><PERSON> and creating Docker image.
echo.

cd %~dp0
cd ..

echo [STEP 1] Building project with <PERSON><PERSON> (skipping tests)...
call mvn clean package -Dmaven.test.skip=true -DskipTests

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Maven build failed. Please check the error messages above.
    goto :end
)

if not exist "%CD%\blog-admin\target\blog-admin.jar" (
    echo [ERROR] JAR file not found. Maven build may have failed.
    goto :end
)

echo [STEP 2] Copying files to WSL environment...

:: Create directory in WSL
wsl mkdir -p ~/blog-docker/blog-admin

:: Copy Dockerfile to WSL
wsl cp -f %CD:\=/%/blog-admin/Dockerfile ~/blog-docker/blog-admin/

:: Copy JAR file to WSL
wsl cp -f %CD:\=/%/blog-admin/target/blog-admin.jar ~/blog-docker/blog-admin/

:: Copy docker-compose.yml to WSL
wsl cp -f %CD:\=/%/docker-compose.yml ~/blog-docker/

echo [STEP 3] Building Docker image in WSL...
wsl cd ~/blog-docker/blog-admin && docker build -t blog-admin:latest .

echo [STEP 4] Transferring files to remote server...

:: Create and execute the script directly in WSL
wsl bash -c "echo '#!/bin/bash' > ~/scp_transfer.sh && echo 'set -e' >> ~/scp_transfer.sh && echo 'echo \"Creating directory on remote server...\"' >> ~/scp_transfer.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"mkdir -p /data/docker/server/blog-admin\"' >> ~/scp_transfer.sh && echo 'echo \"Copying files to remote server...\"' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no ~/blog-docker/blog-admin/blog-admin.jar root@*************:/data/docker/server/blog-admin/' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no ~/blog-docker/blog-admin/Dockerfile root@*************:/data/docker/server/blog-admin/' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no ~/blog-docker/docker-compose.yml root@*************:/data/docker/server/' >> ~/scp_transfer.sh && echo 'echo \"Building Docker image on remote server...\"' >> ~/scp_transfer.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"cd /data/docker/server/blog-admin && docker build -t blog-admin:latest .\"' >> ~/scp_transfer.sh && echo 'echo \"SCP transfer and remote build completed successfully!\"' >> ~/scp_transfer.sh && chmod +x ~/scp_transfer.sh && SSHPASS=e487h2IF sshpass -e ~/scp_transfer.sh"

echo [STEP 5] Completed.
echo Files have been successfully transferred to the remote server and Docker image has been built.
echo You can use the following command to start the container on the remote server:
echo ssh root@************* "cd /data/docker/server && docker-compose up -d"
echo.
echo Or use the following command to view logs on the remote server:
echo ssh root@************* "docker logs -f blog-admin"
echo.

:end
pause
