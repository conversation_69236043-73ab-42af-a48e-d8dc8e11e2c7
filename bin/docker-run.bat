@echo off
chcp 65001
echo.
echo [INFO] Starting Docker container on remote server.
echo.

echo [STEP 1] Starting Docker container on remote server...

:: Create and execute the script directly in WSL
wsl bash -c "echo '#!/bin/bash' > ~/remote_run.sh && echo 'set -e' >> ~/remote_run.sh && echo 'echo \"Starting containers on remote server...\"' >> ~/remote_run.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"cd /data/docker/server && docker-compose up -d\"' >> ~/remote_run.sh && echo 'echo \"Containers started successfully!\"' >> ~/remote_run.sh && echo 'echo \"Checking container logs...\"' >> ~/remote_run.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"docker logs blog-admin | tail -n 20\"' >> ~/remote_run.sh && chmod +x ~/remote_run.sh && SSHPASS=e487h2IF sshpass -e ~/remote_run.sh"

echo [STEP 2] Completed.
echo Remote containers have been started.
echo You can use the following command to view logs on the remote server:
echo ssh root@************* "docker logs -f blog-admin"
echo.

pause
