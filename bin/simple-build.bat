@echo off
chcp 65001
echo.
echo [INFO] Simple build and deploy script.
echo.

cd %~dp0
cd ..

echo [STEP 1] Building project with <PERSON><PERSON> (skipping tests)...
call mvn clean package -Dmaven.test.skip=true -DskipTests -pl blog-admin -am

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] <PERSON>ven build failed. Please check the error messages above.
    goto :end
)

if not exist "%CD%\blog-admin\target\blog-admin.jar" (
    echo [ERROR] JAR file not found. Maven build may have failed.
    goto :end
)

echo [STEP 2] Creating Dockerfile in blog-admin directory...

echo # Using JDK 1.8 as base image > blog-admin\Dockerfile
echo FROM openjdk:8-jdk-alpine >> blog-admin\Dockerfile
echo # Set working directory >> blog-admin\Dockerfile
echo WORKDIR /app >> blog-admin\Dockerfile
echo # Add author information >> blog-admin\Dockerfile
echo LABEL maintainer="blog-admin" >> blog-admin\Dockerfile
echo # Set timezone to Asia/Shanghai >> blog-admin\Dockerfile
echo RUN apk add --no-cache tzdata ^&^& ^\ >> blog-admin\Dockerfile
echo     cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime ^&^& ^\ >> blog-admin\Dockerfile
echo     echo "Asia/Shanghai" ^> /etc/timezone ^&^& ^\ >> blog-admin\Dockerfile
echo     apk del tzdata >> blog-admin\Dockerfile
echo # Set environment variables >> blog-admin\Dockerfile
echo ENV JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m" >> blog-admin\Dockerfile
echo ENV TZ=Asia/Shanghai >> blog-admin\Dockerfile
echo # Create logs directory and set permissions >> blog-admin\Dockerfile
echo RUN mkdir -p /app/logs ^&^& ^\ >> blog-admin\Dockerfile
echo     chmod -R 777 /app/logs >> blog-admin\Dockerfile
echo # Copy JAR file to container >> blog-admin\Dockerfile
echo COPY blog-admin.jar /app/blog-admin.jar >> blog-admin\Dockerfile
echo # Expose port >> blog-admin\Dockerfile
echo EXPOSE 8080 >> blog-admin\Dockerfile
echo # Set container startup command >> blog-admin\Dockerfile
echo ENTRYPOINT ["sh", "-c", "java ${JAVA_OPTS} -jar /app/blog-admin.jar --spring.profiles.active=druid"] >> blog-admin\Dockerfile

echo [STEP 3] Creating docker-compose.yml file...

echo version: '3' > docker-compose.yml
echo. >> docker-compose.yml
echo services: >> docker-compose.yml
echo   # Application service >> docker-compose.yml
echo   blog-admin: >> docker-compose.yml
echo     image: blog-admin:latest >> docker-compose.yml
echo     container_name: blog-admin >> docker-compose.yml
echo     restart: always >> docker-compose.yml
echo     ports: >> docker-compose.yml
echo       - "8080:8080" >> docker-compose.yml
echo     environment: >> docker-compose.yml
echo       # Database connection configuration - using external database >> docker-compose.yml
echo       - SPRING_DATASOURCE_DRUID_MASTER_URL=************************************************************************************************************************************************** >> docker-compose.yml
echo       - SPRING_DATASOURCE_DRUID_MASTER_USERNAME=root >> docker-compose.yml
echo       - SPRING_DATASOURCE_DRUID_MASTER_PASSWORD=123456 >> docker-compose.yml
echo       # Redis configuration - using external Redis >> docker-compose.yml
echo       - SPRING_REDIS_HOST=localhost >> docker-compose.yml
echo       - SPRING_REDIS_PORT=6379 >> docker-compose.yml
echo       - SPRING_REDIS_DATABASE=0 >> docker-compose.yml
echo       # Log configuration >> docker-compose.yml
echo       - LOGGING_FILE_PATH=/app/logs >> docker-compose.yml
echo       - LOGGING_LEVEL_COM_BLOG=debug >> docker-compose.yml
echo     volumes: >> docker-compose.yml
echo       - /data/docker/server/blog-admin/logs:/app/logs >> docker-compose.yml
echo       - /data/docker/server/blog-admin/uploadPath:/app/uploadPath >> docker-compose.yml
echo     network_mode: "host" >> docker-compose.yml

echo [STEP 4] Transferring files to remote server...

:: Create and execute the script directly in WSL
wsl bash -c "echo '#!/bin/bash' > ~/scp_transfer.sh && echo 'set -e' >> ~/scp_transfer.sh && echo 'echo \"Creating directory on remote server...\"' >> ~/scp_transfer.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"mkdir -p /data/docker/server/blog-admin\"' >> ~/scp_transfer.sh && echo 'echo \"Copying files to remote server...\"' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no %CD:\=/%/blog-admin/target/blog-admin.jar root@*************:/data/docker/server/blog-admin/' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no %CD:\=/%/blog-admin/Dockerfile root@*************:/data/docker/server/blog-admin/' >> ~/scp_transfer.sh && echo 'scp -o StrictHostKeyChecking=no %CD:\=/%/docker-compose.yml root@*************:/data/docker/server/' >> ~/scp_transfer.sh && echo 'echo \"Building Docker image on remote server...\"' >> ~/scp_transfer.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"cd /data/docker/server/blog-admin && docker build -t blog-admin:latest .\"' >> ~/scp_transfer.sh && echo 'echo \"SCP transfer and remote build completed successfully!\"' >> ~/scp_transfer.sh && chmod +x ~/scp_transfer.sh && SSHPASS=e487h2IF sshpass -e ~/scp_transfer.sh"

echo [STEP 5] Completed.
echo Files have been successfully transferred to the remote server and Docker image has been built.
echo You can use the following command to start the container on the remote server:
echo ssh root@************* "cd /data/docker/server && docker-compose up -d"
echo.
echo Or use the following command to view logs on the remote server:
echo ssh root@************* "docker logs -f blog-admin"
echo.

:end
pause
