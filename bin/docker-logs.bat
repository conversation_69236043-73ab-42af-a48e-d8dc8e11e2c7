@echo off
chcp 65001
echo.
echo [INFO] Viewing Docker container logs on remote server.
echo.

echo [STEP 1] Connecting to remote server to view logs...

:: Create and execute the script directly in WSL
wsl bash -c "echo '#!/bin/bash' > ~/remote_logs.sh && echo 'set -e' >> ~/remote_logs.sh && echo 'echo \"Connecting to remote server to view logs...\"' >> ~/remote_logs.sh && echo 'ssh -o StrictHostKeyChecking=no root@************* \"docker logs -f blog-admin\"' >> ~/remote_logs.sh && chmod +x ~/remote_logs.sh && SSHPASS=e487h2IF sshpass -e ~/remote_logs.sh"

echo [STEP 2] Completed.
echo.

pause
