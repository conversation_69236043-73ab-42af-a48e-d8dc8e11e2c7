@echo off
chcp 65001
echo.
echo [INFO] Installing necessary tools in WSL.
echo.

echo [STEP 1] Installing sshpass in WSL...

:: Create and execute the script directly in WSL
wsl bash -c "echo '#!/bin/bash' > ~/setup_wsl.sh && echo 'set -e' >> ~/setup_wsl.sh && echo 'echo \"Updating package lists...\"' >> ~/setup_wsl.sh && echo 'sudo apt-get update' >> ~/setup_wsl.sh && echo 'echo \"Installing sshpass...\"' >> ~/setup_wsl.sh && echo 'sudo apt-get install -y sshpass' >> ~/setup_wsl.sh && echo 'echo \"Installation completed!\"' >> ~/setup_wsl.sh && chmod +x ~/setup_wsl.sh && ~/setup_wsl.sh"

echo [STEP 2] Completed.
echo Necessary tools have been installed in WSL.
echo.

pause
