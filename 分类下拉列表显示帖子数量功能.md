# 分类下拉列表显示帖子数量功能

## 需求描述

在首页的分类下拉列表中，当分类的帖子数量不为0时，在分类名称后面显示帖子数量。

## 数据结构

API返回的分类数据结构：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": [
    {
      "categoryId": 1,
      "categoryName": "Open Source Hardware",
      "sort": 1,
      "status": "0",
      "langId": 2,
      "postCount": 8
    },
    {
      "categoryId": 2,
      "categoryName": "Integrated Devices", 
      "sort": 2,
      "status": "0",
      "langId": 2,
      "postCount": 0
    }
  ]
}
```

## 实现方案

### 修改内容

在 `block/src/views/home/<USER>

**修改前**：
```vue
<el-select size="large" @change="fetchPosts()" style="width: 150px;" v-model="params.categoryId"
  :placeholder="$t('selectCategory')" clearable>
  <el-option v-for="item in categories" :key="item.categoryId" :label="item.categoryName"
    :value="item.categoryId" />
</el-select>
```

**修改后**：
```vue
<el-select size="large" @change="fetchPosts()" style="width: 200px;" v-model="params.categoryId"
  :placeholder="$t('selectCategory')" clearable>
  <el-option v-for="item in categories" :key="item.categoryId"
    :label="item.postCount > 0 ? `${item.categoryName} (${item.postCount})` : item.categoryName"
    :value="item.categoryId" />
</el-select>
```

### 关键修改点

1. **条件显示逻辑**：
   ```javascript
   item.postCount > 0 ? `${item.categoryName} (${item.postCount})` : item.categoryName
   ```
   - 当 `postCount > 0` 时：显示 `分类名称 (数量)`
   - 当 `postCount = 0` 时：只显示 `分类名称`

2. **下拉框宽度调整**：
   - 从 `width: 150px` 增加到 `width: 200px`
   - 为了容纳显示帖子数量后更长的文本

## 显示效果

### 有帖子的分类
```
Open Source Hardware (8)
```

### 没有帖子的分类
```
Integrated Devices
```

## 功能特点

### 1. 智能显示
- **有帖子**：显示分类名称和帖子数量
- **无帖子**：只显示分类名称，保持界面简洁

### 2. 用户体验优化
- **信息丰富**：用户可以直观看到每个分类的活跃度
- **选择指导**：帮助用户选择有内容的分类
- **界面简洁**：空分类不显示数量，避免视觉干扰

### 3. 响应式设计
- 增加了下拉框宽度以适应更长的文本
- 保持了原有的样式和交互逻辑

## 技术实现

### Vue模板语法
使用三元运算符进行条件渲染：
```vue
:label="item.postCount > 0 ? `${item.categoryName} (${item.postCount})` : item.categoryName"
```

### 数据绑定
- `item.categoryName`：分类名称
- `item.postCount`：帖子数量
- `item.categoryId`：分类ID（用于value绑定）

## 相关文件

- `block/src/views/home/<USER>
- API接口：`/api/post/category/list` - 获取分类列表

## 测试验证

### 测试用例

1. **有帖子的分类**：
   - 验证显示格式：`分类名称 (数量)`
   - 验证数量正确性

2. **无帖子的分类**：
   - 验证只显示分类名称
   - 验证不显示 `(0)`

3. **下拉框功能**：
   - 验证选择功能正常
   - 验证清除功能正常
   - 验证筛选功能正常

### 预期结果

- ✅ 有帖子的分类显示数量
- ✅ 无帖子的分类不显示数量
- ✅ 下拉框宽度适应内容
- ✅ 选择和筛选功能正常

## 扩展功能

### 可能的优化

1. **样式优化**：
   ```vue
   <el-option v-for="item in categories" :key="item.categoryId" :value="item.categoryId">
     <span>{{ item.categoryName }}</span>
     <span v-if="item.postCount > 0" style="color: #999; font-size: 12px;">
       ({{ item.postCount }})
     </span>
   </el-option>
   ```

2. **国际化支持**：
   ```vue
   :label="item.postCount > 0 ? `${item.categoryName} (${item.postCount} ${$t('posts')})` : item.categoryName"
   ```

3. **数量格式化**：
   ```javascript
   const formatCount = (count) => {
     if (count >= 1000) {
       return (count / 1000).toFixed(1) + 'k'
     }
     return count.toString()
   }
   ```

## 注意事项

1. **数据一致性**：确保API返回的postCount准确
2. **性能考虑**：分类列表通常不会很长，性能影响可忽略
3. **用户体验**：避免显示过长的数字，必要时可以格式化

现在分类下拉列表已经支持显示帖子数量，用户可以更直观地了解每个分类的活跃度。
