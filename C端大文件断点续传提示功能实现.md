# C端大文件断点续传提示功能实现

## 需求描述

在C端的大文件上传功能中，当检测到之前已有分片上传成功时，需要在进度条上显示断点续传的提示信息：

1. **检测提示**：`🔄 检测到已上传的分片，正在恢复上传进度...`
2. **恢复提示**：`✅ 已恢复上传进度，继续上传剩余分片...`

## 文件位置

- **主要页面**：`block/src/views/create-post/index.vue`
- **工具函数**：`block/src/utils/upload-helpers.js`

## 实现方案

### 1. 修改upload-helpers.js工具函数

#### 添加消息回调参数
```javascript
/**
 * 分片上传文件
 * @param {File} file 文件对象
 * @param {string} bizType 业务类型
 * @param {Function} progressCallback 进度回调函数
 * @param {Function} messageCallback 消息回调函数
 * @returns {Promise<string>} 文件URL
 */
export async function uploadFileInChunks(file, bizType, progressCallback, messageCallback) {
```

#### 添加断点续传提示逻辑
```javascript
// 如果有已上传的分片，显示断点续传提示
if (uploadedChunks.length > 0) {
  const resumeProgress = Math.round((uploadedChunks.length / totalChunks) * 100)
  console.log(`🔄 检测到已上传的分片，正在恢复上传进度... (${uploadedChunks.length}/${totalChunks}) - ${resumeProgress}%`)

  // 显示断点续传检测提示
  if (messageCallback) {
    messageCallback('🔄 检测到已上传的分片，正在恢复上传进度...')
  }

  if (progressCallback) {
    progressCallback(resumeProgress)
  }

  // 延迟2秒让用户看到提示
  await new Promise(resolve => setTimeout(resolve, 2000))

  console.log(`✅ 已恢复上传进度，继续上传剩余分片... (${uploadedChunks.length}/${totalChunks})`)

  // 显示继续上传提示
  if (messageCallback) {
    messageCallback('✅ 已恢复上传进度，继续上传剩余分片...')
  }

  // 再延迟1秒
  await new Promise(resolve => setTimeout(resolve, 1000))
} else {
  console.log('🆕 没有已上传的分片，开始全新上传')
  if (messageCallback) {
    messageCallback('正在上传文件...')
  }
}
```

### 2. 修改create-post页面

#### 图片上传添加消息回调
```javascript
const url = await uploadFileInChunks(file, 'forum', (progress) => {
  progressInner.style.width = `${progress}%`
  if (progress > 0 && progress < 100) {
    progressText.textContent = `正在上传图片: ${progress}%`
  } else if (progress === 100) {
    progressText.textContent = `上传完成: ${progress}%`
  } else {
    progressText.textContent = `准备上传图片...`
  }
}, (message) => {
  // 消息回调，显示断点续传提示
  progressText.textContent = message
})
```

#### 文件上传添加消息回调
```javascript
const url = await uploadFileInChunks(file, 'forum', (progress) => {
  progressInner.style.width = `${progress}%`
  if (progress > 0 && progress < 100) {
    progressText.textContent = `正在上传文件: ${progress}%`
  } else if (progress === 100) {
    progressText.textContent = `上传完成: ${progress}%`
  } else {
    progressText.textContent = `准备上传文件...`
  }
}, (message) => {
  // 消息回调，显示断点续传提示
  progressText.textContent = message
})
```

## 功能特点

### 1. 智能检测
- **自动检测**：检查服务器上已上传的分片
- **进度恢复**：根据已上传分片计算当前进度
- **状态显示**：实时显示断点续传状态

### 2. 用户体验优化
- **清晰提示**：明确告知用户正在进行断点续传
- **进度可视化**：进度条显示恢复的上传进度
- **时间控制**：适当的延迟让用户看清提示信息

### 3. 双重回调机制
- **进度回调**：更新进度条的百分比
- **消息回调**：更新进度条上的文字提示

## 显示效果

### 断点续传流程
1. **检测阶段**：
   ```
   🔄 检测到已上传的分片，正在恢复上传进度...
   ```
   - 进度条显示已恢复的进度百分比
   - 持续显示2秒

2. **恢复阶段**：
   ```
   ✅ 已恢复上传进度，继续上传剩余分片...
   ```
   - 提示用户即将继续上传
   - 持续显示1秒

3. **继续上传**：
   ```
   正在上传文件: 45%
   ```
   - 恢复正常的进度显示
   - 继续上传剩余分片

### 全新上传流程
1. **开始上传**：
   ```
   正在上传文件...
   ```
   - 直接开始上传，无断点续传提示

## 技术实现

### 1. 回调函数设计
```javascript
// 进度回调：更新进度条
progressCallback(progress) // progress: 0-100

// 消息回调：更新提示文字
messageCallback(message) // message: 字符串
```

### 2. 时间控制
```javascript
// 检测提示显示2秒
await new Promise(resolve => setTimeout(resolve, 2000))

// 恢复提示显示1秒
await new Promise(resolve => setTimeout(resolve, 1000))
```

### 3. 进度计算
```javascript
// 根据已上传分片计算恢复进度
const resumeProgress = Math.round((uploadedChunks.length / totalChunks) * 100)
```

## 兼容性说明

### 1. 向后兼容
- 新增的`messageCallback`参数是可选的
- 如果不传递该参数，功能仍然正常工作
- 只是不会显示断点续传提示

### 2. 错误处理
- 如果消息回调函数出错，不会影响上传功能
- 进度回调和消息回调相互独立

## 测试验证

### 测试场景

1. **断点续传测试**：
   - 上传大文件到一半时中断
   - 重新上传同一文件
   - 验证显示断点续传提示

2. **全新上传测试**：
   - 上传新的大文件
   - 验证不显示断点续传提示

3. **进度显示测试**：
   - 验证进度条正确显示恢复的进度
   - 验证提示文字正确更新

### 预期结果

- ✅ 断点续传时显示检测和恢复提示
- ✅ 进度条正确显示恢复的进度
- ✅ 全新上传时不显示断点续传提示
- ✅ 上传功能完全正常

## 相关文件

- **主要页面**：`block/src/views/create-post/index.vue`
- **工具函数**：`block/src/utils/upload-helpers.js`
- **后端接口**：
  - `/api/oss/chunk/check` - 检查分片状态
  - `/api/oss/chunk/upload` - 上传分片
  - `/api/oss/chunk/merge` - 合并分片

## 优化建议

### 1. 国际化支持
```javascript
// 可以根据语言设置显示不同的提示
const messages = {
  zh: {
    detecting: '🔄 检测到已上传的分片，正在恢复上传进度...',
    resuming: '✅ 已恢复上传进度，继续上传剩余分片...',
    uploading: '正在上传文件...'
  },
  en: {
    detecting: '🔄 Detected uploaded chunks, resuming upload progress...',
    resuming: '✅ Upload progress resumed, continuing with remaining chunks...',
    uploading: 'Uploading file...'
  }
}
```

### 2. 进度动画
```javascript
// 可以添加进度条的动画效果
progressInner.style.transition = 'width 0.5s ease-in-out'
```

### 3. 错误重试
```javascript
// 可以添加断点续传失败时的重试机制
if (uploadedChunks.length > 0 && retryCount < maxRetries) {
  // 重试逻辑
}
```

现在C端的大文件上传已经支持断点续传提示功能，用户可以清楚地看到上传进度的恢复过程。
