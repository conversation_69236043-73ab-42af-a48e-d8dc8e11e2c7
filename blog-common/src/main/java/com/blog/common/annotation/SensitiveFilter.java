package com.blog.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 敏感词过滤注解
 * 
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveFilter {
    /**
     * 是否启用敏感词过滤，默认为true
     */
    boolean enabled() default true;
}
