package com.blog.common.utils.file;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件类型工具类
 *
 * <AUTHOR>
 */
public class FileTypeUtils
{
    private static final Logger log = LoggerFactory.getLogger(FileTypeUtils.class);

    // 文件类型魔数映射
    private static final Map<String, String> FILE_TYPE_MAP = new HashMap<String, String>();

    static {
        // 图片文件
        FILE_TYPE_MAP.put("FFD8FF", "jpg");             // JPEG
        FILE_TYPE_MAP.put("89504E47", "png");           // PNG
        FILE_TYPE_MAP.put("47494638", "gif");           // GIF
        FILE_TYPE_MAP.put("424D", "bmp");               // BMP
        FILE_TYPE_MAP.put("49492A00", "tif");           // TIFF
        FILE_TYPE_MAP.put("38425053", "psd");           // PSD
        FILE_TYPE_MAP.put("41564931", "avi");           // AVI

        // 压缩文件
        FILE_TYPE_MAP.put("504B0304", "zip");           // ZIP
        FILE_TYPE_MAP.put("52617221", "rar");           // RAR
        FILE_TYPE_MAP.put("1F8B08", "gz");              // GZ
        FILE_TYPE_MAP.put("377ABCAF271C", "7z");        // 7Z
        FILE_TYPE_MAP.put("425A68", "bz2");             // BZ2

        // 办公文档
        FILE_TYPE_MAP.put("D0CF11E0", "doc");           // MS Word/Excel
        FILE_TYPE_MAP.put("504B0304", "docx");          // MS Word/Excel 2007+
        FILE_TYPE_MAP.put("25504446", "pdf");           // PDF

        // 音频文件
        FILE_TYPE_MAP.put("49443303", "mp3");           // MP3
        FILE_TYPE_MAP.put("57415645", "wav");           // WAV
        FILE_TYPE_MAP.put("664C6143", "flac");          // FLAC

        // 视频文件
        FILE_TYPE_MAP.put("00000020667479", "mp4");     // MP4
        FILE_TYPE_MAP.put("000001BA", "mpg");           // MPEG
        FILE_TYPE_MAP.put("000001B3", "mpg");           // MPEG
        FILE_TYPE_MAP.put("6674797069736F", "mp4");     // ISO MP4
        FILE_TYPE_MAP.put("1A45DFA3", "mkv");           // MKV
    }
    /**
     * 获取文件类型
     * <p>
     * 例如: blog.txt, 返回: txt
     *
     * @param file 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(File file)
    {
        if (null == file)
        {
            return StringUtils.EMPTY;
        }
        return getFileType(file.getName());
    }

    /**
     * 获取文件类型
     * <p>
     * 例如: blog.txt, 返回: txt
     *
     * @param fileName 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(String fileName)
    {
        int separatorIndex = fileName.lastIndexOf(".");
        if (separatorIndex < 0)
        {
            return "";
        }
        return fileName.substring(separatorIndex + 1).toLowerCase();
    }

    /**
     * 获取文件类型
     *
     * @param photoByte 文件字节码
     * @return 后缀（不含".")
     */
    public static String getFileExtendName(byte[] photoByte)
    {
        // 先尝试使用魔数获取真实文件类型
        String fileType = getRealFileType(photoByte);
        if (StringUtils.isNotEmpty(fileType)) {
            return fileType.toUpperCase();
        }

        // 如果魔数方法无法识别，则使用原来的方法
        String strFileExtendName = "JPG";
        if ((photoByte[0] == 71) && (photoByte[1] == 73) && (photoByte[2] == 70) && (photoByte[3] == 56)
                && ((photoByte[4] == 55) || (photoByte[4] == 57)) && (photoByte[5] == 97))
        {
            strFileExtendName = "GIF";
        }
        else if ((photoByte[6] == 74) && (photoByte[7] == 70) && (photoByte[8] == 73) && (photoByte[9] == 70))
        {
            strFileExtendName = "JPG";
        }
        else if ((photoByte[0] == 66) && (photoByte[1] == 77))
        {
            strFileExtendName = "BMP";
        }
        else if ((photoByte[1] == 80) && (photoByte[2] == 78) && (photoByte[3] == 71))
        {
            strFileExtendName = "PNG";
        }
        return strFileExtendName;
    }

    /**
     * 获取文件真实类型
     * 基于文件头部的魔数识别文件类型
     *
     * @param file 上传的文件
     * @return 文件类型（小写字母，不含点）
     */
    public static String getRealFileType(MultipartFile file) {
        try {
            return getRealFileType(file.getInputStream());
        } catch (IOException e) {
            log.error("获取文件真实类型失败", e);
            // 如果无法识别，则返回文件名中的后缀
            return getFileType(file.getOriginalFilename());
        }
    }

    /**
     * 获取文件真实类型
     * 基于文件头部的魔数识别文件类型
     *
     * @param inputStream 文件输入流
     * @return 文件类型（小写字母，不含点）
     */
    public static String getRealFileType(InputStream inputStream) {
        try {
            // 读取文件头部字节
            byte[] fileHeader = new byte[28];
            inputStream.read(fileHeader, 0, 28);
            inputStream.close();

            // 将文件头部字节转换为十六进制字符串
            String hexHeader = bytesToHexString(fileHeader);

            // 匹配文件类型
            if (StringUtils.isNotEmpty(hexHeader)) {
                for (Map.Entry<String, String> entry : FILE_TYPE_MAP.entrySet()) {
                    String magicNumber = entry.getKey();
                    if (hexHeader.startsWith(magicNumber)) {
                        return entry.getValue();
                    }
                }
            }
        } catch (IOException e) {
            log.error("获取文件真实类型失败", e);
        }
        return null;
    }

    /**
     * 获取文件真实类型
     * 基于文件头部的魔数识别文件类型
     *
     * @param fileBytes 文件字节数组
     * @return 文件类型（小写字母，不含点）
     */
    public static String getRealFileType(byte[] fileBytes) {
        if (fileBytes == null || fileBytes.length < 28) {
            return null;
        }

        // 取文件头部字节
        byte[] fileHeader = new byte[28];
        System.arraycopy(fileBytes, 0, fileHeader, 0, 28);

        // 将文件头部字节转换为十六进制字符串
        String hexHeader = bytesToHexString(fileHeader);

        // 匹配文件类型
        if (StringUtils.isNotEmpty(hexHeader)) {
            for (Map.Entry<String, String> entry : FILE_TYPE_MAP.entrySet()) {
                String magicNumber = entry.getKey();
                if (hexHeader.startsWith(magicNumber)) {
                    return entry.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHexString(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        for (byte b : bytes) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
}
