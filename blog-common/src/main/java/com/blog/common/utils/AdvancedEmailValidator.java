package com.blog.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 高级邮箱验证工具类
 * 提供更可靠的邮箱验证方法，不需要发送验证邮件
 */
public class AdvancedEmailValidator {
    private static final Logger log = LoggerFactory.getLogger(AdvancedEmailValidator.class);

    // 邮箱正则表达式 - 基本语法验证
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    // 常见的邮箱服务商域名
    private static final Set<String> COMMON_EMAIL_DOMAINS = new HashSet<>();

    // 一次性/临时邮箱域名列表
    private static final Set<String> DISPOSABLE_EMAIL_DOMAINS = new HashSet<>();

    // 角色邮箱前缀列表
    private static final Set<String> ROLE_ACCOUNTS = new HashSet<>();

    // 邮箱服务商特定规则
    private static final Map<String, EmailRule> EMAIL_RULES = new HashMap<>();

    static {
        // 初始化常见邮箱服务商域名
        COMMON_EMAIL_DOMAINS.addAll(Arrays.asList(
                "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "live.com", "msn.com",
                "qq.com", "163.com", "126.com", "yeah.net", "foxmail.com", "sina.com", "sohu.com",
                "aliyun.com", "139.com", "189.com", "wo.cn", "icloud.com", "me.com", "mac.com",
                "mail.com", "gmx.com", "protonmail.com", "zoho.com", "yandex.com", "aol.com"
        ));

        // 初始化一次性/临时邮箱域名列表
        DISPOSABLE_EMAIL_DOMAINS.addAll(Arrays.asList(
                "mailinator.com", "guerrillamail.com", "temp-mail.org", "fakeinbox.com", "tempmail.com",
                "10minutemail.com", "yopmail.com", "throwawaymail.com", "sharklasers.com", "getairmail.com",
                "getnada.com", "mailnesia.com", "mailcatch.com", "dispostable.com", "maildrop.cc",
                "harakirimail.com", "trashmail.com", "temp-mail.ru", "tempail.com", "tempr.email",
                "fakemailgenerator.com", "emailondeck.com", "tempmailo.com", "spamgourmet.com", "mytemp.email"
        ));

        // 初始化角色邮箱前缀列表
        ROLE_ACCOUNTS.addAll(Arrays.asList(
                "admin", "administrator", "webmaster", "hostmaster", "postmaster", "info", "support",
                "sales", "marketing", "contact", "help", "abuse", "noc", "security", "root", "team",
                "service", "mail", "nobody", "noreply", "no-reply", "hr", "jobs", "webadmin", "sysadmin",
                "system", "newsletter", "office", "billing", "enquiries", "feedback", "test", "dev"
        ));

        // 初始化邮箱服务商特定规则
        // QQ邮箱规则：用户名为5-11位数字
        EMAIL_RULES.put("qq.com", email -> {
            String username = email.substring(0, email.indexOf('@'));
            return username.matches("\\d+") && username.length() >= 5 && username.length() <= 11;
        });

        // Gmail规则：用户名至少6个字符，只能包含字母、数字和点
        EMAIL_RULES.put("gmail.com", email -> {
            String username = email.substring(0, email.indexOf('@'));
            return username.matches("[a-zA-Z0-9.]+") && username.length() >= 6 && !username.contains("..");
        });

        // 163/126邮箱规则：用户名至少5个字符
        EmailRule netease = email -> {
            String username = email.substring(0, email.indexOf('@'));
            return username.length() >= 5;
        };
        EMAIL_RULES.put("163.com", netease);
        EMAIL_RULES.put("126.com", netease);
    }

    /**
     * 邮箱规则接口
     */
    @FunctionalInterface
    private interface EmailRule {
        boolean isValid(String email);
    }

    /**
     * 验证邮箱格式是否正确
     * @param email 邮箱地址
     * @return 格式是否正确
     */
    public static boolean isValidFormat(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证邮箱域名是否存在
     * @param email 邮箱地址
     * @return 域名是否存在
     */
    public static boolean isDomainExists(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();

        // 检查是否是常见邮箱服务商域名
        if (COMMON_EMAIL_DOMAINS.contains(domain)) {
            return true;
        }

        try {
            // 查询域名的MX记录
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);
            Attributes attributes = dirContext.getAttributes(domain, new String[]{"MX"});
            Attribute attribute = attributes.get("MX");

            return attribute != null && attribute.size() > 0;
        } catch (NamingException e) {
            log.warn("验证邮箱域名失败: {}", email, e);
            return false;
        }
    }

    /**
     * 检查是否是一次性/临时邮箱
     * @param email 邮箱地址
     * @return 是否是一次性/临时邮箱
     */
    public static boolean isDisposableEmail(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();
        return DISPOSABLE_EMAIL_DOMAINS.contains(domain);
    }

    /**
     * 检查是否是角色邮箱
     * @param email 邮箱地址
     * @return 是否是角色邮箱
     */
    public static boolean isRoleAccount(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String username = email.substring(0, email.indexOf('@')).toLowerCase();
        return ROLE_ACCOUNTS.contains(username);
    }

    /**
     * 应用邮箱服务商特定规则
     * @param email 邮箱地址
     * @return 是否符合特定规则
     */
    public static boolean applyDomainSpecificRules(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();
        EmailRule rule = EMAIL_RULES.get(domain);

        // 如果没有特定规则，默认返回true
        if (rule == null) {
            return true;
        }

        return rule.isValid(email);
    }

    /**
     * 验证邮箱服务器是否可连接
     * @param email 邮箱地址
     * @return 服务器是否可连接
     */
    public static boolean isServerReachable(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();

        try {
            // 查询域名的MX记录
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);
            Attributes attributes = dirContext.getAttributes(domain, new String[]{"MX"});
            Attribute attribute = attributes.get("MX");

            if (attribute == null || attribute.size() == 0) {
                return false;
            }

            // 获取MX记录中的邮件服务器地址
            String record = attribute.get(0).toString();
            String[] parts = record.split("\\s+");
            String mailServer = parts[parts.length - 1];

            // 去掉末尾的点
            if (mailServer.endsWith(".")) {
                mailServer = mailServer.substring(0, mailServer.length() - 1);
            }

            // 尝试连接邮件服务器的25端口
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(mailServer, 25), 5000);
                return true;
            }
        } catch (Exception e) {
            log.warn("验证邮箱服务器连接失败: {}", email, e);
            // 如果是常见邮箱服务商，我们假设服务器是可达的
            return COMMON_EMAIL_DOMAINS.contains(domain);
        }

    }

    /**
     * 验证邮箱地址是否存在（使用SMTP验证）
     * @param email 邮箱地址
     * @return 邮箱地址是否存在
     */
    public static boolean isEmailExists(String email) {
        if (!isValidFormat(email) || !isDomainExists(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1).toLowerCase();

        // 对于常见邮箱服务商，应用特定规则
        if (COMMON_EMAIL_DOMAINS.contains(domain)) {
            if (!applyDomainSpecificRules(email)) {
                return false;
            }
            
            // 对于常见邮箱服务商，我们不进行SMTP验证，因为很多服务商会阻止这种验证
            // 而是依赖于特定规则验证
            return true;
        }

        try {
            // 查询域名的MX记录
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);
            Attributes attributes = dirContext.getAttributes(domain, new String[]{"MX"});
            Attribute attribute = attributes.get("MX");

            if (attribute == null || attribute.size() == 0) {
                return false;
            }

            // 获取MX记录中的邮件服务器地址
            String record = attribute.get(0).toString();
            String[] parts = record.split("\\s+");
            String mailServer = parts[parts.length - 1];

            // 去掉末尾的点
            if (mailServer.endsWith(".")) {
                mailServer = mailServer.substring(0, mailServer.length() - 1);
            }

            // 与邮件服务器建立连接
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(mailServer, 25), 5000);

                // 创建输入输出流
                BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);

                // 读取服务器的初始响应
                String response = reader.readLine();
                if (response == null || !response.startsWith("220")) {
                    return false;
                }

                // 发送HELO命令
                writer.println("HELO example.com");
                response = reader.readLine();
                if (response == null || !response.startsWith("250")) {
                    return false;
                }

                // 发送MAIL FROM命令
                writer.println("MAIL FROM: <<EMAIL>>");
                response = reader.readLine();
                if (response == null || !response.startsWith("250")) {
                    return false;
                }

                // 发送RCPT TO命令，验证邮箱地址是否存在
                writer.println("RCPT TO: <" + email + ">");
                response = reader.readLine();

                // 发送QUIT命令
                writer.println("QUIT");

                // 如果响应以250开头，表示邮箱地址存在
                return response != null && response.startsWith("250");
            } finally {
                socket.close();
            }
        } catch (IOException | NamingException e) {
            log.warn("验证邮箱地址失败: {}", email, e);
            // 如果发生异常，我们默认认为邮箱域名存在，但邮箱地址可能不存在
            return false;
        }
    }

    /**
     * 综合验证邮箱是否有效
     * @param email 邮箱地址
     * @return 邮箱是否有效
     */
    public static boolean isValidEmail(String email) {
        // 1. 验证格式
        if (!isValidFormat(email)) {
            log.info("邮箱格式不正确: {}", email);
            return false;
        }

        // 2. 检查是否是一次性/临时邮箱
        if (isDisposableEmail(email)) {
            log.info("一次性/临时邮箱不允许: {}", email);
            return false;
        }

        // 3. 检查是否是角色邮箱
        if (isRoleAccount(email)) {
            log.info("角色邮箱不允许: {}", email);
            return false;
        }

        // 4. 验证域名是否存在
        if (!isDomainExists(email)) {
            log.info("邮箱域名不存在: {}", email);
            return false;
        }

        // 5. 验证邮箱服务器是否可连接
        if (!isServerReachable(email)) {
            log.info("邮箱服务器不可连接: {}", email);
            return false;
        }

        // 6. 应用邮箱服务商特定规则
        if (!applyDomainSpecificRules(email)) {
            log.info("邮箱不符合服务商特定规则: {}", email);
            return false;
        }

        // 7. 验证邮箱地址是否存在
        if (!isEmailExists(email)) {
            log.info("邮箱地址不存在: {}", email);
            return false;
        }

        return true;
    }

    /**
     * 添加自定义邮箱规则
     * @param domain 域名
     * @param rule 规则
     */
    public static void addEmailRule(String domain, EmailRule rule) {
        EMAIL_RULES.put(domain.toLowerCase(), rule);
    }

    /**
     * 添加常见邮箱服务商域名
     * @param domain 域名
     */
    public static void addCommonEmailDomain(String domain) {
        COMMON_EMAIL_DOMAINS.add(domain.toLowerCase());
    }

    /**
     * 添加一次性/临时邮箱域名
     * @param domain 域名
     */
    public static void addDisposableEmailDomain(String domain) {
        DISPOSABLE_EMAIL_DOMAINS.add(domain.toLowerCase());
    }

    /**
     * 添加角色邮箱前缀
     * @param prefix 前缀
     */
    public static void addRoleAccount(String prefix) {
        ROLE_ACCOUNTS.add(prefix.toLowerCase());
    }
}
