package com.blog.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 邮箱验证工具类
 * 用于验证邮箱是否存在且有效
 */
public class EmailValidator {
    private static final Logger log = LoggerFactory.getLogger(EmailValidator.class);

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    // 常见的邮箱服务商域名
    private static final List<String> COMMON_EMAIL_DOMAINS = new ArrayList<>();

    static {
        // 初始化常见邮箱服务商域名
        COMMON_EMAIL_DOMAINS.add("gmail.com");
        COMMON_EMAIL_DOMAINS.add("yahoo.com");
        COMMON_EMAIL_DOMAINS.add("hotmail.com");
        COMMON_EMAIL_DOMAINS.add("outlook.com");
        COMMON_EMAIL_DOMAINS.add("qq.com");
        COMMON_EMAIL_DOMAINS.add("163.com");
        COMMON_EMAIL_DOMAINS.add("126.com");
        COMMON_EMAIL_DOMAINS.add("sina.com");
        COMMON_EMAIL_DOMAINS.add("sohu.com");
        COMMON_EMAIL_DOMAINS.add("foxmail.com");
    }

    /**
     * 验证邮箱格式是否正确
     * @param email 邮箱地址
     * @return 格式是否正确
     */
    public static boolean isValidFormat(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证邮箱域名是否存在
     * @param email 邮箱地址
     * @return 域名是否存在
     */
    public static boolean isDomainExists(String email) {
        if (!isValidFormat(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1);
        try {
            // 查询域名的MX记录
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);
            Attributes attributes = dirContext.getAttributes(domain, new String[]{"MX"});
            Attribute attribute = attributes.get("MX");

            return attribute != null && attribute.size() > 0;
        } catch (NamingException e) {
            log.warn("验证邮箱域名失败: {}", email, e);
            return false;
        }
    }

    /**
     * 验证邮箱地址是否存在（使用SMTP验证）
     * @param email 邮箱地址
     * @return 邮箱地址是否存在
     */
    public static boolean isEmailExists(String email) {
        if (!isDomainExists(email)) {
            return false;
        }

        String domain = email.substring(email.indexOf('@') + 1);
        String username = email.substring(0, email.indexOf('@'));

        // 对于常见邮箱服务商，进行额外验证
        if (COMMON_EMAIL_DOMAINS.contains(domain.toLowerCase())) {
            // 对于常见邮箱服务商，验证用户名长度
            if (username.length() < 3) {
                return false; // 用户名太短，可能不合法
            }

            // 对于QQ邮箱，验证用户名是否为数字
            if ("qq.com".equals(domain.toLowerCase())) {
                return username.matches("\\d+") && username.length() >= 5 && username.length() <= 11;
            }
        }

        try {
            // 查询域名的MX记录
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);
            Attributes attributes = dirContext.getAttributes(domain, new String[]{"MX"});
            Attribute attribute = attributes.get("MX");

            if (attribute == null || attribute.size() == 0) {
                return false;
            }

            // 获取MX记录中的邮件服务器地址
            String record = attribute.get(0).toString();
            String[] parts = record.split("\\s+");
            String mailServer = parts[parts.length - 1];

            // 去掉末尾的点
            if (mailServer.endsWith(".")) {
                mailServer = mailServer.substring(0, mailServer.length() - 1);
            }

            // 与邮件服务器建立连接
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(mailServer, 25), 5000);

                // 创建输入输出流
                BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);

                // 读取服务器的初始响应
                String response = reader.readLine();
                if (response == null || !response.startsWith("220")) {
                    return false;
                }

                // 发送HELO命令
                writer.println("HELO example.com");
                response = reader.readLine();
                if (response == null || !response.startsWith("250")) {
                    return false;
                }

                // 发送MAIL FROM命令
                writer.println("MAIL FROM: <<EMAIL>>");
                response = reader.readLine();
                if (response == null || !response.startsWith("250")) {
                    return false;
                }

                // 发送RCPT TO命令，验证邮箱地址是否存在
                writer.println("RCPT TO: <" + email + ">");
                response = reader.readLine();

                // 发送QUIT命令
                writer.println("QUIT");

                // 如果响应以250开头，表示邮箱地址存在
                return response != null && response.startsWith("250");
            } finally {
                socket.close();
            }
        } catch (IOException | NamingException e) {
            log.warn("验证邮箱地址失败: {}", email, e);
            // 如果发生异常，我们默认认为邮箱域名存在，但邮箱地址可能不存在
            // 对于常见邮箱服务商，我们假设邮箱地址存在
            return COMMON_EMAIL_DOMAINS.contains(domain.toLowerCase());
        }
    }

    /**
     * 综合验证邮箱是否有效
     * @param email 邮箱地址
     * @return 邮箱是否有效
     */
    public static boolean isValidEmail(String email) {
        // 验证格式
        if (!isValidFormat(email)) {
            return false;
        }

        // 验证域名是否存在
        if (!isDomainExists(email)) {
            return false;
        }

        // 验证邮箱地址是否存在
        return isEmailExists(email);
    }
}
