-- ----------------------------
-- 敏感词表
-- ----------------------------
DROP TABLE IF EXISTS `sys_sensitive_word`;
CREATE TABLE `sys_sensitive_word` (
  `word_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `type` char(1) NOT NULL DEFAULT '5' COMMENT '敏感词类型（1-政治，2-色情，3-暴力，4-广告，5-其他）',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0-正常，1-停用）',
  `is_specified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否指定（0-否，1-是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`word_id`),
  UNIQUE KEY `idx_word` (`word`) COMMENT '敏感词唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- ----------------------------
-- 初始化敏感词数据
-- ----------------------------
INSERT INTO `sys_sensitive_word` (`word`, `type`, `status`, `is_specified`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('赌博', '5', '0', 0, 'admin', NOW(), '', NULL, '敏感词示例');
INSERT INTO `sys_sensitive_word` (`word`, `type`, `status`, `is_specified`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('色情', '2', '0', 0, 'admin', NOW(), '', NULL, '敏感词示例');
INSERT INTO `sys_sensitive_word` (`word`, `type`, `status`, `is_specified`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('广告', '4', '0', 0, 'admin', NOW(), '', NULL, '敏感词示例');
