-- ----------------------------
-- 论坛核心表
-- ----------------------------

-- 轮播图表
DROP TABLE IF EXISTS forum_carousel;
CREATE TABLE forum_carousel (
    carousel_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
    title varchar(100) NOT NULL COMMENT '标题',
    image_url varchar(255) NOT NULL COMMENT '图片URL',
    click_url varchar(255) DEFAULT NULL COMMENT '点击跳转URL',
    sort int(11) DEFAULT 0 COMMENT '排序',
    status char(1) DEFAULT '0' COMMENT '状态（0显示 1隐藏）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (carousel_id)
) ENGINE=InnoDB COMMENT='轮播图表';

-- 帖子分类表
DROP TABLE IF EXISTS forum_post_category;
CREATE TABLE forum_post_category (
    category_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    category_name varchar(50) NOT NULL COMMENT '分类名称',
    sort int(11) DEFAULT 0 COMMENT '排序',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (category_id)
) ENGINE=InnoDB COMMENT='帖子分类表';

-- 帖子表
DROP TABLE IF EXISTS forum_post;
CREATE TABLE forum_post (
    post_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '帖子ID',
    category_id bigint(20) DEFAULT NULL COMMENT '分类ID',
    title varchar(100) NOT NULL COMMENT '标题',
    summary varchar(200) DEFAULT NULL COMMENT '摘要',
    cover_image varchar(255) DEFAULT NULL COMMENT '封面图',
    content longtext COMMENT '内容（富文本）',
    view_count int(11) DEFAULT 0 COMMENT '浏览数',
    comment_count int(11) DEFAULT 0 COMMENT '评论数',
    like_count int(11) DEFAULT 0 COMMENT '点赞数',
    collect_count int(11) DEFAULT 0 COMMENT '收藏数',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1下线）',
    is_top char(1) DEFAULT '0' COMMENT '是否置顶（0否 1是）',
    sort int(11) DEFAULT 0 COMMENT '排序',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (post_id),
    KEY idx_category_id (category_id)
) ENGINE=InnoDB COMMENT='帖子表';

-- 帖子附件表
DROP TABLE IF EXISTS forum_post_attachment;
CREATE TABLE forum_post_attachment (
    attachment_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    post_id bigint(20) NOT NULL COMMENT '帖子ID',
    file_name varchar(100) DEFAULT NULL COMMENT '文件名称',
    file_url varchar(255) NOT NULL COMMENT '文件URL',
    file_size bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
    file_type varchar(50) DEFAULT NULL COMMENT '文件类型',
    create_time datetime DEFAULT NULL COMMENT '上传时间',
    PRIMARY KEY (attachment_id),
    KEY idx_post_id (post_id)
) ENGINE=InnoDB COMMENT='帖子附件表';

-- 用户发帖表（我要说）
DROP TABLE IF EXISTS forum_user_post;
CREATE TABLE forum_user_post (
    user_post_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    content text NOT NULL COMMENT '内容',
    view_count int(11) DEFAULT 0 COMMENT '浏览数',
    comment_count int(11) DEFAULT 0 COMMENT '评论数',
    like_count int(11) DEFAULT 0 COMMENT '点赞数',
    status char(1) DEFAULT '0' COMMENT '状态（0显示 1隐藏）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (user_post_id),
    KEY idx_user_id (user_id)
) ENGINE=InnoDB COMMENT='用户发帖表';

-- 用户发帖图片表
DROP TABLE IF EXISTS forum_user_post_image;
CREATE TABLE forum_user_post_image (
    image_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '图片ID',
    user_post_id bigint(20) NOT NULL COMMENT '用户发帖ID',
    image_url varchar(255) NOT NULL COMMENT '图片URL',
    sort int(11) DEFAULT 0 COMMENT '排序',
    create_time datetime DEFAULT NULL COMMENT '上传时间',
    PRIMARY KEY (image_id),
    KEY idx_user_post_id (user_post_id)
) ENGINE=InnoDB COMMENT='用户发帖图片表';

-- 评论表
DROP TABLE IF EXISTS forum_comment;
CREATE TABLE forum_comment (
    comment_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    content_type char(1) NOT NULL COMMENT '内容类型（0帖子 1用户发帖）',
    content_id bigint(20) NOT NULL COMMENT '内容ID',
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    parent_id bigint(20) DEFAULT 0 COMMENT '父评论ID（0表示一级评论）',
    content text NOT NULL COMMENT '评论内容',
    like_count int(11) DEFAULT 0 COMMENT '点赞数',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (comment_id),
    KEY idx_content (content_type, content_id),
    KEY idx_user_id (user_id)
) ENGINE=InnoDB COMMENT='评论表';

-- 点赞表
DROP TABLE IF EXISTS forum_like;
CREATE TABLE forum_like (
    like_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
    content_type char(1) NOT NULL COMMENT '内容类型（0帖子 1用户发帖 2评论）',
    content_id bigint(20) NOT NULL COMMENT '内容ID',
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (like_id),
    UNIQUE KEY uk_content_user (content_type, content_id, user_id)
) ENGINE=InnoDB COMMENT='点赞表';

-- 收藏表
DROP TABLE IF EXISTS forum_collect;
CREATE TABLE forum_collect (
    collect_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
    content_type char(1) NOT NULL COMMENT '内容类型（0帖子 1用户发帖）',
    content_id bigint(20) NOT NULL COMMENT '内容ID',
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (collect_id),
    UNIQUE KEY uk_content_user (content_type, content_id, user_id)
) ENGINE=InnoDB COMMENT='收藏表';

-- 用户扩展表
DROP TABLE IF EXISTS forum_user_extend;
CREATE TABLE forum_user_extend (
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    nickname varchar(50) DEFAULT NULL COMMENT '昵称',
    avatar varchar(255) DEFAULT NULL COMMENT '头像URL',
    bio varchar(200) DEFAULT NULL COMMENT '个人简介',
    post_count int(11) DEFAULT 0 COMMENT '发帖数',
    comment_count int(11) DEFAULT 0 COMMENT '评论数',
    like_count int(11) DEFAULT 0 COMMENT '获赞数',
    collect_count int(11) DEFAULT 0 COMMENT '收藏数',
    is_banned_post char(1) DEFAULT '0' COMMENT '是否禁止发帖（0否 1是）',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (user_id)
) ENGINE=InnoDB COMMENT='用户扩展表';


-- 语言类型表
DROP TABLE IF EXISTS forum_language;
CREATE TABLE forum_language (
    lang_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '语言ID',
    lang_code varchar(10) NOT NULL COMMENT '语言代码(如zh-CN, en-US)',
    lang_name varchar(50) NOT NULL COMMENT '语言名称',
    sort int(11) DEFAULT 0 COMMENT '排序',
    status char(1) DEFAULT '0' COMMENT '状态（0启用 1停用）',
    is_default char(1) DEFAULT '0' COMMENT '是否默认语言（0否 1是）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (lang_id),
    UNIQUE KEY uk_lang_code (lang_code)
) ENGINE=InnoDB COMMENT='语言类型表';

-- 多语言帖子内容表
DROP TABLE IF EXISTS forum_post_i18n_content;
CREATE TABLE forum_post_i18n_content (
    content_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容ID',
    post_id bigint(20) NOT NULL COMMENT '帖子ID',
    lang_id bigint(20) NOT NULL COMMENT '语言ID',
    title varchar(100) NOT NULL COMMENT '标题',
    summary varchar(200) DEFAULT NULL COMMENT '摘要',
    content longtext COMMENT '内容（富文本）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (content_id),
    UNIQUE KEY uk_post_lang (post_id, lang_id),
    KEY idx_lang_id (lang_id)
) ENGINE=InnoDB COMMENT='多语言帖子内容表';

-- 初始化数据 (DML)
INSERT INTO sys_dict_type VALUES(11, '轮播图状态', 'forum_carousel_status', '0', 'admin', sysdate(), '', null, '轮播图状态列表');
INSERT INTO sys_dict_data VALUES(30, 1, '显示', '0', 'forum_carousel_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '显示轮播图');
INSERT INTO sys_dict_data VALUES(31, 2, '隐藏', '1', 'forum_carousel_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '隐藏轮播图');

INSERT INTO sys_dict_type VALUES(12, '帖子状态', 'forum_post_status', '0', 'admin', sysdate(), '', null, '帖子状态列表');
INSERT INTO sys_dict_data VALUES(32, 1, '正常', '0', 'forum_post_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '正常状态');
INSERT INTO sys_dict_data VALUES(33, 2, '下线', '1', 'forum_post_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '下线状态');

INSERT INTO sys_dict_type VALUES(13, '用户发帖状态', 'forum_user_post_status', '0', 'admin', sysdate(), '', null, '用户发帖状态列表');
INSERT INTO sys_dict_data VALUES(34, 1, '显示', '0', 'forum_user_post_status', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '显示状态');
INSERT INTO sys_dict_data VALUES(35, 2, '隐藏', '1', 'forum_user_post_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '隐藏状态');

INSERT INTO sys_dict_type VALUES(14, '内容类型', 'forum_content_type', '0', 'admin', sysdate(), '', null, '内容类型列表');
INSERT INTO sys_dict_data VALUES(36, 1, '帖子', '0', 'forum_content_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '帖子内容');
INSERT INTO sys_dict_data VALUES(37, 2, '用户发帖', '1', 'forum_content_type', '', '', 'N', '0', 'admin', sysdate(), '', null, '用户发帖内容');
INSERT INTO sys_dict_data VALUES(38, 3, '评论', '2', 'forum_content_type', '', '', 'N', '0', 'admin', sysdate(), '', null, '评论内容');


ALTER TABLE forum_user_extend
    ADD COLUMN email VARCHAR(100) NOT NULL COMMENT '邮箱',
ADD COLUMN email_verified TINYINT(1) DEFAULT 0 COMMENT '邮箱是否验证(0否 1是)',
ADD UNIQUE INDEX uk_email (email);

-- 帖子标签表
DROP TABLE IF EXISTS `forum_tag`;
CREATE TABLE `forum_tag` (
    `tag_id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `sort` int NULL DEFAULT 0 COMMENT '排序',
    `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '帖子标签表' ROW_FORMAT = Dynamic;

-- 标签多语言内容表
DROP TABLE IF EXISTS `forum_tag_i18n`;
CREATE TABLE `forum_tag_i18n` (
    `i18n_id` bigint NOT NULL AUTO_INCREMENT COMMENT '多语言ID',
    `tag_id` bigint NOT NULL COMMENT '标签ID',
    `lang_id` bigint NOT NULL COMMENT '语言ID',
    `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标签名称',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`i18n_id`) USING BTREE,
    UNIQUE INDEX `uk_tag_lang`(`tag_id`, `lang_id`) USING BTREE,
    INDEX `idx_lang_id`(`lang_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '标签多语言内容表' ROW_FORMAT = Dynamic;

-- 帖子标签关联表
DROP TABLE IF EXISTS `forum_post_tag`;
CREATE TABLE `forum_post_tag` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `post_id` bigint NOT NULL COMMENT '帖子ID',
    `tag_id` bigint NOT NULL COMMENT '标签ID',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_post_tag`(`post_id`, `tag_id`) USING BTREE,
    INDEX `idx_tag_id`(`tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '帖子标签关联表' ROW_FORMAT = Dynamic;

-- 分类多语言内容表
DROP TABLE IF EXISTS `forum_category_i18n`;
CREATE TABLE `forum_category_i18n` (
    `i18n_id` bigint NOT NULL AUTO_INCREMENT COMMENT '多语言ID',
    `category_id` bigint NOT NULL COMMENT '分类ID',
    `lang_id` bigint NOT NULL COMMENT '语言ID',
    `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分类名称',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`i18n_id`) USING BTREE,
    UNIQUE INDEX `uk_category_lang`(`category_id`, `lang_id`) USING BTREE,
    INDEX `idx_lang_id`(`lang_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分类多语言内容表' ROW_FORMAT = Dynamic;

-- 在帖子表增加状态字段
ALTER TABLE `forum_post`
    ADD COLUMN `post_status` char(1) NULL DEFAULT '1' COMMENT '帖子状态（0草稿 1已发布 2待审核 3审核不通过）' AFTER `status`;

-- Minio文件存储表
DROP TABLE IF EXISTS `sys_oss`;
CREATE TABLE `sys_oss` (
    `oss_id` bigint NOT NULL AUTO_INCREMENT COMMENT '对象存储ID',
    `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '文件名',
    `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '原始文件名',
    `file_suffix` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '文件后缀名',
    `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'URL地址',
    `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '文件路径',
    `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小（字节）',
    `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT 'MIME类型',
    `service_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'minio' COMMENT '服务商（minio/aliyun/qiniu等）',
    `biz_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '业务类型',
    `biz_id` bigint NULL DEFAULT NULL COMMENT '业务ID',
    `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '上传者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`oss_id`) USING BTREE,
    INDEX `idx_biz` (`biz_type`, `biz_id`) USING BTREE,
    INDEX `idx_url` (`file_url`(191)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OSS对象存储表';

-- 用户发帖图片表（原forum_user_post_image需要修改）
ALTER TABLE `forum_user_post_image`
    ADD COLUMN `oss_id` bigint NULL COMMENT 'OSS对象存储ID' AFTER `image_id`;

ALTER TABLE forum_user_post
    ADD COLUMN category_id bigint NULL COMMENT '分类ID' AFTER user_id;

ALTER TABLE forum_post ADD COLUMN share_count INT DEFAULT 0 COMMENT '分享次数';
