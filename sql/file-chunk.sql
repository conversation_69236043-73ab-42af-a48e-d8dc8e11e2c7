-- ----------------------------
-- 文件分片表
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_chunk`;
CREATE TABLE `sys_file_chunk` (
    `chunk_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分片ID',
    `file_identifier` varchar(100) NOT NULL COMMENT '文件唯一标识',
    `chunk_number` int NOT NULL COMMENT '分片编号',
    `chunk_size` bigint NOT NULL COMMENT '分片大小（字节）',
    `total_chunks` int NOT NULL COMMENT '总分片数',
    `total_size` bigint NOT NULL COMMENT '文件总大小（字节）',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `chunk_path` varchar(500) NOT NULL COMMENT '分片在MinIO中的路径',
    `biz_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0：上传中，1：已完成）',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`chunk_id`),
    UNIQUE KEY `uk_identifier_chunk` (`file_identifier`, `chunk_number`),
    KEY `idx_file_identifier` (`file_identifier`)
) ENGINE=InnoDB COMMENT='文件分片表';

-- ----------------------------
-- 文件上传记录表
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_upload`;
CREATE TABLE `sys_file_upload` (
    `upload_id` bigint NOT NULL AUTO_INCREMENT COMMENT '上传ID',
    `file_identifier` varchar(100) NOT NULL COMMENT '文件唯一标识',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `file_path` varchar(500) NOT NULL COMMENT '文件在MinIO中的路径',
    `file_url` varchar(500) NOT NULL COMMENT '文件访问URL',
    `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
    `chunk_total` int NOT NULL COMMENT '总分片数',
    `biz_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0：上传中，1：已完成）',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`upload_id`),
    UNIQUE KEY `uk_file_identifier` (`file_identifier`),
    KEY `idx_biz_type` (`biz_type`)
) ENGINE=InnoDB COMMENT='文件上传记录表';
