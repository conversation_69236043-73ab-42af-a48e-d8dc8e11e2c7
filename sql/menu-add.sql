-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ���', '2000', '1', 'postCategory', 'forum/postCategory/index', 1, 0, 'C', '0', '0', 'forum:postCategory:list', '#', 'admin', sysdate(), '', null, '���ӷ���˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ����ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:postCategory:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ�������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:postCategory:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ����޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:postCategory:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ���ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:postCategory:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӷ��ർ��', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:postCategory:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ���', '2000', '1', 'postAttachment', 'forum/postAttachment/index', 1, 0, 'C', '0', '0', 'forum:postAttachment:list', '#', 'admin', sysdate(), '', null, '���Ӹ����˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ�����ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:postAttachment:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ�������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:postAttachment:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ����޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:postAttachment:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ���ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:postAttachment:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӹ�������', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:postAttachment:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����', '2000', '1', 'like', 'forum/like/index', 1, 0, 'C', '0', '0', 'forum:like:list', '#', 'admin', sysdate(), '', null, '���޲˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���޲�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:like:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:like:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�����޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:like:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:like:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���޵���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:like:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����', '2000', '1', 'comment', 'forum/comment/index', 1, 0, 'C', '0', '0', 'forum:comment:list', '#', 'admin', sysdate(), '', null, '���۲˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���۲�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:comment:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:comment:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�����޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:comment:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:comment:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���۵���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:comment:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղ�', '2000', '1', 'collect', 'forum/collect/index', 1, 0, 'C', '0', '0', 'forum:collect:list', '#', 'admin', sysdate(), '', null, '�ղز˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղز�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:collect:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղ�����', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:collect:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղ��޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:collect:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղ�ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:collect:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ղص���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:collect:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼ', '2000', '1', 'carousel', 'forum/carousel/index', 1, 0, 'C', '0', '0', 'forum:carousel:list', '#', 'admin', sysdate(), '', null, '�ֲ�ͼ�˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼ��ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:carousel:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼ����', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:carousel:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼ�޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:carousel:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:carousel:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�ֲ�ͼ����', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:carousel:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����', '2000', '1', 'userPost', 'forum/userPost/index', 1, 0, 'C', '0', '0', 'forum:userPost:list', '#', 'admin', sysdate(), '', null, '�û������˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�������ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPost:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û���������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPost:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û������޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPost:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPost:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û���������', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPost:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬ', '2000', '1', 'userPostImage', 'forum/userPostImage/index', 1, 0, 'C', '0', '0', 'forum:userPostImage:list', '#', 'admin', sysdate(), '', null, '�û�����ͼƬ�˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬ��ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPostImage:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬ����', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPostImage:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬ�޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPostImage:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPostImage:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ͼƬ����', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:userPostImage:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����', '2000', '1', 'userExtend', 'forum/userExtend/index', 1, 0, 'C', '0', '0', 'forum:userExtend:list', '#', 'admin', sysdate(), '', null, '�û�����˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û������ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:userExtend:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û���������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:userExtend:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û������޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:userExtend:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�����ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:userExtend:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�û�������', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:userExtend:export',       '#', 'admin', sysdate(), '', null, '');

-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����', '2000', '1', 'post', 'forum/post/index', 1, 0, 'C', '0', '0', 'forum:post:list', '#', 'admin', sysdate(), '', null, '���Ӳ˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���Ӳ�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:post:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:post:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�����޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:post:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('����ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:post:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���ӵ���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:post:export',       '#', 'admin', sysdate(), '', null, '');


-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������', '2000', '1', 'language', 'forum/language/index', 1, 0, 'C', '0', '0', 'forum:language:list', '#', 'admin', sysdate(), '', null, '�������Ͳ˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�������Ͳ�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:language:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('������������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:language:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���������޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:language:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:language:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�������͵���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:language:export',       '#', 'admin', sysdate(), '', null, '');


-- �˵� SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������������', '2000', '1', 'postI18nContent', 'forum/postI18nContent/index', 1, 0, 'C', '0', '0', 'forum:postI18nContent:list', '#', 'admin', sysdate(), '', null, '�������������ݲ˵�');

-- ��ť���˵�ID
SELECT @parentId := LAST_INSERT_ID();

-- ��ť SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�������������ݲ�ѯ', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'forum:postI18nContent:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('������������������', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'forum:postI18nContent:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('���������������޸�', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'forum:postI18nContent:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('��������������ɾ��', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'forum:postI18nContent:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('�������������ݵ���', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'forum:postI18nContent:export',       '#', 'admin', sysdate(), '', null, '');
