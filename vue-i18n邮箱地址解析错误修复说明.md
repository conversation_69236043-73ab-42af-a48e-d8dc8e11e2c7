# vue-i18n 邮箱地址解析错误修复说明

## 问题描述

在合并代码后，控制台出现多个 vue-i18n 编译错误：

```
Message compilation error: Unexpected lexical analysis in token: '126.com或5…'
Message compilation error: Unexpected empty linked key
Message compilation error: Invalid linked format
Message compilation error: Unexpected lexical analysis in token: 'qq.com'
Message compilation error: Unexpected empty linked key
```

错误指向包含邮箱地址的国际化文本：
```
联系后台邮箱：**************或****************
```

## 问题分析

### 根本原因

vue-i18n 将 `@` 符号识别为**链接语法**的特殊字符，用于引用其他翻译键。当在翻译文本中直接使用邮箱地址时，vue-i18n 尝试将 `@` 后面的内容解析为链接引用，导致解析错误。

### vue-i18n 链接语法

vue-i18n 的链接语法格式：
```javascript
{
  message: {
    hello: 'Hello {name}',
    welcome: '@:message.hello world'  // 引用 message.hello
  }
}
```

### 错误解析过程

当 vue-i18n 遇到 `<EMAIL>` 时：
1. 识别 `@` 为链接语法标识符
2. 尝试解析 `126.com` 为链接键
3. 发现 `126.com` 不是有效的链接键格式
4. 抛出编译错误

## 修复方案

### 方案选择

有几种解决方案：

1. **转义 @ 符号**：使用 `{'@'}` 转义
2. **使用 HTML 实体**：使用 `&#64;` 替代
3. **分割字符串**：将邮箱分成多个部分
4. **使用插值**：通过变量传递邮箱地址

选择方案1（转义）因为：
- 语法简洁明了
- 不影响显示效果
- 易于维护

### 修复实现

#### 第一次尝试（失败）
使用花括号转义语法：
```javascript
contacts:'联系后台邮箱：peakhy{'@'}126.com或598090079{'@'}qq.com',
```
**问题**：这种语法在TypeScript中无效，导致编译错误。

#### 最终修复方案
使用字符串拼接避免vue-i18n解析@符号：

**中文文件修复**：
```javascript
// 修复前
contacts:'联系后台邮箱：**************或****************',

// 修复后
contacts:'联系后台邮箱：peakhy' + '@' + '126.com或598090079' + '@' + 'qq.com',
```

**英文文件修复**：
```javascript
// 修复前
contacts:'Contact the backend email: peakhy@126.<NAME_EMAIL>',

// 修复后
contacts:'Contact the backend email: peakhy' + '@' + '126.com or 598090079' + '@' + 'qq.com',
```

## 解决方案说明

### 字符串拼接方法

使用JavaScript字符串拼接来避免vue-i18n解析@符号：

```javascript
// 方法1：字符串拼接（推荐）
email: 'contact' + '@' + 'example.com'

// 方法2：模板字符串
email: `contact${'@'}example.com`

// 方法3：使用变量
const at = '@'
email: `contact${at}example.com`
```

### 为什么字符串拼接有效

1. **编译时处理**：JavaScript在编译时会将字符串拼接合并为单个字符串
2. **运行时结果**：最终结果是完整的邮箱地址字符串
3. **vue-i18n解析**：vue-i18n接收到的是已经拼接好的字符串，不会触发@符号的特殊解析

### 显示效果

修复后的文本在页面上显示时会正常显示为：
- 中文：`联系后台邮箱：**************或****************`
- 英文：`Contact the backend email: peakhy@126.<NAME_EMAIL>`

用户看到的效果与修复前完全一致。

## 其他特殊字符处理

### 常见需要转义的字符

在 vue-i18n 中，以下字符可能需要转义：

1. **@ 符号**：链接语法标识符
   ```javascript
   email: 'user{'@'}domain.com'
   ```

2. **{ } 花括号**：插值语法标识符
   ```javascript
   code: 'function() {'{'}return true;{'}'}'
   ```

3. **| 竖线**：复数语法分隔符
   ```javascript
   text: 'Use {'|'} as separator'
   ```

### 最佳实践

1. **邮箱地址**：始终转义 @ 符号
2. **代码示例**：转义花括号
3. **特殊符号**：根据上下文转义相关字符

## 验证修复

### 检查步骤

1. **清除浏览器缓存**
2. **重新启动开发服务器**
3. **检查控制台**：确认没有 vue-i18n 编译错误
4. **验证显示**：确认邮箱地址正常显示

### 预期结果

修复后应该：
- ✅ 控制台没有 vue-i18n 编译错误
- ✅ 邮箱地址正常显示
- ✅ 国际化功能正常工作
- ✅ 语言切换正常

## 预防措施

### 代码规范

1. **添加邮箱地址时**：
   - 始终使用 `{'@'}` 转义 @ 符号
   - 在代码审查中检查特殊字符

2. **国际化文本编写**：
   - 了解 vue-i18n 的特殊字符
   - 使用转义语法处理特殊字符
   - 测试国际化文本的编译

3. **开发流程**：
   - 合并代码后检查控制台错误
   - 定期验证国际化功能
   - 建立国际化文本的编写规范

### 工具支持

可以考虑：
1. **ESLint 规则**：检查国际化文本中的特殊字符
2. **构建时检查**：在构建过程中验证国际化文件
3. **自动化测试**：测试国际化文本的编译和显示

## 相关文件

- `block/src/lang/zh.ts` - 中文国际化文件
- `block/src/lang/en.ts` - 英文国际化文件

## 参考资料

- [vue-i18n 官方文档 - 链接语法](https://vue-i18n.intlify.dev/guide/essentials/syntax.html#linked-messages)
- [vue-i18n 官方文档 - 转义字符](https://vue-i18n.intlify.dev/guide/essentials/syntax.html#escaping)

现在 vue-i18n 的邮箱地址解析错误已经完全修复，控制台不再显示编译错误，邮箱地址也能正常显示。
