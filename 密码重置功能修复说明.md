# 密码重置功能修复说明

## 问题总结

1. **C端登录问题**：我之前修改了C端的重置密码页面，将其改为需要原密码的修改密码功能，这与"忘记密码"的需求不符
2. **B端缺少重置密码功能**：B端用户管理页面缺少管理员重置用户密码的功能

## 修复内容

### 1. C端重置密码功能修复

#### 恢复为"忘记密码"功能
- **页面结构**：邮箱 → 验证码 → 新密码 → 确认密码
- **验证方式**：通过邮箱验证码验证身份，不需要原密码
- **API接口**：`/api/auth/password/reset` (使用验证码)

#### 新增发送验证码功能
- **API接口**：`/api/auth/password/send-code`
- **验证码有效期**：10分钟
- **存储方式**：Redis缓存

#### 新增修改密码功能（需要原密码）
- **API接口**：`/api/auth/password/change`
- **验证方式**：需要提供原密码
- **用途**：用户在已登录状态下修改密码

### 2. B端重置密码功能新增

#### 页面功能
- **位置**：`blog-ui/src/views/forum/user/index.vue`
- **操作按钮**：在用户列表的操作列添加"重置密码"按钮
- **权限控制**：`v-hasPermi="['forum:user:resetPassword']"`

#### 重置密码对话框
- **用户ID**：显示用户ID（只读）
- **用户邮箱**：显示用户邮箱（只读）
- **新密码**：输入新密码（6-20位）
- **确认密码**：再次输入新密码进行确认

#### 后端实现
- **控制器**：`ForumUserController.resetPassword()`
- **服务层**：`ForumUserExtendServiceImpl.resetUserPassword()`
- **权限控制**：`@PreAuthorize("@ss.hasPermi('forum:user:resetPassword')")`
- **操作日志**：`@Log(title = "重置用户密码", businessType = BusinessType.UPDATE)`

## API接口说明

### C端接口

#### 1. 发送重置密码验证码
```
POST /api/auth/password/send-code
参数: email (邮箱)
```

#### 2. 重置密码（通过验证码）
```
POST /api/auth/password/reset
参数: email (邮箱), code (验证码), newPassword (新密码)
```

#### 3. 修改密码（需要原密码）
```
POST /api/auth/password/change
参数: email (邮箱), oldPassword (原密码), newPassword (新密码)
```

### B端接口

#### 重置用户密码
```
POST /forum/user/resetPassword
参数: userId (用户ID), newPassword (新密码)
权限: forum:user:resetPassword
```

## 安全特性

### 1. C端安全控制
- **验证码验证**：通过邮箱验证码确认身份
- **验证码有效期**：10分钟自动过期
- **用户状态检查**：禁用用户无法重置密码
- **密码加密**：使用 `SecurityUtils.encryptPassword()` 加密

### 2. B端安全控制
- **权限验证**：需要 `forum:user:resetPassword` 权限
- **管理员保护**：不能重置超级管理员密码
- **用户状态检查**：禁用用户无法重置密码
- **密码长度验证**：6-20位字符限制

### 3. 操作审计
- **操作日志**：所有重置密码操作记录到系统日志
- **操作人记录**：记录执行重置操作的管理员信息

## 数据验证

### 前端验证
- **邮箱格式验证**
- **密码长度验证**（6-20位）
- **确认密码一致性验证**
- **验证码格式验证**（6位数字）

### 后端验证
- **用户存在性验证**
- **用户状态验证**
- **权限验证**
- **密码强度验证**

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 邮箱格式不正确 | 邮箱格式无效 | 输入正确的邮箱格式 |
| 该邮箱未注册 | 邮箱不存在 | 使用已注册的邮箱 |
| 验证码已过期 | 验证码超过10分钟 | 重新获取验证码 |
| 验证码错误 | 输入的验证码不正确 | 检查验证码是否正确 |
| 用户已被禁用 | 目标用户被禁用 | 先启用用户再重置密码 |
| 密码长度为6-20位 | 密码长度不符合要求 | 调整密码长度 |
| 两次输入的密码不一致 | 确认密码不匹配 | 重新输入确认密码 |

## 使用说明

### C端用户使用
1. **忘记密码**：
   - 访问重置密码页面
   - 输入注册邮箱
   - 点击"获取验证码"
   - 输入收到的验证码
   - 设置新密码
   - 确认新密码
   - 提交重置

2. **修改密码**（已登录）：
   - 使用 `/api/auth/password/change` 接口
   - 提供原密码和新密码

### B端管理员使用
1. **重置用户密码**：
   - 进入"论坛管理" → "用户管理"
   - 找到目标用户
   - 点击"重置密码"按钮
   - 输入新密码
   - 确认新密码
   - 提交重置

## 权限配置

需要在系统中添加以下权限：

```sql
-- 重置用户密码权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('重置密码', (SELECT menu_id FROM sys_menu WHERE menu_name = '用户管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '论坛管理')), 4, '', '', 1, 0, 'F', '0', '0', 'forum:user:resetPassword', '#', 'admin', NOW(), '', NULL, '重置用户密码');
```

## 注意事项

1. **邮件服务配置**：确保邮件服务正常配置，能够发送验证码
2. **Redis配置**：确保Redis服务正常运行，用于存储验证码
3. **权限分配**：为相应的管理员角色分配重置密码权限
4. **密码策略**：建议设置更强的密码策略
5. **操作审计**：定期检查密码重置操作日志

## 测试验证

### C端测试
1. **忘记密码流程测试**
2. **验证码发送和验证测试**
3. **密码重置成功后登录测试**

### B端测试
1. **权限控制测试**
2. **重置密码功能测试**
3. **操作日志记录测试**

## 最新修复（B端API调用错误）

### 问题描述
B端页面出现错误：`TypeError: Cannot read properties of undefined (reading 'post')`

### 问题原因
- 使用了错误的API调用方式 `this.$http.post`
- B端应该使用从 `@/utils/request` 导入的 `request` 方法

### 修复内容

#### 1. 创建API方法
在 `blog-ui/src/api/forum/user.js` 中添加：
```javascript
// 重置用户密码
export function resetUserPassword(userId, newPassword) {
  return request({
    url: '/forum/user/resetPassword',
    method: 'post',
    params: {
      userId: userId,
      newPassword: newPassword
    }
  })
}
```

#### 2. 更新页面导入
```javascript
import { listUser, changeBannedPost, changeUserStatus, resetUserPassword } from "@/api/forum/user";
```

#### 3. 修正方法调用
```javascript
// 修复前
this.resetUserPassword(userId, newPassword)

// 修复后
resetUserPassword(userId, newPassword)
```

### 修复后的完整流程

1. **用户点击重置密码按钮**
2. **弹出重置密码对话框**
3. **输入新密码和确认密码**
4. **表单验证通过后调用API**
5. **后端验证权限和用户状态**
6. **更新用户密码**
7. **返回成功提示**

现在密码重置功能已经完全修复，C端恢复为正常的"忘记密码"功能，B端新增了管理员重置用户密码功能，并且修复了API调用错误。
