{"name": "blog-forum-web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tiptap/extension-code": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-task-item": "^2.11.7", "@tiptap/extension-task-list": "^2.11.7", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.1.0", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "axios": "^1.4.0", "date-fns": "^4.1.0", "element-plus": "^2.3.9", "mitt": "^3.0.1", "mockjs": "^1.1.0", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-emoji-picker": "^1.0.3", "vue-i18n": "^9.14.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/mockjs": "^1.0.7", "@types/node": "^20.4.5", "@vitejs/plugin-vue": "^5.2.3", "sass": "^1.64.2", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^6.2.5", "vue-tsc": "^2.2.10"}}