# 图片手动调整大小功能实现说明

## 功能概述

实现了富文本编辑器中图片的手动调整大小功能：
1. 取消自动等比缩放功能，恢复之前的操作逻辑
2. 支持用户手动拖动调整图片大小
3. 只能在没有文字的行进行拖动缩放
4. 保持图片宽高比，提供良好的用户体验

## 核心功能

### 1. 图片基础样式恢复

**恢复到之前的样式**：
- `display: inline-block` - 内联块显示
- `max-width: 100%` - 最大宽度限制
- `height: auto` - 高度自适应
- `margin: 5px` - 适当间距
- `vertical-align: middle` - 垂直居中对齐

```javascript
// 设置图片基础样式
img.style.maxWidth = '100%'
img.style.height = 'auto'
img.style.display = 'inline-block'
img.style.margin = '5px'
img.style.cursor = 'pointer'
img.style.border = '1px solid #ddd'
img.style.borderRadius = '4px'
img.style.verticalAlign = 'middle'
```

### 2. 手动调整大小功能

**调整控制点**：
- 在图片右下角显示蓝色圆形控制点
- 鼠标悬停时显示，离开时隐藏
- 只有在没有文字的行才显示控制点
- 拖动控制点可以调整图片大小

**实现逻辑**：
```javascript
// 创建调整大小的控制点
const resizeHandle = document.createElement('div')
resizeHandle.style.cssText = `
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 10px;
  height: 10px;
  background: #409EFF;
  border: 1px solid #fff;
  border-radius: 50%;
  cursor: se-resize;
  display: none;
  z-index: 1000;
`
```

### 3. 文字检测机制

**检测逻辑**：
- 检查图片所在行是否有文字内容
- 遍历父元素的所有子节点
- 检测文本节点和元素节点的文字内容
- 移除所有空白字符后判断是否有实际文字

```javascript
canResizeImage(imgContainer) {
  const parent = imgContainer.parentNode
  const siblings = Array.from(parent.childNodes)
  
  for (let node of siblings) {
    if (node === imgContainer) continue
    
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.replace(/\s/g, '')
      if (text.length > 0) {
        return false // 有文字，不能调整
      }
    }
    // ... 其他检测逻辑
  }
  
  return true // 没有文字，可以调整
}
```

### 4. 拖动调整实现

**鼠标事件处理**：
```javascript
// 开始调整大小
resizeHandle.addEventListener('mousedown', (e) => {
  isResizing = true
  startX = e.clientX
  startY = e.clientY
  startWidth = parseInt(getComputedStyle(img).width, 10)
  startHeight = parseInt(getComputedStyle(img).height, 10)
  
  document.addEventListener('mousemove', doResize)
  document.addEventListener('mouseup', stopResize)
})

// 调整过程
const doResize = (e) => {
  const width = startWidth + e.clientX - startX
  
  // 限制最小和最大尺寸
  const minSize = 50
  const maxWidth = this.$refs.editorContent.clientWidth - 20
  
  if (width >= minSize && width <= maxWidth) {
    img.style.width = width + 'px'
    img.style.height = 'auto' // 保持宽高比
  }
}
```

## 技术实现

### 1. 图片容器包装

**容器结构**：
```html
<div class="image-resize-container">
  <img src="..." />
  <div class="image-resize-handle"></div>
</div>
```

**容器功能**：
- 提供相对定位基础
- 包含图片和调整控制点
- 管理鼠标事件
- 控制调整功能的显示/隐藏

### 2. 事件管理

**鼠标事件绑定**：
```javascript
// 鼠标进入显示控制点
imgContainer.addEventListener('mouseenter', () => {
  if (this.canResizeImage(imgContainer)) {
    resizeHandle.style.display = 'block'
  }
})

// 鼠标离开隐藏控制点
imgContainer.addEventListener('mouseleave', () => {
  if (!isResizing) {
    resizeHandle.style.display = 'none'
  }
})
```

### 3. 已有图片处理

**初始化处理**：
```javascript
setupImageFeatures() {
  const images = this.$refs.editorContent.querySelectorAll('img')
  images.forEach(img => {
    if (!img.parentNode.classList.contains('image-resize-container')) {
      // 重新设置基础样式
      // 添加点击预览事件
      // 添加可调整大小功能
      this.makeImageResizable(img)
    }
  })
}
```

## CSS样式设计

### 1. 图片基础样式

```scss
img {
  max-width: 100%;
  height: auto;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease;
  vertical-align: middle;

  &:hover {
    border-color: #409EFF;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
}
```

### 2. 调整控制点样式

```scss
.image-resize-container {
  position: relative;
  display: inline-block;
  margin: 5px;
  vertical-align: middle;

  .image-resize-handle {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 10px;
    height: 10px;
    background: #409EFF;
    border: 1px solid #fff;
    border-radius: 50%;
    cursor: se-resize;
    display: none;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    &:hover {
      background: #337ecc;
      transform: scale(1.2);
    }
  }

  // 当图片所在行有文字时，隐藏调整控制点
  &.has-text .image-resize-handle {
    display: none !important;
  }
}
```

## 功能特性

### ✅ 用户体验优化
- **直观的调整方式**：鼠标悬停显示调整控制点
- **智能显示控制**：只在没有文字的行显示调整功能
- **平滑的交互**：拖动过程流畅，实时反馈
- **视觉反馈**：控制点有悬停效果和缩放动画

### ✅ 功能限制
- **最小尺寸限制**：图片最小宽度50px
- **最大尺寸限制**：图片最大宽度为编辑器宽度减20px
- **宽高比保持**：调整宽度时自动调整高度保持比例
- **文字行限制**：有文字的行不显示调整控制点

### ✅ 兼容性保证
- **已有图片支持**：编辑时加载的图片也支持调整功能
- **内容更新处理**：watch监听确保新内容中的图片正确处理
- **事件清理**：避免重复绑定事件，防止内存泄漏

## 使用场景

### 1. 纯图片行
- ✅ 显示调整控制点
- ✅ 可以拖动调整大小
- ✅ 提供最佳的调整体验

### 2. 图文混排行
- ❌ 不显示调整控制点
- ❌ 不能拖动调整大小
- ✅ 保持原有的显示效果

### 3. 多图片行
- ✅ 每个图片都可以独立调整
- ✅ 不会相互影响
- ✅ 保持良好的布局

## 测试建议

### 1. 基础功能测试
- 上传图片，验证调整控制点显示
- 拖动控制点，验证大小调整功能
- 在图片旁边输入文字，验证控制点隐藏

### 2. 边界情况测试
- 调整到最小尺寸，验证限制生效
- 调整到最大尺寸，验证限制生效
- 快速拖动，验证响应性能

### 3. 兼容性测试
- 编辑已有帖子，验证图片调整功能
- 复制粘贴图片，验证功能正常
- 不同浏览器测试兼容性

## 注意事项

1. **性能考虑**：避免频繁的DOM操作，使用事件委托
2. **内存管理**：正确添加和移除事件监听器
3. **用户体验**：提供清晰的视觉反馈和操作提示
4. **功能边界**：明确什么情况下可以调整，什么情况下不能
5. **样式冲突**：确保新样式不会影响其他功能

## 扩展功能建议

1. **多方向调整**：支持四个方向的调整控制点
2. **比例锁定**：提供锁定宽高比的选项
3. **快捷键支持**：支持键盘快捷键调整大小
4. **预设尺寸**：提供常用尺寸的快速选择
5. **调整历史**：支持撤销/重做调整操作
