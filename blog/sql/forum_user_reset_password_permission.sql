-- 添加用户管理重置密码权限
-- 插入重置密码权限菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('用户重置密码', (SELECT menu_id FROM sys_menu WHERE menu_name = '用户管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '论坛管理')), 4, '', '', '', 1, 0, 'F', '0', '0', 'forum:userExtend:resetPassword', '#', 'admin', sysdate(), '', null, '用户重置密码权限');

-- 为管理员角色分配重置密码权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms = 'forum:userExtend:resetPassword';
