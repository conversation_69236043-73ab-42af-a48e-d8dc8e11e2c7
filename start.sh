#!/bin/bash

# 设置Java环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 设置应用目录
APP_DIR=/data/blog-admin
JAR_FILE=$APP_DIR/blog-admin.jar

# 设置JVM参数
JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
# 设置字符编码相关参数
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.language=zh -Duser.region=CN"
# 设置日志编码
JAVA_OPTS="$JAVA_OPTS -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Dlogback.charset=UTF-8"

# 启动应用
echo "Starting application with UTF-8 encoding..."
nohup java $JAVA_OPTS -jar $JAR_FILE > /dev/null 2>&1 &

# 输出进程ID
echo $! > $APP_DIR/application.pid
echo "Application started with PID: $(cat $APP_DIR/application.pid)"
