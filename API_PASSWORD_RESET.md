# 密码重置API文档

## 接口变更说明

原来的密码重置功能使用验证码验证，现已改为使用密码验证的方式。

### 变更内容

1. **删除的接口**：
   - `POST /api/auth/password/code` - 发送密码重置验证码

2. **修改的接口**：
   - `POST /api/auth/password/reset` - 重置密码（改为密码验证）

## 新的密码重置接口

### 接口地址
```
POST /api/auth/password/reset
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | String | 是 | 用户邮箱 |
| oldPassword | String | 是 | 原密码 |
| newPassword | String | 是 | 新密码 |

### 请求示例
```json
{
    "email": "<EMAIL>",
    "oldPassword": "oldPassword123",
    "newPassword": "newPassword456"
}
```

### 响应示例

#### 成功响应
```json
{
    "code": 200,
    "msg": "密码修改成功",
    "data": null
}
```

#### 失败响应
```json
{
    "code": 500,
    "msg": "原密码错误",
    "data": null
}
```

### 错误码说明
| 错误信息 | 说明 |
|----------|------|
| 邮箱格式不正确 | 邮箱格式验证失败 |
| 该邮箱未注册 | 邮箱不存在 |
| 用户已被禁用 | 用户状态异常 |
| 原密码错误 | 原密码验证失败 |
| 新密码不能与原密码相同 | 新旧密码相同 |
| 密码修改失败 | 数据库更新失败 |

## 使用流程

1. 用户输入邮箱、原密码和新密码
2. 系统验证邮箱格式
3. 系统查找用户并验证状态
4. 系统验证原密码是否正确
5. 系统检查新密码是否与原密码相同
6. 系统更新用户密码
7. 返回操作结果

## 安全考虑

1. **密码加密**：所有密码都使用 `SecurityUtils.encryptPassword()` 进行加密
2. **用户状态检查**：禁用用户无法修改密码
3. **密码验证**：必须提供正确的原密码才能修改
4. **防重复**：新密码不能与原密码相同

## 前端调用示例

### JavaScript/Axios
```javascript
const resetPassword = async (email, oldPassword, newPassword) => {
    try {
        const response = await axios.post('/api/auth/password/reset', {
            email: email,
            oldPassword: oldPassword,
            newPassword: newPassword
        });
        
        if (response.data.code === 200) {
            console.log('密码修改成功');
            return true;
        } else {
            console.error('密码修改失败:', response.data.msg);
            return false;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return false;
    }
};
```

### Vue.js 示例
```vue
<template>
  <form @submit.prevent="handleSubmit">
    <input v-model="form.email" type="email" placeholder="邮箱" required />
    <input v-model="form.oldPassword" type="password" placeholder="原密码" required />
    <input v-model="form.newPassword" type="password" placeholder="新密码" required />
    <button type="submit" :disabled="loading">修改密码</button>
  </form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        email: '',
        oldPassword: '',
        newPassword: ''
      },
      loading: false
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true;
      try {
        const response = await this.$http.post('/api/auth/password/reset', this.form);
        if (response.code === 200) {
          this.$message.success('密码修改成功');
          // 可以跳转到登录页面
          this.$router.push('/login');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('修改失败，请重试');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>
```

## 注意事项

1. 此接口为公开接口，无需登录即可访问
2. 建议在前端添加密码强度验证
3. 建议添加密码确认输入框
4. 可以考虑添加频率限制防止暴力破解
